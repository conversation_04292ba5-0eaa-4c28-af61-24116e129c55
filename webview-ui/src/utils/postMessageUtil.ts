import { IChatType, QAItem } from "@shared/types/chatHistory";
import { IChatModel, IChatRecord } from "./sessionUtils";
import { CodeSection } from "@shared/types";
import { chatId } from "./chatId";
import { extractLinks } from "./utils";
import { ChatRequestType, fetchMessageAndSendWebView } from "@/http/api/chat";

// 转换数据元素
function transformRecordMessages(
  cachedSessionMessages: QAItem[],
  lastExpiredIndex: number,
  lastClearIndex: number,
): IChatRecord[] {
  const res: IChatRecord[] = [];
  const startIndex = Math.max(lastClearIndex, lastExpiredIndex);

  cachedSessionMessages.forEach((item, index) => {
    if (index > startIndex) {
      res.push({
        chatId: item.id,
        role: "user",
        content: item.Q.question || "",
        rag: !!item.Q.actionResults?.length,
      });
      res.push({
        chatId: item.id,
        role: "assistant",
        content: item.A[0].reply || "",
        rag: !!item.A[0].actionResults?.length,
      });
    }
  });
  return res;
}

/**
 * 优化历史记录 - 保证历史记录以一问一答的形式存储
 * @param chatRecords
 * @returns
 */
function optimizeChatRecords(chatRecords: IChatRecord[]) {
  if (!chatRecords) {
    return [];
  }
  let lastIsAssistant = false;
  const newChatRecords: IChatRecord[] = [];
  // 逆序遍历 chatRecords
  for (let i = chatRecords.length - 1; i >= 0; i--) {
    const record = chatRecords[i];
    // 最后一个不是回答，这一个是回答，添加进去
    if (record.role === "assistant" && !lastIsAssistant) {
      lastIsAssistant = true;
      newChatRecords.unshift(record);
    }
    // 最后是回答，这一个是问题，添加进去
    if (record.role === "user" && lastIsAssistant) {
      lastIsAssistant = false;
      newChatRecords.unshift(record);
    }
  }
  return newChatRecords;
}

/**
 * 开始对话，将消息传递给插件请求接口
 * @param param
 * sessionId: 活跃的对话ID
 */
export function postMessageUtil(param: {
  // MessageType
  type: string;
  content: string;
  // resend 会有 uniqueId
  uniqueId?: string;
  cachedMessages: QAItem[];
  sessionId: string;
  chatType: IChatType;
  useSearch: boolean;
  refFiles?: number[];
  chatModel: IChatModel;
  docId: number;
  section?: CodeSection;
  fullPath?: string;
  // 过期被清除的上下文
  expiredIndex: number[];
  // 用户主动清除的上下文
  clearContextIndex: number[];
  // 指定向哪个 tab 发送消息
  vendor: "composer" | "chat";
  startTime: number;
  rules?: string[];
}) {
  const {
    uniqueId: originUniqueIdId,
    cachedMessages,
    useSearch,
    docId,
    expiredIndex,
    clearContextIndex,
    vendor,
    startTime,
    ...rest
  } = param;
  // 消息对应的ID
  const uniqueId
    = originUniqueIdId
    || Date.now().toString(36) + Math.random().toString(36).substr(2);
  chatId.updateChatId(uniqueId);
  const chatRecords = transformRecordMessages(
    cachedMessages.filter(msg => msg.id !== uniqueId),
    expiredIndex.pop() ?? -1,
    clearContextIndex.pop() ?? -1,
  );

  const formatMessage = {
    formatQuestion: param.content,
    chatRecords: optimizeChatRecords(chatRecords).map(item => ({
      role: item.role,
      content: item.content,
    })),
    useSearch: useSearch,
    uniqueId,
    linkRepoId: `${docId ? docId : ""}`,
    refLinks: extractLinks(param.content),
    ...rest,
    beginTimestamp: +new Date(),
  };

  fetchMessageAndSendWebView({
    content: formatMessage.formatQuestion,
    uniqueId,
    rules: formatMessage.rules,
    chatRecords: formatMessage.chatRecords,
    chatType: "intelligentChat",
    sessionId: param.sessionId,
    isResend: !!originUniqueIdId,
    useSearch: param.useSearch,
    refFiles: rest.refFiles ?? [],
    chatModel: {
      modelType: param.chatModel.modelType ?? "kwaipilot_pro_32k",
    },
    linkRepoId: formatMessage.linkRepoId,
    beginTimestamp: +new Date(),
    vendor,
    requestType:
      vendor === "composer" ? ChatRequestType.composer : ChatRequestType.normal,
    startTime,
  });
}
