import { useVsEditorConfig } from "@/store/vsEditorConfig";
import { createHighlighter, type Highlighter } from "shiki";
import defaultDarkThem from "../../theme/output-default-dark.json";
import defaultLightThem from "../../theme/output-default-light.json";

let highlighterInstance: Highlighter | null = null;
export const getHighlighterInstance = () => {
  if (!highlighterInstance) {
    throw new Error("Highlighter instance not initialized");
  }
  return highlighterInstance;
};

export const initHighlighterInstance = async () => {
  highlighterInstance = await createHighlighter({
    themes: [defaultDarkThem as any, defaultLightThem as any],
    langs: [
      "javascript",
      "typescript",
      "python",
      "java",
      "html",
      "css",
      "scss",
      "less",
      "json",
      "jsonc",
      "yaml",
      "markdown",
      "vue",
      "vue-html",
      "xml",
      "php",
      "shellscript",
      "tsx",
      "jsx",
      "sql",
      "wasm",
      "ruby",
      "rust",
      "jsx",
      "tsx",
      "cpp",
      "cs",
      "txt",
      "pug",
      "docker",
      "javascript",
      "typescript",
      "txt",
      "sh",
    ],
  });
  if (window.__injectTheme__ && window.__injectTheme__.name && window.__injectTheme__.settings) {
    await highlighterInstance.loadTheme(window.__injectTheme__.settings);
    useVsEditorConfig.setState({
      currentThemeName: window.__injectTheme__.name,
    });
  }
};

/**
 * 提取 html 字符串中的背景颜色
 * @param str shiki 生成的 html 字符串
 * @returns 提取出的背景颜色
 */
export function extractBackgroundColor(str: string): string | null {
  const regex = /background-color:(#[0-9a-fA-F]{3,8}|[a-zA-Z]+)/;
  const match = str.match(regex);

  if (match) {
    return match[1];
  }

  return null;
}

export function addLineHeightToPreStyle(htmlString: string, lineHeight: number): string {
  const regex = /<pre\s+([^>]*style="[^"]*"[^>]*|[^>]*)>/g;

  return htmlString.replace(regex, (match, p1) => {
    if (p1.includes("style=\"")) {
      // 如果已经存在 style 属性
      return match.replace(/style="([^"]*)"/, (_styleMatch, styleContent) => {
        return `style="${styleContent};line-height:${lineHeight}px"`;
      });
    }
    else {
      // 如果不存在 style 属性
      return match.replace(">", ` style="line-height:${lineHeight}px">`);
    }
  });
}
