import { defineStyle } from "@chakra-ui/react";

export const MessageType = {
  THEME: "THEME",
  LOGIN: "LOGIN", // 登录
  SEND_MESSAGE: "SEND_MESSAGE", // 发送消息动作
  RECEIVE_MESSAGE: "RECEIVE_MESSAGE", // 接受消息动作
  LIKE_MESSAGE: "LIKE_MESSAGE", // 喜好消息行为
  RECEIVE_MESSAGE_DONE: "RECEIVE_MESSAGE_DONE", // 结束接收流式信息
  RECEIVE_MESSAGE_ERROR: "RECEIVE_MESSAGE_ERROR", // 接收信息流失败了
  STOP_RECEIVE_MESSAGE: "STOP_RECEIVE_MESSAGE", // 停止接收消息
  RECEIVE_FONTSIZE: "RECEIVE_FONTSIZE", // 收到字体设置
  RECEIVE_PLATFORMLIST: "RECEIVE_PLATFORMLIST", // 收到支持的平台
  RECEIVE_USERINFO: "RECEIVE_USERINFO", // 收到用户信息
  RECEIVE_MODEL_LIST: "RECEIVE_MODEL_LIST", // 收到模型列表
  GENERATE_COMMENT_MESSAGE: "GENERATE_COMMENT_MESSAGE", // 生成注释消息
  GENERATE_UNIT_TEST_MESSAGE: "GENERATE_UNIT_TEST_MESSAGE", // 生成单元测试消息
  GENERATE_CODE_OPTIMIZATION_MESSAGE: "GENERATE_CODE_OPTIMIZATION_MESSAGE", // 生成代码优化消息
  RECEIVE_COMMENT_MESSAGE: "RECEIVE_COMMENT_MESSAGE", // 接收生成注释消息
  RECEIVE_UNIT_TEST_MESSAGE: "RECEIVE_UNIT_TEST_MESSAGE", // 接收生成单元测试消息
  RECEIVE_CODE_OPTIMIZATION_MESSAGE: "RECEIVE_CODE_OPTIMIZATION_MESSAGE", // 接收代码优化消息
  RESEND_MESSAGE: "RESEND_MESSAGE", // 重新发送消息动作
  COPY_MESSAGE: "COPY_MESSAGE", // 问答复制动作
  SEND_GENERATING_COMMENT_MESSAGE: "SEND_GENERATING_COMMENT_MESSAGE", // 发送生成注释消息
  SEND_GENERATING_UNIT_MESSAGE: "SEND_GENERATING_UNIT_MESSAGE", // 发送生成单元测试消息
  SEND_GENERATING_EXPLAIN: "SEND_GENERATING_EXPLAIN", // 解释代码
  SEND_CODE_OPTIMIZATION_MESSAGE: "SEND_CODE_OPTIMIZATION_MESSAGE", // 发送代码优化消息
  UPLOAD_FILE: "UPLOAD_FILE", // 上传文件请求
  UPLOAD_FILE_START: "UPLOAD_FILE_START", // 上传文件请求
  UPLOAD_FILE_PROGRESS: "UPLOAD_FILE_PROGRESS", // 上传文件请求
  UPLOAD_FILE_SUCCESS: "UPLOAD_FILE_SUCCESS", // 上传文件请求
  UPLOAD_FILE_FAILED: "UPLOAD_FILE_FAILED", // 上传文件请求
  FILE_SIZE: "FILE_SIZE", // 上传文件请求
  GETDOCLIST: "GETDOCLIST", // 获取知识库列表
  GENERATING_EXPLAIN: "GENERATING_EXPLAIN",
  RECEIVE_EXPLAIN: "RECEIVE_EXPLAIN",

  VERSION: "VERSION", // 接收插件版本

  // codeActions
  RECEIVE_CODE_ACTION_MESSAGE: "RECEIVE_CODE_ACTION_MESSAGE", // 前端接受并展示codeBlockAction的内容
  // SEND前缀表示来自插件的消息，打开chat面板， 无前缀表示webview像服务端（插件）发送请求的消息
  SEND_GENERATING_FUNCTION_COMMENT: "SEND_GENERATING_FUNCTION_COMMENT",
  // 向插件发送生成消息记录的消息
  GENERATING_FUNCTION_COMMENT: "GENERATING_FUNCTION_COMMENT",
  SEND_GENERATING_LINE_COMMENT: "SEND_GENERATING_LINE_COMMENT",
  GENERATING_LINE_COMMENT: "GENERATING_LINE_COMMENT",
  SEND_GENERATING_CODE_EXPLANATION: "SEND_GENERATING_CODE_EXPLANATION",
  GENERATING_CODE_EXPLANATION: "GENERATING_CODE_EXPLANATION",
  SEND_GENERATING_TUNING_SUGGESTION: "SEND_GENERATING_TUNING_SUGGESTION",
  GENERATING_TUNING_SUGGESTION: "GENERATING_TUNING_SUGGESTION",
  SEND_GENERATING_FUNCTION_SPLIT: "SEND_GENERATING_FUNCTION_SPLIT",
  GENERATING_FUNCTION_SPLIT: "GENERATING_FUNCTION_SPLIT",

  // inline chat
  INLINE_CHAT: "INLINE_CHAT", // vs -> webview 进入inlineChat模式  weview -> vs 发送inlinechat对话消息
  RECEIVE_INLINE_CHAT_MESSAGE: "RECEIVE_INLINE_CHAT_MESSAGE", // 接收inlineChat消息
  RECEIVE_INLINE_CHAT_MESSAGE_DONE: "RECEIVE_INLINE_CHAT_MESSAGE_DONE", // 接收inlineChat消息
  INLINE_CHAT_ACCEPT: "INLINE_CHAT_ACCEPT",
  INLINE_CHAT_REJECT: "INLINE_CHAT_REJECT",
  RECEIVE_GOTO_CHAT: "RECEIVE_GOTO_CHAT", // 意图识别命中 非 inlinchat时触发
};

export const ButtonStyleMap = {
  dark: {
    default: {
      color: "white",
      bg: "linear-gradient(93deg, #3385FF 6.85%, #07A1F8 99.24%)",
    },
    disabled: {
      color: "#B4BCD0",
      bg: "rgba(131, 131, 131, 0.10)",
    },
  },
  light: {
    default: {
      color: "white",
      bg: "linear-gradient(93deg, #3385FF 6.85%, #07A1F8 99.24%)",
    },
    disabled: {
      color: "#70777F",
      bg: "#F4F6F8",
    },
  },
};

export const customIconButtonForInput = defineStyle({
  display: "flex",
  padding: "0px",
  alignItems: "center",
  justifyContent: "center",
  borderRadius: "4px",
  minWidth: "22px",
  width: "22px",
  height: "22px",
  // mr: "4px",
  background: "none",
  _dark: {
    color: "#8D96A0",
    _hover: {
      background: "#29313A",
    },
  },
  _light: {
    color: "#636C76",
    _hover: {
      background: "#E2E9F2",
    },
  },
});

export const customIconButton = defineStyle({
  display: "flex",
  padding: "4px 12px",
  alignItems: "center",
  justifyContent: "center",
  borderRadius: "4px",
  minWidth: "38px",
  height: "22px",
  mr: "4px",
  _dark: {
    border: "0.5px solid #40474E",
    background: "#1E262F",
    color: "#F7F8F8",
    _hover: {
      border: "0.5px solid #8C939C",
      background: "#31363D",
    },
  },
  _light: {
    border: "0.5px solid #DCE1E6",
    background: "#F6F8FA",
    color: "#656D76",
    _hover: {
      border: "0.5px solid #DCE1E6",
      background: "#EEF1F4",
    },
  },
});

export const modelSelectButton = defineStyle({
  _dark: {
    // border: "0.5px solid #40474E",
    background: "#1E262F",
    color: "#F7F8F8",
    _hover: {
      // border: "0.5px solid #8C939C",
      background: "#31363D",
    },
    _selected: {
      // border: "0.5px solid #8C939C",
      background: "#3F464F",
    },
  },
  _light: {
    // border: "0.5px solid #DCE1E6",
    background: "#FFFFFF",
    color: "#656D76",
    _hover: {
      // border: "0.5px solid #DCE1E6",
      background: "#EEF1F4",
    },
    _selected: {
      // border: "0.5px solid #8C939C",
      background: "#EAEEF2",
    },
  },
});

export const textOverflowCss = defineStyle({
  display: "inline-block",
  overflow: "hidden",
  whiteSpace: "nowrap",
  textOverflow: "ellipsis",
});

export const defaultThemeName = {
  dark: "kwaipilot-default-theme-dark",
  light: "kwaipilot-default-theme-light",
};
