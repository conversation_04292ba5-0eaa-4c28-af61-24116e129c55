import {
  getLocalStorageValue,
  setLocalStorageValue,
  removeLocalStorageValue,
} from "./localStorage";
import { ClientSideModel, type DialogSetting } from "@/store/record";
import { IChatModelType } from "@shared/types/business";
import { ICachedMessageAnswer, ICachedMessageQuestion, QAItem, SessionItem } from "@shared/types/chatHistory";
import { generateCustomUUID } from "shared/lib/misc/generateCustomUUID";

export type ISessionItemOrigin = {
  cachedMessages: QAItem[];
  sessionName: string;
  sessionTime: string;
};

// 把 key 放到元素里，形成一个数组
export type ISessionItem = ISessionItemOrigin & {
  sessionId: string;
};

export type IActionResult = {
  link: string;
  title: string;
};

export interface CodeSearchRelate {
  code: string;
  endColNo: number;
  endLineNo: number;
  id: number;
  language: string;
  path: string;
  startColNo: number;
  startLineNo: number;
}

// 提问和回答会存两条，相同的ID
export type ICachedMessage = ICachedMessageAnswer | ICachedMessageQuestion;

export type IChatRecord = {
  chatId: string;
  role: "user" | "assistant";
  content: string;
  rag: boolean;
};

export type ISessionHistory = {
  [key: string]: ISessionItemOrigin;
};

export type IChatType = "ChatGPT" | "intelligentChat";
export type CurrentSession = {
  type: IChatType;
  sessionIdMap: {
    // intelligentChat: string;
    // ChatGPT: string;
    [key in IChatType]: string;
  };
};

export type IChatModel = {
  modelType?: IChatModelType;
};

export const initCurrentSession: CurrentSession = {
  type: "intelligentChat",
  sessionIdMap: {
    intelligentChat: "",
    ChatGPT: "",
  },
};

export const tabArray: {
  key: IChatType;
  name: string;
}[] = [
  {
    key: "intelligentChat",
    name: "智能问答",
  },
  {
    key: "ChatGPT",
    name: "ChatGPT",
  },
];

export interface IModelListType {
  modelType: IChatModelType;
  name: string;
  desc: string;
  icon: string;
  disabled: boolean;
  vipIcon: string;
  vip: boolean;
  tooltip: string;
}

export function geActiveChatModel(list: ClientSideModel[], key?: IChatModelType) {
  return (
    list.find(model => model.modelType === key || model.name === key)
    || list[0]
  );
}

export function getActiveTabIndex(key: IChatType) {
  return tabArray.findIndex(tab => tab.key === key);
}

// https://docs.corp.kuaishou.com/d/home/<USER>
export function getActionResults(reply: string) {
  try {
    const formatReply = JSON.parse(reply) as {
      type: "FILE_PARSE_RESULT" | "LINK_PARSE_RESULT" | "SEARCH_RESULT";
      id?: number;
      length?: number;
      link?: string;
      title?: string;
      summary?: string;
    }[];
    const result: IActionResult[] = [];
    formatReply.forEach((item) => {
      if (["SEARCH_RESULT"].includes(item.type) && item.link && item.title) {
        result.push({
          link: item.link || "",
          title: item.title || "",
        });
      }
    });
    return result;
  }
  catch (e) {
    console.error(e);
    return [];
  }
}

export {
  generateCustomUUID,
};

/** 获取对话列表，获取后转化为 ISessionItem[]  */
export function getSessionHistory(): string[] {
  try {
    const localValue = getLocalStorageValue("sessionHistory");
    const originValue: ISessionHistory = localValue
      ? JSON.parse(localValue)
      : {};
    const keys = Object.keys(originValue);
    return keys.map(key => key);
  }
  catch (e) {
    console.error(e);
    return [];
  }
}
/**
 * 获取localstorage中的recodeList
 */
export function getLocalStorageSessionHistory(): SessionItem[] {
  try {
    let res: SessionItem[] = [];
    const localValue = getLocalStorageValue("sessionHistory");
    const originValue:
      | ISessionHistory
      | Record<
        string,
        {
          cachedMessages: ICachedMessage[];
          sessionName: string;
          sessionTime: string;
        }
      > = localValue ? JSON.parse(localValue) : {};
    const keys = Object.keys(originValue);
    const maybeOldData = keys.map(key => ({
      sessionId: key,
      ...originValue[key],
      expiredIndex: [],
      clearContextIndex: [],
      isComposer: false,
    }));

    // eslint-disable-next-line no-prototype-builtins
    if (maybeOldData[0]?.cachedMessages[0].hasOwnProperty("reply")) {
      res = formatCachedMessage(
        maybeOldData as {
          cachedMessages: ICachedMessage[];
          sessionName: string;
          sessionTime: string;
          sessionId: string;
        }[],
      );
    }
    else {
      res = maybeOldData as SessionItem[];
    }
    return res;
  }
  catch (e) {
    console.error(e);
    return [];
  }
}
/** 兼容旧版的数据存储，在获取时转换一下 */
const formatCachedMessage = (
  recordList: {
    cachedMessages: ICachedMessage[];
    sessionName: string;
    sessionTime: string;
    sessionId: string;
  }[],
): SessionItem[] => {
  return recordList.map((record) => {
    const cachedMessage = record.cachedMessages;
    const formatMessage: QAItem[] = [];
    let currentQAItem: QAItem | undefined;
    cachedMessage.forEach((msg) => {
      const { isSelf } = msg;
      if (isSelf) {
        currentQAItem = {
          id: msg.id,
          Q: {
            ...msg,
            // 反正是历史数据，不兼容了
            v2: undefined,
          },
          A: [],
        };
        formatMessage.push(currentQAItem);
      }
      else {
        if (currentQAItem && msg.id === currentQAItem.id) {
          currentQAItem.A.push({
            ...msg,
            answerId: "answerId" in msg && msg.answerId ? msg.answerId : generateCustomUUID(),
            ttfb: 0,
            thinkInfo: undefined,
          });
        }
      }
    });
    return {
      sessionId: record.sessionId,
      cachedMessages: formatMessage,
      sessionName: record.sessionName,
      sessionTime: record.sessionTime,
      expiredIndex: [],
      clearContextIndex: [],
      isComposer: false,
    };
  });
};

/**
 * 保存用户设置
 */
export function updateDialogSetting(setting: DialogSetting) {
  const str = getLocalStorageValue("dialogSetting");
  const newList: DialogSetting[] = [];
  if (str) {
    const list = JSON.parse(str) as DialogSetting[];
    newList.push(...list);
  }
  newList.push(setting);
  setLocalStorageValue("dialogSetting", JSON.stringify(newList));
}
export function getDialogSetting() {
  const str = getLocalStorageValue("dialogSetting");
  if (str) {
    const list = JSON.parse(str) as DialogSetting[];
    return list;
  }
  return [];
}

/** 移除对话列表 */
export function removeSessionHistory() {
  return removeLocalStorageValue("sessionHistory");
}

export function getActiveSession() {
  try {
    return getLocalStorageValue("activaSession") ?? "";
  }
  catch (e) {
    console.error(e);
    return "";
  }
}

/**
 * 设置活跃的对话
 */
export function setSessionToLocal(value: string) {
  return setLocalStorageValue("activaSession", value);
}
