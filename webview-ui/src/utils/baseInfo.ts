import { SharpCommand } from "@shared/types";
import eventBus from "./eventBus";

export class BaseInfoManager {
  pluginVersion = "7.0.4";
  ideVersion = "1.77.0";
  platform = "darwin";
  release = "1.0.0";
  deviceId = "";
  ide = "kwaipilot-vscode";
  hostname = "";
  machine = "";
  arch = "";
  version = "";
  isInWorkspace: boolean | undefined = undefined;

  updateIsInWorkspace(v: boolean) {
    this.isInWorkspace = v;
  }

  updatePluginVersion(v: string) {
    this.pluginVersion = v;
  }

  updateIdeVersion(v: string) {
    this.ideVersion = v;
  }

  updatePlatform(v: string) {
    this.platform = v;
  }

  updateRelease(v: string) {
    this.release = v;
  }

  updateDeviceId(v: string) {
    this.deviceId = v;
  }

  updateIde(v: string) {
    if (v !== "kwaipilot-xcode") {
      eventBus.emit("pushRichEditorPanelDisableMenu", {
        [SharpCommand.FILE]: {
          status: false,
          msg: "",
        },
      });
    }
    this.ide = v;
  }

  updateHostname(v: string) {
    this.hostname = v;
  }

  updateMachine(v: string) {
    this.machine = v;
  }

  updateArch(v: string) {
    this.arch = v;
  }

  updateVersion(v: string) {
    this.version = v;
  }

  get isXcode() {
    const isXcode = this.ide === "kwaipilot-xcode";
    return isXcode;
  }
}

const baseInfoManager = new BaseInfoManager();
export default baseInfoManager;
