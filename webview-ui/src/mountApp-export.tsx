// NOTE: ide 组件，不影响插件逻辑
import React from "react";
import * as ReactDOM from "react-dom/client";
import { ChakraProvider } from "@chakra-ui/react";
import App from "@/App";
import theme from "@/utils/theme";
import CodeImage from "@/components/Prediction/codeImage";
import { IdeEnvProvider } from "@/providers/IdeEnvProvider";
import { getCurrentEnvIsInIDE } from "./utils/ide";
import { VSCodeNativeBridge } from "@/bridge-export/kwaipilotBridge";
import { VSCodeServicesAccessor } from "./mount-export";

/**
 * 挂载应用到指定DOM元素
 *
 * @param rootElement - 要挂载应用的DOM元素
 * @param bridge - 桥接对象实现
 * @param accessor - 可选的 VSCode services accessor，用于访问 VSCode 服务
 * @returns 包含重新渲染和销毁方法的对象
 */
const mountApp = async (
  rootElement: HTMLElement,
  bridge: VSCodeNativeBridge,
  accessor?: VSCodeServicesAccessor,
) => {
  if (typeof document === "undefined") {
    console.error("mount.tsx error: document was undefined");
    return {
      rerender: () => {},
      dispose: () => {},
    };
  }

  const root = ReactDOM.createRoot(rootElement);

  const rerender = () => {
    root.render(
      <ChakraProvider theme={theme}>
        <IdeEnvProvider
          value={{ isKwaiPilotIDE: getCurrentEnvIsInIDE(), accessor }}
        >
          <CodeImage />
          <App />
        </IdeEnvProvider>
      </ChakraProvider>,
    );
  };

  const dispose = () => {
    root.unmount();
    bridge.dispose();
  };

  rerender();

  return {
    rerender,
    dispose,
  };
};

export default mountApp;
