/**
 * 设计系统 token， 同时给 tailwind 和 js hooks 使用
 * https://docs.corp.kuaishou.com/d/home/<USER>
 */

export const light = {
  colorTextMain: "#000000",
  colorTextCommonPrimary: "#20242A",
  colorTextCommonSecondary: "#636C76",
  colorTextCommonTertiary: "#95A1AC",
  colorTextCommonDisable: "#A9B3BE",
  colorTextBrandDefault: "#118DFF",
  colorTextBrandHover: "#359EFF",
  colorIconCommonPrimary: "#20242A",
  colorIconCommonSecondary: "#636C76",
  colorIconCommonTertiary: "#95A1AC",
  colorIconCommonDisable: "#A9B3BE",
  colorIconBrandDefault: "#118DFF",
  colorIconBrandHover: "#359EFF",
  colorBorderCommon: "#E1EAF5",
  colorBorderHorizontal: "#E7EEF7",
  colorBorderVertical: "#E1E2E3",
  colorBorderBar: "#E8F1FF",
  colorBorderNormal: "#D6DCE3",
  colorBgUserDefault: "#D3EBFF",
  colorBgSystemDefault: "#FFFFFF",
  colorBgControlsHover: "#E2E9F2",
  colorBgSelected: "#F0F6FF",
  colorBgHover: "#F3F8FF",
  colorBgBrandActive: "rgba(17, 141, 255, 0.08)",
  colorBgFill: "#FFFFFF",
  colorBgCodeCard: "#FFFFFF",
  colorBgScrollbarDefault: "#DADFE7",
  colorBgCodeBar: "#F4FAFD",
  colorBgCodeContent: "#1A222E",
  colorBgBar: "#F6FBFF",
  colorBgInputFill: "#FFF",
  colorInputTextPlaceholder: "#70777F",
  colorInputBorderNormal: "#D6DCE3",
  colorInputBorderBar: "#E8F1FF",
  colorInputBgBar: "#F6FBFF",
  colorInputBgInputFill: "#FFFFFF",
  colorSendHover: "#E1E5EC",
  colorInputBgSendDisable: "#DADFE7",
  colorInputIconSendDisable: "#FFFFFF",
  colorTagTextKwaipilot: "rgba(129, 155, 189, 0.8)",
  colorTagTextClaude: "rgba(180, 142, 129, 0.8)",
  colorTagTextGpt: "rgba(129, 189, 143, 0.8)",
  // 指令块
  colorCommandText: "rgba(99, 108, 118, 1)",
  colorCommandBg: "rgba(239, 247, 253, 1)",

  colorBgDiffAdded: "#d3e3b1",
  colorBgDiffRemoved: "#fdadad",
};

export type ThemeVars = typeof light;

export const dark: ThemeVars = {
  colorTextMain: "#F8F8F8",
  colorTextCommonPrimary: "#E5ECF2",
  colorTextCommonSecondary: "#B7C3D0",
  colorTextCommonTertiary: "#62646B",
  colorTextCommonDisable: "#4D525B",
  colorTextBrandDefault: "#5AA7FF",
  colorTextBrandHover: "#4F91DC",

  colorIconCommonPrimary: "#E5ECF2",
  colorIconCommonSecondary: "#B7C3D0",
  colorIconCommonTertiary: "#62646B",
  colorIconCommonDisable: "#4D525B",
  colorIconBrandDefault: "#5AA7FF",
  colorIconBrandHover: "#4F91DC",

  colorBorderCommon: "#30363D",
  colorBorderHorizontal: "#38444D",
  colorBorderVertical: "#364658",
  colorBorderBar: "#1A2531",
  colorBorderNormal: "#33455A",

  colorBgUserDefault: "#162A47",
  colorBgSystemDefault: "#1F2B39",
  colorBgControlsHover: "#29313A",
  colorBgSelected: "#2D363E",
  colorBgHover: "#353E49",
  colorBgBrandActive: "rgba(17, 141, 255, 0.08)",
  colorBgFill: "#232C36",
  colorBgCodeCard: "#364459",
  colorBgScrollbarDefault: "#323942",

  colorBgCodeBar: "#0E1825",
  colorBgCodeContent: "#1A222E",
  colorBgBar: "#1C2734",
  colorBgInputFill: "#1F2B39",
  colorInputTextPlaceholder: "#8D96A0",
  colorInputBorderNormal: "#33455A",
  colorInputBorderBar: "#1A2531",
  colorInputBgBar: "#1C2734",
  colorInputBgInputFill: "#1F2B39",
  colorSendHover: "#2E3A48",
  colorInputBgSendDisable: "#323E4C",
  colorInputIconSendDisable: "#757E89",
  colorTagTextKwaipilot: "#96ADCA",
  colorTagTextClaude: "rgba(180, 142, 129, 0.8)",
  colorTagTextGpt: "rgba(129, 189, 143, 0.8)",
  // 指令块
  colorCommandText: "rgba(183, 195, 208, 1)",
  colorCommandBg: "rgba(255, 255, 255, 0.16)",

  colorBgDiffAdded: "#465528",
  colorBgDiffRemoved: "#6d1d1d",
};
