import TopRightArrow from "@/assets/icons/top-right-arrow.svg?react";
import Close from "@/assets/icons/close.svg?react";
import AutoTooltip from "@/components/AutoTooltip";
import clsx from "clsx";
import { ext2IconName } from "@/constant";
import { useMemo } from "react";

interface IProps {
  startLine: number;
  endLine: number;
  remove?: () => void;
  showIcon?: boolean;
  title: string;
  filename: string;
}

export const SelectCodeCard: React.FC<IProps> = (props: IProps) => {
  const {
    filename,
    startLine,
    endLine,
    remove,
    title,
    showIcon = true,
  } = props;

  const iconSrc = useMemo(() => {
    const ext = filename?.split(".").pop() ?? "ts";
    const src = `https://cdnfile.corp.kuaishou.com/kc/files/a/kwaipilot/kwaipilot-file-ext-icon/${
      ext2IconName[ext] ?? "typescript"
    }.svg`;

    return src;
  }, [filename]);

  return (
    <div className="flex w-full flex-col text-[13px] leading-[19.5px] p-2 bg-input-content border border-border-border-common rounded">
      <div className="flex w-full gap-1 h-6 items-center">
        {showIcon && (
          <div className="w-6 h-6 flex-shrink-0 flex justify-center items-center">
            <TopRightArrow></TopRightArrow>
          </div>
        )}
        <div className="flex justify-between flex-auto">
          <div className="text-text-common-secondary">{title}</div>
          {remove && (
            <div
              className="w-4 h-4 flex justify-center items-center  cursor-pointer"
              onClick={() => {
                remove?.();
              }}
            >
              <Close></Close>
            </div>
          )}
        </div>
      </div>
      <div
        className={clsx("flex items-center w-full", {
          "pl-[28px]": showIcon,
        })}
      >
        <div className="flex gap-1 items-center flex-auto overflow-hidden">
          <div className="w-4 h-4">
            <img src={iconSrc} alt="" className="w-4 h-4" />
          </div>
          <div className="flex-auto flex  overflow-hidden">
            <AutoTooltip
              title={filename}
              className="text-text-common-secondary cursor-pointer"
              placement="top"
            >
              {filename}
            </AutoTooltip>
          </div>
        </div>
        <div className="text-text-common-tertiary whitespace-nowrap">
          Lines
          {" "}
          {startLine}
          {" "}
          -
          {" "}
          {endLine}
        </div>
      </div>
    </div>
  );
};
