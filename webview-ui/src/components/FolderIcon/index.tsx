// FIXME: eslint

import clsx from "clsx";

interface IProps {
  isDark: boolean;
  size?: number;
  className?: string;
}

export const FolderIcon: React.FC<IProps> = (props) => {
  const { isDark, className } = props;
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={clsx(className)}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.4933 4.53494L11.322 1.36368L10.5715 1.05713H4.22896L3.17188 2.11421V14.7992L4.22896 15.8563H13.7427L14.7998 14.7992V5.28547L14.4933 4.53494ZM13.7427 14.7992H4.22896V2.11421H9.51439V6.34256H13.7427V14.7992ZM10.5715 5.28547V2.11421L13.7427 5.28547H10.5715Z"
        fill={isDark ? "#3077E2" : "#326BFB"}
      />
    </svg>
  );
};
