import { useState } from "react";
import { Code } from "@/components/Code";
import { CodeSearchRelate } from "@/utils/sessionUtils";
import clsx from "clsx";
import { useColorMode } from "@chakra-ui/react";
import CodeSearchRelateIcon from "@/assets/images/codesearch-relate.svg?react";
import css from "./index.module.less";

interface IProps {
  list?: CodeSearchRelate[];
}

export const RelateCode: React.FC<IProps> = (props: IProps) => {
  const { list } = props;
  const [isFold, setIsFold] = useState(true);
  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";

  if (!list) return null;

  return (
    <div
      className={clsx(
        "p-2 flex flex-col gap-2 border overflow-hidden mb-3 rounded",
        [isFold ? "h-[34px]" : "h-auto"],
        [isDark ? "border-[#40474E]" : "border-[#DCE1E6]"],
      )}
    >
      <div
        className={clsx("title cursor-pointer group flex items-center gap-1", [
          isDark ? css["fold-dark"] : css["fold-light"],
          isFold ? css["fold"] : "",
        ])}
        onClick={() => setIsFold(!isFold)}
      >
        <CodeSearchRelateIcon />
        <span
          className={clsx(
            "text-[13px] leading-[18px]",
            [isDark ? "text-[#8B949E]" : "text-[#676D75]"],
            [
              isDark
                ? "group-hover:text-[#B4BCD0]"
                : "group-hover:text-[#212429]",
            ],
          )}
        >
          {isFold ? "展开" : "收起"}
          相关引用(
          {list?.length}
          )
        </span>
      </div>
      {list.map((code, idx) => {
        return (
          <Code
            key={idx}
            path={code.path}
            filename={code.path.split("/").pop() || ""}
            startLine={code.startLineNo}
            endLine={code.endLineNo}
            content={code.code}
          >
          </Code>
        );
      })}
    </div>
  );
};
