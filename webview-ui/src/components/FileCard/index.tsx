import CircleClose from "@/assets/icons/circle-close.svg?react";

import { UploadFile } from "@shared/types/textarea";
import { useMemo } from "react";
import AutoTooltip from "@/components/AutoTooltip";
import { formatFileSize } from "@/utils/utils";
import { FileIconType, getFileExt } from "@/utils/file";

interface IProps {
  file: UploadFile;
  remove?: (file: UploadFile) => void;
  className?: string;
}

export const FileCard: React.FC<IProps> = (props: IProps) => {
  const { file, remove, className } = props;
  const { filename, type, size } = file;
  const fileExit = useMemo(() => {
    return getFileExt(filename);
  }, [filename]);

  const filenameExcludeExt = useMemo(() => {
    const paths = filename.split(".");
    return paths.shift();
  }, [filename]);
  const FileIcon = useMemo(() => {
    return FileIconType(getFileExt(filename), type);
  }, [filename, type]);

  const formatSize = formatFileSize(size);
  return (
    <div
      className={`flex group gap-1 cursor-pointer bg-textPreformat-background rounded py-[6px] px-[8px] items-center relative ${className}`}
    >
      <div className="h-6 w-6 flex justify-center items-center  flex-shrink-0">
        <FileIcon className="w-full h-full"></FileIcon>
      </div>
      <div className="text-[12px] leading-[18px] flex flex-auto flex-col overflow-hidden">
        <div className="text-foreground flex-auto overflow-hidden justify-start flex flex-nowrap">
          <AutoTooltip placement="top" title={filenameExcludeExt}>
            {filenameExcludeExt}
          </AutoTooltip>
          <span>
            {" "}
            {fileExit ? `.${fileExit}` : ""}
          </span>
        </div>
        <div className="text-foreground">{formatSize}</div>
      </div>
      {remove && (
        <div
          className="size-4 cursor-pointer absolute top-0 right-0 translate-x-1/4 -translate-y-1/4 hidden group-hover:block text-icon-foreground"
          onClick={() => {
            remove?.(file);
          }}
        >
          <CircleClose />
        </div>
      )}
    </div>
  );
};
