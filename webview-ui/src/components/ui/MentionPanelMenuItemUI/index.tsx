import { Spinner, useMergeRefs } from "@chakra-ui/react";
import { Icon } from "@/components/Union/t-iconify";

import { ComponentProps, forwardRef, ReactNode, useEffect, useRef } from "react";
import { twMerge } from "tailwind-merge";

export const MenuItemUI = forwardRef<
  HTMLButtonElement,
  {
    selectMode: "unique" | "any";
    isInContextHeader: boolean;
    selected: boolean;
    iconifyIcon?: string;
    icon?: ReactNode;
    children?: ReactNode;
    loading?: boolean;
  } & ComponentProps<"button">
>(function MenuItemUI({
  selected,
  isInContextHeader,
  selectMode,
  icon,
  iconifyIcon,
  children,
  loading,
  className,
  ...rest
}, forwardedRef) {
  const rootRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (selected) {
      rootRef.current?.scrollIntoView({
        behavior: "instant",
        block: "nearest",
      });
    }
  }, [selected]);

  return (
    <button
      ref={useMergeRefs(rootRef, forwardedRef)}
      style={{
        fontFamily: "var(--vscode-font-family)",
      }}
      className={twMerge(

        "group flex items-center px-3 gap-1 w-full rounded-sm h-[28px] leading-[16px] cursor-pointer focus-visible:outline-none",
        selected
          ? "bg-list-hoverBackground"
          : "",
        className,
      )}
      {...rest}
    >
      {icon ? icon : iconifyIcon ? <Icon icon={iconifyIcon} width={12} className="flex-none" /> : undefined}
      {children}
      {selectMode === "unique" && isInContextHeader && (
        <Icon icon="codicon:check" width="14" height="14" className="ml-auto flex-none" color="currentColor" />
      )}
      {loading && <Spinner ml="4px" size="xs" />}
    </button>
  );
});
