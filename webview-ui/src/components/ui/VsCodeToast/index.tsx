import { vsCss } from "@/style/vscode";
import { Alert, AlertDescription, AlertTitle, chakra, CloseButton, ToastProps } from "@chakra-ui/react";

/**
 * 贴近原生 vscode ui 的 toast 样式, 验证没问题后集成到全局 provider 中
 * @param props
 * @returns
 */
export const VsCodeToast: React.FC<ToastProps> = (props) => {
  const {
    status,
    variant = "solid",
    id,
    title,
    isClosable,
    onClose,
    description,
    colorScheme,
    // icon,
  } = props;

  const ids = id
    ? {
        root: `toast-${id}`,
        title: `toast-${id}-title`,
        description: `toast-${id}-description`,
      }
    : undefined;

  return (
    <Alert
      addRole={false}
      status={status}
      variant={variant}
      id={ids?.root}
      alignItems="start"
      borderRadius="md"
      boxShadow="lg"
      paddingEnd={8}
      textAlign="start"
      width="auto"
      textColor={vsCss.notificationsForeground}
      borderColor={vsCss.notificationToastBorder}
      borderWidth={1}
      borderStyle="solid"
      bgColor={vsCss.notificationsBackground}
      px="10px"
      py="10px"
      colorScheme={colorScheme}
    >
      {/* <AlertIcon>{icon}</AlertIcon> */}
      <chakra.div flex="1" maxWidth="100%">
        {title && <AlertTitle id={ids?.title}>{title}</AlertTitle>}
        {description && (
          <AlertDescription id={ids?.description} display="block">
            {description}
          </AlertDescription>
        )}
      </chakra.div>
      {isClosable && (
        <CloseButton
          size="sm"
          onClick={onClose}
          position="absolute"
          insetEnd={1}
          top={1}
        />
      )}
    </Alert>
  );
};
