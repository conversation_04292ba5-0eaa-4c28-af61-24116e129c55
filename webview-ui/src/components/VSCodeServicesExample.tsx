// NOTE: ide 专用文件，插件请勿修改
import React, { useEffect, useState } from "react";
import { Box, Button, Text, VStack, HStack, Input } from "@chakra-ui/react";
import {
  useVSCodeServices,
  useCommandService,
  useConfigurationService,
  useLogService,
  useThemeService,
} from "../hooks/useVSCodeServices";

/**
 * VSCode Services 使用示例组件
 * 展示如何在 React 组件中访问和使用 VSCode 的各种服务
 */
export const VSCodeServicesExample: React.FC = () => {
  const [commandId, setCommandId] = useState("workbench.action.showCommands");
  const [configSection, setConfigSection] = useState("editor.fontSize");
  const [serviceStatus, setServiceStatus] = useState<string>("");

  // 使用自定义 hooks
  const vscodeServices = useVSCodeServices();
  const commandService = useCommandService();
  const configService = useConfigurationService();
  const logService = useLogService();
  const themeService = useThemeService();

  useEffect(() => {
    if (vscodeServices) {
      setServiceStatus("✅ VSCode Services 可用");
    }
    else {
      setServiceStatus("❌ VSCode Services 不可用");
    }
  }, [vscodeServices]);

  const handleExecuteCommand = async () => {
    try {
      if (commandService) {
        await commandService.executeCommand(commandId);
        console.log(`执行命令: ${commandId}`);
        logService?.info(`成功执行命令: ${commandId}`);
      }
      else {
        console.warn("命令服务不可用");
      }
    }
    catch (error) {
      console.error("命令执行失败:", error);
      logService?.error("命令执行失败:", error);
    }
  };

  const handleGetConfiguration = () => {
    try {
      if (configService) {
        const config = configService.getValue(configSection);
        console.log(`获取配置 ${configSection}:`, config);
        logService?.info(`获取配置 ${configSection}:`, config);
      }
      else {
        console.warn("配置服务不可用");
      }
    }
    catch (error) {
      console.error("配置获取失败:", error);
      logService?.error("配置获取失败:", error);
    }
  };

  const handleGetTheme = () => {
    try {
      if (themeService) {
        const theme = themeService.getColorTheme();
        console.log("当前主题信息:", theme);
        logService?.info("当前主题信息:", theme);
      }
      else {
        console.warn("主题服务不可用");
      }
    }
    catch (error) {
      console.error("主题获取失败:", error);
      logService?.error("主题获取失败:", error);
    }
  };

  const handleLogTest = () => {
    if (logService) {
      logService.info("这是一条信息日志");
      logService.warn("这是一条警告日志");
      logService.error("这是一条错误日志");
      logService.debug("这是一条调试日志");
    }
    else {
      console.log("日志服务不可用，使用 console 输出");
    }
  };

  return (
    <Box p={4} border="1px" borderColor="gray.200" borderRadius="md">
      <VStack spacing={4} align="stretch">
        <Text fontSize="lg" fontWeight="bold">
          VSCode Services 使用示例
        </Text>

        <Text color={serviceStatus.includes("✅") ? "green.500" : "red.500"}>
          {serviceStatus}
        </Text>

        {/* 服务可用性状态 */}
        <Box bg="gray.50" p={3} borderRadius="md">
          <Text fontSize="sm" fontWeight="medium" mb={2}>服务可用性状态:</Text>
          <VStack align="start" spacing={1}>
            <Text fontSize="xs" color={commandService ? "green.600" : "red.600"}>
              命令服务:
              {" "}
              {commandService ? "✅ 可用" : "❌ 不可用"}
            </Text>
            <Text fontSize="xs" color={configService ? "green.600" : "red.600"}>
              配置服务:
              {" "}
              {configService ? "✅ 可用" : "❌ 不可用"}
            </Text>
            <Text fontSize="xs" color={logService ? "green.600" : "red.600"}>
              日志服务:
              {" "}
              {logService ? "✅ 可用" : "❌ 不可用"}
            </Text>
            <Text fontSize="xs" color={themeService ? "green.600" : "red.600"}>
              主题服务:
              {" "}
              {themeService ? "✅ 可用" : "❌ 不可用"}
            </Text>
          </VStack>
        </Box>

        {/* 命令执行示例 */}
        <Box>
          <Text fontWeight="medium" mb={2}>执行 VSCode 命令:</Text>
          <HStack>
            <Input
              value={commandId}
              onChange={e => setCommandId(e.target.value)}
              placeholder="输入命令 ID"
              size="sm"
            />
            <Button
              size="sm"
              onClick={handleExecuteCommand}
              isDisabled={!commandService}
            >
              执行命令
            </Button>
          </HStack>
        </Box>

        {/* 配置获取示例 */}
        <Box>
          <Text fontWeight="medium" mb={2}>获取 VSCode 配置:</Text>
          <HStack>
            <Input
              value={configSection}
              onChange={e => setConfigSection(e.target.value)}
              placeholder="输入配置节"
              size="sm"
            />
            <Button
              size="sm"
              onClick={handleGetConfiguration}
              isDisabled={!configService}
            >
              获取配置
            </Button>
          </HStack>
        </Box>

        {/* 主题服务示例 */}
        <Box>
          <Text fontWeight="medium" mb={2}>获取主题信息:</Text>
          <Button
            size="sm"
            onClick={handleGetTheme}
            isDisabled={!themeService}
          >
            获取当前主题
          </Button>
        </Box>

        {/* 日志服务示例 */}
        <Box>
          <Text fontWeight="medium" mb={2}>日志服务测试:</Text>
          <Button size="sm" onClick={handleLogTest}>
            测试日志输出
          </Button>
        </Box>

        {/* 使用说明 */}
        <Box bg="blue.50" p={3} borderRadius="md">
          <Text fontSize="sm" color="blue.800">
            <strong>使用说明：</strong>
            <br />
            1. 现在使用预准备的服务实例，避免了运行时查找错误
            <br />
            2. 每个服务都有对应的便捷 hook，如 useCommandService()
            <br />
            3. 服务不可用时按钮会被禁用，并显示相应状态
            <br />
            4. 所有操作都会在控制台和 VSCode 日志中记录
          </Text>
        </Box>
      </VStack>
    </Box>
  );
};
