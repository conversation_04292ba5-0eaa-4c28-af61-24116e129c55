import { kwaiPilotBridgeAPI } from "@/bridge";
import { useEffect } from "react";
import { useWebviewVisible } from "../hooks/useWebviewVisible";
import { $getAllCustomVariableNode, $createCustomVariableNode } from "./CustomVariableNode";
import { useLexicalEditable } from "@lexical/react/useLexicalEditable";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { $setSelection } from "lexical";
import { throwNeverError } from "@/utils/throwUnknownError";
import { displayPathDirname } from "shared";
import { URI } from "vscode-uri";

/**
 * 一些CustomVariable(file, selection) 需要根据当前文件和选中的内容自动更新
 * 需求 自定义提示词：https://docs.corp.kuaishou.com/k/home/<USER>/fcABCcPY44EYjgB1nEMJT8zWl
 * @returns
 */
export function CustomVariableAutoUpdatePlugin() {
  const [editor] = useLexicalComposerContext();
  const webviewVisible = useWebviewVisible();
  const editable = useLexicalEditable();
  useEffect(() => {
    if (webviewVisible && editable) {
      const subscription = kwaiPilotBridgeAPI.observableAPI.currentFileAndSelection()
        .subscribe((data) => {
          if (!data) {
            return;
          }
          editor.update(() => {
            const customVariableNodesToBeUpdated = $getAllCustomVariableNode();

            let isUpdated = false;
            for (const node of customVariableNodesToBeUpdated) {
              const contextItem = node.getContextItem();
              if (contextItem.type === "file") {
                node.replace($createCustomVariableNode({
                  type: "file",
                  ...data,
                }, { isFromInitialContext: node.isFromInitialContext }));
                isUpdated = true;
              }
              else if (contextItem.type === "selection") {
                node.replace($createCustomVariableNode({
                  ...data,
                  type: "selection",
                }, { isFromInitialContext: node.isFromInitialContext }));
                isUpdated = true;
              }
              else if (contextItem.type === "repository") {
                // 不需要随时 update， 在插入时获取仓库信息即可
              }
              else if (contextItem.type === "symbol") {
                throw new Error("暂不支持 Symbol");
              }
              else if (contextItem.type === "tree") {
                node.replace($createCustomVariableNode({
                  type: "tree",
                  uri: displayPathDirname(URI.parse(data.uri)),
                  isWorkspaceRoot: true,
                }, { isFromInitialContext: node.isFromInitialContext }));
                isUpdated = true;
              }
              else if (contextItem.type === "userDefined") {
                // 无需处理
              }
              else if (contextItem.type === "language") {
                // 无需处理
              }
              else {
                throwNeverError(contextItem);
              }
            }
            if (isUpdated) {
              $setSelection(null);
            }
          });
        });
      return () => {
        subscription.unsubscribe();
      };
    }
  }, [editable, editor, webviewVisible]);
  return null;
}
