import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { useLexicalNodeSelection } from "@lexical/react/useLexicalNodeSelection";
import { mergeRegister } from "@lexical/utils";
import {
  $getNodeByKey,
  $getSelection,
  $isDecoratorNode,
  $isElementNode,
  $isNodeSelection,
  $isTextNode,
  $setSelection,
  BLUR_COMMAND,
  CLICK_COMMAND,
  COMMAND_PRIORITY_LOW,
  COMMAND_PRIORITY_NORMAL,
  KEY_ARROW_LEFT_COMMAND,
  KEY_ARROW_RIGHT_COMMAND,
  KEY_BACKSPACE_COMMAND,
  KEY_DELETE_COMMAND,
  LexicalNode,
  SELECTION_CHANGE_COMMAND,
} from "lexical";
import { createContext, type FunctionComponent, ReactNode, useCallback, useContext, useEffect, useMemo, useRef, useState } from "react";
import { type CustomVariableNode } from "./CustomVariableNode";
import { useIsFocused } from "../hooks/useRichEditorContext";
import { Tooltip } from "@/components/Union/chakra-ui";
import { customVariableItemMentionNodeDisplayText, SerializedCustomVariableItem } from "shared/lib/CustomVariable/nodes";
import styles from "./CustomVariableLabel.module.css";
import KidIcon from "@/components/Union/kid";

import IconFile from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_file_line";
import IconTree from "@kid/enterprise-icon/icon/output/common/system/common_system_folder_close_line";
import IconLanguage from "@kid/enterprise-icon/icon/output/common/system/new_kdev_code";
import IconRepository from "@kid/enterprise-icon/icon/output/common/system/kdev_repositories";
import IconSymbol from "@kid/enterprise-icon/icon/output/common/system/common_system_marker";
import useResizeObserver from "use-resize-observer";
import clsx from "clsx";
import { BuiltInVariableType, CustomVariableItem, CustomVariableUserDefined } from "shared";
import { twMerge } from "tailwind-merge";
import { produce } from "immer";

const iconMappedByType: Partial<Record<BuiltInVariableType, ReactNode>> = {
  tree: <KidIcon config={IconTree} size={14} color="currentColor" />,
  file: <KidIcon config={IconFile} size={14} color="currentColor" />,
  repository: <KidIcon config={IconRepository} size={14} color="currentColor" />,
  selection: <KidIcon config={IconFile} size={14} color="currentColor" />,
  language: <KidIcon config={IconLanguage} size={14} color="currentColor" />,
  symbol: <KidIcon config={IconSymbol} size={14} color="currentColor" />,
};

const variableLiteralLabelMap: Partial<Record<BuiltInVariableType, string>> = {
  tree: "directory",
  file: "file",
  repository: "repository",
  selection: "selection",
  language: "language",
  symbol: "symbol",
};

const MENTION_CLASS_NAME = styles.customVariableNode;

const FOCUSED_CLASS_NAME = styles.customVariableChipNodeFocused;

export const MENTION_NODE_CLASS_NAME = `custom-variable-node ${MENTION_CLASS_NAME}`;

const IS_IOS = false;

const CustomVariableContext = createContext<{
  $isCustomVariableNode: (
  node: LexicalNode | null | undefined,
  ) => node is CustomVariableNode;
} | null>(null);

const BuiltInCustomVariableComponent: Record<BuiltInVariableType, FunctionComponent<{ node: CustomVariableNode; className?: string }>> = {
  tree: CustomVariable_CurrentDir,
  file: CustomVariable_CurrentFile,
  repository: CustomVariable_Repository,
  selection: CustomVariable_Selection,
  language: CustomVariable_Language,
  symbol: CustomVariable_UserDefined,
};

function extraClassNamesForContextItem(contextItem: CustomVariableItem | SerializedCustomVariableItem): string {
  const classNames: string[] = [];
  if (/* contextItem.isTooLarge ||  */contextItem.isIgnored) {
    classNames.push(styles.isTooLargeOrIgnored);
  }
  return classNames.join(" ");
}

export const CustomVariableLabel: FunctionComponent<{
  node: CustomVariableNode;
  className?: string;
  $isCustomVariableNode: (
    node: LexicalNode | null | undefined,
  ) => node is CustomVariableNode;
}> = ({ node, className, $isCustomVariableNode }) => {
  const [editor] = useLexicalComposerContext();
  const isReadonly = !editor.isEditable();
  const type = node.__contextItem.type;
  const Component = isReadonly
    ? CustomVariableReadonly
    : type === "userDefined"
      ? CustomVariable_UserDefined
      : (BuiltInCustomVariableComponent[type] ?? CustomVariable_UserDefined);

  return (
    <CustomVariableContext.Provider value={{ $isCustomVariableNode }}>
      <Component node={node} className={className} />
    </CustomVariableContext.Provider>
  );
};

/**
 * 匹配当前选中的内容
 */
function CustomVariable_Selection({ node, className }: {
  node: CustomVariableNode;
  className?: string;
}) {
  const tooltipContent = (
    <div className="text-[12px] flex items-center gap-1">
      <KidIcon config={IconFile} size={14} color="currentColor" />
      {customVariableItemMentionNodeDisplayText(node.__contextItem)}
    </div>
  );
  return (
    <CustomVariableLabelBase node={node} className={className} tooltip={tooltipContent}>
      {variableLiteralLabelMap.selection}
    </CustomVariableLabelBase>
  );
}

/**
 * 匹配当前文件
 */
function CustomVariable_CurrentFile({ node, className }: {
  node: CustomVariableNode;
  className?: string;
}) {
  const tooltipContent = (
    <div className=" text-[12px] flex items-center gap-1">
      <KidIcon config={IconFile} size={14} color="currentColor" />
      {customVariableItemMentionNodeDisplayText(node.__contextItem)}
    </div>
  );
  return (
    <CustomVariableLabelBase node={node} className={className} tooltip={tooltipContent}>
      {variableLiteralLabelMap.file}
    </CustomVariableLabelBase>
  );
}

/**
 * 匹配当前文件的语言
 */
function CustomVariable_Language({ node, className }: {
  node: CustomVariableNode;
  className?: string;
}) {
  const tooltipContent = (
    <div className=" text-[12px] flex items-center gap-1">
      <KidIcon config={IconLanguage} size={14} color="currentColor" />
      {customVariableItemMentionNodeDisplayText(node.__contextItem)}
    </div>
  );
  return (
    <CustomVariableLabelBase node={node} className={className} tooltip={tooltipContent}>
      {variableLiteralLabelMap.language}
    </CustomVariableLabelBase>
  );
}

function CustomVariable_Repository({ node, className }: {
  node: CustomVariableNode;
  className?: string;
}) {
  const tooltipContent = (
    <div className="text-[12px] flex items-center gap-1">
      <KidIcon config={IconFile} size={14} color="currentColor" />
      {customVariableItemMentionNodeDisplayText(node.__contextItem)}
    </div>
  );
  return (
    <CustomVariableLabelBase node={node} className={className} tooltip={tooltipContent}>
      {variableLiteralLabelMap.repository}
    </CustomVariableLabelBase>
  );
}

/**
 * 匹配当前目录
 */
function CustomVariable_CurrentDir({ node, className }: {
  node: CustomVariableNode;
  className?: string;
}) {
  const tooltipContent = (
    <div className="text-[12px] flex items-center gap-1">
      <KidIcon config={IconFile} size={14} color="currentColor" />
      {customVariableItemMentionNodeDisplayText(node.__contextItem)}
    </div>
  );
  return (
    <CustomVariableLabelBase node={node} className={className} tooltip={tooltipContent}>
      {variableLiteralLabelMap.tree}
    </CustomVariableLabelBase>
  );
}

/**
 * 非内置的组件，需要用户自行输入
 */
function CustomVariable_UserDefined({ node, className }: {
  node: CustomVariableNode;
  className?: string;
}) {
  const variableItem = node.__contextItem as unknown as CustomVariableUserDefined;
  const [editor] = useLexicalComposerContext();

  const placeholderRef = useRef<HTMLSpanElement>(null);
  const { width: placeholderWidth = 1 } = useResizeObserver({ ref: placeholderRef });

  const [inputValue, setInputValue] = useState(variableItem.variableValue);
  const inputValueRef = useRef<HTMLSpanElement>(null);

  const showPlaceholder = !inputValue;

  useEffect(() => {
    editor.update(() => {
      const item = node.getContextItem();
      if (item.type === "userDefined" && item.variableValue === inputValue) {
        return;
      }
      node.setContextItem(produce(node.__contextItem, (draft) => {
        if (draft.type === "userDefined") {
          draft.variableValue = inputValue;
        }
      }));
    });
  }, [inputValue, node, editor]);

  const onDelete = useCallback((e: KeyboardEvent) => {
    if (e.target === inputValueRef.current) {
      // 保证正常 contentEditable 的 input 事件
      if (!inputValue) {
        $getNodeByKey(node.getKey())?.remove();
      }
      return true;
    }
    return false;
  }, [inputValue, node]);

  useEffect(() => {
    const unregister = mergeRegister(
      editor.registerCommand(KEY_DELETE_COMMAND, onDelete, COMMAND_PRIORITY_NORMAL),
      editor.registerCommand(KEY_BACKSPACE_COMMAND, onDelete, COMMAND_PRIORITY_NORMAL),
    );
    return () => {
      unregister();
    };
  }, [editor, onDelete]);

  return (
    <CustomVariableLabelBase interactive node={node} className={className} tooltip={showPlaceholder ? undefined : variableItem.variableName}>
      <span className=" ">
        <span className={clsx(" opacity-75 w-0 relative", !showPlaceholder && "hidden")}>
          <span ref={placeholderRef} className=" pointer-events-none absolute left-0 top-0 w-max">
            {/* placeholder */}
            [
            {variableItem.variableName}
            ]
          </span>
        </span>
        <span
          ref={inputValueRef}
          onInput={(e) => { setInputValue(e.currentTarget.textContent ?? ""); }}
          className="outline-none inline-block"
          style={{ width: showPlaceholder ? placeholderWidth + "px" : undefined }}
          contentEditable
        >
        </span>
      </span>

    </CustomVariableLabelBase>
  );
}

function CustomVariableReadonly({ node, className }: {
  node: CustomVariableNode;
  className?: string;
}) {
  const tooltipContent = (
    <div className="text-[12px] flex items-center gap-1">
      {node.__contextItem.type in iconMappedByType && iconMappedByType[node.__contextItem.type as BuiltInVariableType]}
      {customVariableItemMentionNodeDisplayText(node.__contextItem)}
    </div>
  );
  return (
    <CustomVariableLabelBase
      node={node}
      className={twMerge("max-w-[100px] inline-block align-top  bg-tag-bg-kwaipilot", className)}
      tooltip={tooltipContent}
    >

      {/* <AutoTooltip title={node.getTextContent()} className=" inline-block text-[12px]  align-top leading-[18px]"> */}
      {node.__contextItem.type === "userDefined"
        ? node.__contextItem.variableName
        : variableLiteralLabelMap[node.__contextItem.type]}
      {/* </AutoTooltip> */}
    </CustomVariableLabelBase>
  );
}

function CustomVariableLabelBase({ node, className, tooltip, children, interactive }: {
  node: CustomVariableNode;
  className?: string;
  tooltip?: ReactNode;
  children?: ReactNode;
  interactive?: boolean;
}) {
  const nodeKey = node.getKey();
  const [editor] = useLexicalComposerContext();
  const isEditorFocused = useIsFocused();
  const [isSelected, setSelected, clearSelection] = useLexicalNodeSelection(nodeKey);
  const onCustomVariableNodeMetaClick = useCallback((_item: SerializedCustomVariableItem) => {}, []);
  const ref = useRef<any>(null);

  const composedClassNames = useMemo(() => {
    const classes = [MENTION_NODE_CLASS_NAME, extraClassNamesForContextItem(
      node.__contextItem,
    ), interactive ? styles["custom-variable-chip-node--interactive"] : undefined, className];
    if (isSelected && (isEditorFocused || !editor.isEditable()) && FOCUSED_CLASS_NAME) {
      classes.push(FOCUSED_CLASS_NAME);
    }
    return classes.join(" ").trim() || undefined;
  }, [node.__contextItem, interactive, className, isSelected, isEditorFocused, editor]);

  const $isCustomVariableNode = useContext(CustomVariableContext)?.$isCustomVariableNode;
  if (!$isCustomVariableNode) {
    throw new Error("CustomVariableLabelBase must be used within a CustomVariableContext");
  }

  const onDelete = useCallback(
    (payload: KeyboardEvent) => {
      if (isSelected && $isNodeSelection($getSelection())) {
        payload.preventDefault();
        const node = $getNodeByKey(nodeKey);
        if ($isCustomVariableNode(node)) {
          node.remove();
          return true;
        }
      }
      return false;
    },
    [$isCustomVariableNode, isSelected, nodeKey],
  );

  const onArrowLeftPress = useCallback(
    (event: KeyboardEvent) => {
      const node = $getNodeByKey(nodeKey);
      if (!node || !node.isSelected()) {
        return false;
      }
      let handled = false;
      const nodeToSelect = node.getPreviousSibling();
      if ($isElementNode(nodeToSelect)) {
        nodeToSelect.selectEnd();
        handled = true;
      }
      if ($isTextNode(nodeToSelect)) {
        nodeToSelect.select();
        handled = true;
      }
      if ($isDecoratorNode(nodeToSelect)) {
        nodeToSelect.selectNext();
        handled = true;
      }
      if (nodeToSelect === null) {
        node.selectPrevious();
        handled = true;
      }
      if (handled) {
        event.preventDefault();
      }
      return handled;
    },
    [nodeKey],
  );

  const onArrowRightPress = useCallback(
    (event: KeyboardEvent) => {
      const node = $getNodeByKey(nodeKey);
      if (!node || !node.isSelected()) {
        return false;
      }
      let handled = false;
      const nodeToSelect = node.getNextSibling();
      if ($isElementNode(nodeToSelect)) {
        nodeToSelect.selectStart();
        handled = true;
      }
      if ($isTextNode(nodeToSelect)) {
        nodeToSelect.select(0, 0);
        handled = true;
      }
      if ($isDecoratorNode(nodeToSelect)) {
        nodeToSelect.selectPrevious();
        handled = true;
      }
      if (nodeToSelect === null) {
        node.selectNext();
        handled = true;
      }
      if (handled) {
        event.preventDefault();
      }
      return handled;
    },
    [nodeKey],
  );

  const onClick = useCallback(
    (event: MouseEvent) => {
      if (event.target === ref.current || ref.current?.contains(event.target as Node)) {
        if (!event.shiftKey) {
          clearSelection();
        }
        setSelected(true);

        // metaKey is true when you press cmd on Mac while clicking.
        if (event.metaKey) {
          onCustomVariableNodeMetaClick?.(node.__contextItem);
        }

        return true;
      }
      return false;
    },
    [clearSelection, setSelected, onCustomVariableNodeMetaClick, node.__contextItem],
  );

  const onBlur = useCallback(() => {
    const node = $getNodeByKey(nodeKey);
    if (!node || !node.isSelected()) {
      return false;
    }

    const selection = $getSelection();
    if (!$isNodeSelection(selection)) {
      return false;
    }

    $setSelection(null);
    return false;
  }, [nodeKey]);

  const onSelectionChange = useCallback(() => {
    if (IS_IOS && isSelected) {
      // Needed to keep the cursor in the editor when clicking next to a selected mention.
      setSelected(false);
      return true;
    }
    return false;
  }, [isSelected, setSelected]);

  useEffect(() => {
    const unregister = mergeRegister(
      editor.registerCommand<MouseEvent>(CLICK_COMMAND, onClick, COMMAND_PRIORITY_LOW),
      editor.registerCommand(KEY_DELETE_COMMAND, onDelete, COMMAND_PRIORITY_LOW),
      editor.registerCommand(KEY_BACKSPACE_COMMAND, onDelete, COMMAND_PRIORITY_LOW),
      editor.registerCommand(KEY_ARROW_LEFT_COMMAND, onArrowLeftPress, COMMAND_PRIORITY_LOW),
      editor.registerCommand(KEY_ARROW_RIGHT_COMMAND, onArrowRightPress, COMMAND_PRIORITY_LOW),
      editor.registerCommand(BLUR_COMMAND, onBlur, COMMAND_PRIORITY_LOW),
      editor.registerCommand(SELECTION_CHANGE_COMMAND, onSelectionChange, COMMAND_PRIORITY_LOW),
    );
    return () => {
      unregister();
    };
  }, [editor, onArrowLeftPress, onArrowRightPress, onClick, onDelete, onBlur, onSelectionChange]);

  return (
    <Tooltip label={tooltip} maxW="90vw" wordBreak="break-all" closeOnScroll>
      <span ref={ref} className={composedClassNames} data-type={node.__contextItem.type}>
        {children ?? node.getTextContent()}
      </span>
    </Tooltip>
  );
};
