import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { COMMAND_PRIORITY_HIGH, KEY_ENTER_COMMAND } from "lexical";
import { useEffect } from "react";
import { useRichEditorContext } from "../hooks/useRichEditorContext";

interface IProps {
  submit: () => unknown;
}

export const EnterSendPlugin: React.FC<IProps> = (props: IProps) => {
  const { submit } = props;
  const [editor] = useLexicalComposerContext();
  const { commandShown, mentionShown, slashV2Shown } = useRichEditorContext();
  useEffect(() => {
    const dispose = editor.registerCommand(
      KEY_ENTER_COMMAND,
      (e: KeyboardEvent) => {
        if (e.keyCode === 229) {
          return false;
        }
        e.preventDefault();
        if (!commandShown.sharpShown && !commandShown.slashShown && !mentionShown && !slashV2Shown) {
          if (editor.isComposing()) {
            return false;
          }
          submit();
          return true;
        }
        return false;
      },
      COMMAND_PRIORITY_HIGH,
    );

    return () => {
      dispose();
    };
  }, [editor, commandShown, mentionShown, submit, slashV2Shown]);
  return null;
};
