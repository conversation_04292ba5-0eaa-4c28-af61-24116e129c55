import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { SerializedEditorState, SerializedLexicalNode } from "lexical";
import React, { useEffect } from "react";

interface IProps {
  changeEditorState: (
    state: SerializedEditorState<SerializedLexicalNode>
  ) => void;
}

export const SaveStatePlugin: React.FC<IProps> = (props) => {
  const { changeEditorState } = props;
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    const dispose = editor.registerUpdateListener(({ editorState }) => {
      const state = editorState.toJSON();
      changeEditorState(state);
    });
    return () => {
      dispose();
    };
  }, [changeEditorState, editor]);

  return null;
};
