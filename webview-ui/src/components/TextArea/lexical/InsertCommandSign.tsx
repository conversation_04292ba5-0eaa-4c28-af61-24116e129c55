import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
  $createTextNode,
  $getSelection,
  $insertNodes,
  $isRangeSelection,
} from "lexical";
import { forwardRef, useCallback, useImperativeHandle } from "react";

interface IProps {
  focused: boolean;
}

export const InsertCommandSign = forwardRef<any, IProps>((props, ref) => {
  const { focused } = props;
  const [editor] = useLexicalComposerContext();

  const insertSign = useCallback(
    (sign: string) => {
      editor.update(() => {
        if (!focused) {
          editor.focus();
        }
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          const signNode = $createTextNode(sign);
          $insertNodes([signNode]);
        }
      });
    },
    [editor, focused],
  );

  useImperativeHandle(ref, () => {
    return {
      insertSign,
    };
  });
  return null;
});

InsertCommandSign.displayName = "InsertCommandSign";
