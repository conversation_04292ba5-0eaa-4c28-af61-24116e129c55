import { SerializedEditorState, SerializedLexicalNode } from "lexical";

/**
 * 前序遍历  lexical 0.25 内置了NodeCaret，未来升级到 lexical 内部实现
 * @param node
 * @returns
 */
export function* traversePreOrder(node: SerializedLexicalNode): Generator<SerializedLexicalNode> {
  // 先访问当前节点
  yield node;

  // 如果有子节点，递归遍历每个子节点
  if ("children" in node && Array.isArray(node.children)) {
    for (const child of node.children) {
      yield * traversePreOrder(child);
    }
  }
}

export function findFirst<T extends SerializedLexicalNode>(
  editorState: SerializedEditorState,
  predicate: (node: SerializedLexicalNode) => node is T,
): T | null;
export function findFirst(
  editorState: SerializedEditorState,
  predicate: (node: SerializedLexicalNode) => boolean,
): SerializedLexicalNode | null;
export function findFirst(
  editorState: SerializedEditorState,
  predicate: (node: SerializedLexicalNode) => boolean,
): SerializedLexicalNode | null {
  for (const node of traversePreOrder(editorState.root)) {
    if (predicate(node)) {
      return node;
    }
  }
  return null;
}
