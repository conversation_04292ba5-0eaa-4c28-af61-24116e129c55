.custom-variable-node {
    --mention-color-opacity: 70%;
    --foreground-color: var(--vscode-foreground);
    --background-color: var(--vscode-textPreformat-background);
    color: color-mix(in lch, var(--foreground-color) calc(1.2*var(--mention-color-opacity)), transparent);
    background-color: color-mix(in lch, var(--background-color) var(--mention-color-opacity), transparent);
    border-radius: 4px;
    white-space: normal;
    padding: 0 4px;
    display: inline-flex;
    align-items: baseline;
    outline-offset: -1px;
    cursor: pointer;
    margin: 0 4px;
    line-height: 18px;
}

.custom-variable-node.custom-variable-chip-node--interactive {
    --foreground-color: var(--vscode-textLink-foreground);
    --background-color: var(--vscode-textBlockQuote-border);
}

.custom-variable-node .icon {
    display: inline-flex;
    align-self: center;
    width: 14px;
    height: 14px;
    margin-right: 2px;
    opacity: 0.8;
    transform: translateY(-1px);
}

.custom-variable-node + .custom-variable-node {
    margin-left: 2px;
}

.custom-variable-node.custom-variable-chip-node--focused {
    outline: solid 1px var(--vscode-inputOption-activeBorder);
}

.custom-variable-node.is-too-large-or-ignored {
    text-decoration: line-through;
    color: var(--vscode-editorWarning-foreground);
}

[data-element-type='body']:is([data-vscode-theme-kind='vscode-high-contrast'], [data-vscode-theme-kind='vscode-high-contrast-light']) .custom-variable-node:not(.custom-variable-chip-node--focused) {
    outline: solid 1px var(--vscode-input-foreground);
}
