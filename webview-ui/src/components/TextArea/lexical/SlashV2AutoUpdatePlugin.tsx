import { kwaiPilotBridgeAPI } from "@/bridge";
import { useEffect } from "react";
import { useWebviewVisible } from "../hooks/useWebviewVisible";
import { useLexicalEditable } from "@lexical/react/useLexicalEditable";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { $setSelection } from "lexical";
import { collectMentionNodeV2 } from "@/logics/UserInputTextarea/ContextHeader/collectMentionNode";
import { MentionNodeV2Structure_File, MentionNodeV2Structure_Selection, MentionNodeV2Structure_SlashCommand, slashCommandSetRequiringContextItem } from "shared/lib/MentionNodeV2/nodes";

/**
 * 一些CustomVariable(file, selection) 需要根据当前文件和选中的内容自动更新
 * 需求 自定义提示词：https://docs.corp.kuaishou.com/k/home/<USER>/fcABCcPY44EYjgB1nEMJT8zWl
 * @returns
 */
export function SlashV2AutoUpdatePlugin() {
  const [editor] = useLexicalComposerContext();
  const webviewVisible = useWebviewVisible();
  const editable = useLexicalEditable();
  useEffect(() => {
    if (webviewVisible && editable) {
      const subscription = kwaiPilotBridgeAPI.observableAPI.currentFileAndSelection()
        .subscribe((data) => {
          const slashCommandNodesToBeUpdated = collectMentionNodeV2(editor)
            .filter(v =>
              v.__structure.type === "slashCommand"
              && slashCommandSetRequiringContextItem.has(v.__structure.command),
            );
          const contextItem: MentionNodeV2Structure_File | MentionNodeV2Structure_Selection | undefined = data?.range
            ? {
                type: "selection",
                content: data.content,
                uri: data.uri,
                range: data.range,
                relativePath: data.relativePath,
              }
            : data?.uri.trim()
              ? {
                  type: "file",
                  uri: data.uri,
                  relativePath: data.relativePath,
                }
              : undefined;

          editor.update(() => {
            const isUpdated = slashCommandNodesToBeUpdated.length > 0;
            for (const node of slashCommandNodesToBeUpdated) {
              const structure = node.getStructureData() as MentionNodeV2Structure_SlashCommand;
              node.setStructureData({
                ...structure,
                contextItem,
              });
            }
            if (isUpdated) {
              $setSelection(null);
            }
          });
        });
      return () => {
        subscription.unsubscribe();
      };
    }
  }, [editable, editor, webviewVisible]);
  return null;
}
