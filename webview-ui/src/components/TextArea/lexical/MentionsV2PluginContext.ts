import { createContext, useContext } from "react";
import { TypeaheadMenuState } from "./MentionsV2Plugin";

export const TypeaheadMenuContext = createContext<TypeaheadMenuState | null>(null);

export function useTypeaheadMenuContext() {
  const context = useContext(TypeaheadMenuContext);
  if (!context) {
    throw new Error("useTypeaheadMenuContext must be used within a TypeaheadMenuContext");
  }
  return context;
}
