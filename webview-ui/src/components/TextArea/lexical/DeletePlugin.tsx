import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
  $getSelection,
  $isRangeSelection,
  COMMAND_PRIORITY_LOW,
  KEY_BACKSPACE_COMMAND,
} from "lexical";
import { useCallback, useEffect } from "react";
import { $isEmptyNode } from "@/components/TextArea/lexical/EmptyNode";
import { $isMentionNode } from "@/components/TextArea/lexical/CommandNode";

export const DeletePlugin = () => {
  const [editor] = useLexicalComposerContext();

  const handleDelete = useCallback(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection) && selection.isCollapsed()) {
      const anchorNode = selection.anchor.getNode();

      const prevSibling = anchorNode.getPreviousSibling();
      const nextSibling = anchorNode.getNextSibling();

      if ($isEmptyNode(anchorNode) && $isMentionNode(prevSibling)) {
        anchorNode.remove();
        prevSibling?.remove();
        return true;
      }

      if ($isEmptyNode(anchorNode) && $isMentionNode(nextSibling)) {
        anchorNode.remove();
        nextSibling?.remove();
        return true;
      }

      if ($isMentionNode(anchorNode) && $isEmptyNode(nextSibling)) {
        anchorNode.remove();
        nextSibling?.remove();
        return true;
      }

      if ($isMentionNode(anchorNode) && $isEmptyNode(prevSibling)) {
        anchorNode.remove();
        prevSibling?.remove();
        return true;
      }
    }

    return false;
  }, []);

  useEffect(() => {
    const backspaceDispose = editor.registerCommand(
      KEY_BACKSPACE_COMMAND,
      (e: KeyboardEvent) => {
        if (e.keyCode === 229) {
          return true;
        }
        else {
          return handleDelete();
        }
      },
      COMMAND_PRIORITY_LOW,
    );

    return () => {
      backspaceDispose();
    };
  }, [editor, handleDelete]);

  return null;
};
