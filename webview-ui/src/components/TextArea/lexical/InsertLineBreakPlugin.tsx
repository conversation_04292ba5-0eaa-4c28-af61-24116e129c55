import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
  $createLineBreakNode,
  $getSelection,
  COMMAND_PRIORITY_HIGH,
  KEY_ENTER_COMMAND,
} from "lexical";
import { useEffect } from "react";

export const InsertLineBreakPlugin: React.FC = () => {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    editor.registerCommand(
      KEY_ENTER_COMMAND,
      (e: KeyboardEvent) => {
        e.preventDefault();
        const isMac = navigator.platform.toUpperCase().includes("MAC");
        if (e.shiftKey || (isMac && e.metaKey) || (!isMac && e.ctrlKey)) {
          const selection = $getSelection();
          const breakLine = $createLineBreakNode();
          selection?.insertNodes([breakLine]);
          return true;
        }
        return false;
      },
      COMMAND_PRIORITY_HIGH,
    );
  }, [editor]);
  return null;
};
