import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { $getRoot } from "lexical";
import { forwardRef, useCallback, useImperativeHandle } from "react";

export const ClearPlugin = forwardRef(function (_props, ref) {
  const [editor] = useLexicalComposerContext();

  const clear = useCallback(() => {
    editor.update(() => {
      const root = $getRoot();
      // const firstNode = root.getFirstChild<ParagraphNode>();
      // firstNode?.clear();
      root?.clear();
    });
  }, [editor]);

  useImperativeHandle(ref, () => {
    return {
      clear,
    };
  });

  return null;
});

ClearPlugin.displayName = "ClearPlugin";
