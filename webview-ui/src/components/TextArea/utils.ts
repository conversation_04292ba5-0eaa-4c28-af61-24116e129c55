import {
  CommandType,
  DisableRichEditorMenu,
  SlashCommand,
} from "@shared/types";
import {
  $createTextNode,
  $getSelection,
  $insertNodes,
  $isRangeSelection,
  $setSelection,
  BaseSelection,
  COMMAND_PRIORITY_HIGH,
  COMMAND_PRIORITY_LOW,
  KEY_ARROW_DOWN_COMMAND,
  KEY_ARROW_UP_COMMAND,
  KEY_ENTER_COMMAND,
  LexicalEditor,
  RangeSelection,
  SerializedEditorState,
  SerializedLexicalNode,
} from "lexical";
import { $createEmptyNode } from "./lexical/EmptyNode";
import { RichEditorBoxPanelData } from "./const";
import { SerializedMentionNode } from "shared/lib/MentionNode";
import { MentionNode } from "./lexical/CommandNode";

let disableMouseEventsTimeout: number | null = null;

export const getSlashCommand = (
  richEditorState?: SerializedEditorState<SerializedLexicalNode>,
): RichEditorBoxPanelData | null => {
  const children = richEditorState?.root?.children || [];

  for (let i = 0; i < children.length; i++) {
    const child: any[] = (children[i] as any).children || [];

    for (let j = 0; j < child.length; j++) {
      if (
        child[j]?.type === "mention"
        && child[j].commandType === CommandType.SLASH
      ) {
        return {
          key: child[j].key,
          commandType: CommandType.SLASH,
          data: child[j].data,
          title: child[j].title,
          description: child[j].description,
          type: "normal",
          uri: "",
        };
      }
    }
  }
  return null;
};

/**
 * @deprecated 使用 `transformToPlainTextForHuman` 代替
 * @param slashCommand
 * @param richEditorState
 * @returns
 */
export const getQuestion = (
  slashCommand: RichEditorBoxPanelData | null,
  richEditorState?: SerializedEditorState<SerializedLexicalNode>,
) => {
  if (slashCommand) {
    return `<div className="kwaipilot-command-block">/${slashCommand.title}</div>`;
  }

  let question = "";

  const children = richEditorState?.root?.children || [];

  children.forEach((child: any) => {
    if (child.type === "linebreak") {
      question += "\n";
    }
    else {
      const subChildren = child?.children as SerializedMentionNode[];
      subChildren?.forEach((i) => {
        if (i.type === "mention" && i.commandType === CommandType.SHARP) {
          question += `<div className="kwaipilot-command-block">#${i.title}</div>`;
        }
        else if (i.type === "text") {
          question += i.text;
        }
        else if (i.type === "linebreak") {
          question += "\n";
        }
      });
      question += "\n";
    }
  });
  return question.trim();
};

export const getSlashCommandReportType = (key: string) => {
  switch (key) {
    case SlashCommand.CODE_EXPLAIN:
      return "代码解释";
    case SlashCommand.LINE_CODE_COMMENT:
      return "行间注释";
    case SlashCommand.FUNC_COMMENT:
      return "函数注释";
    case SlashCommand.CODE_REFACTOR:
      return "代码优化";
    case SlashCommand.FUNC_SPLIT:
      return "函数拆分";
    case SlashCommand.UNIT_TEST:
      return "单元测试";
  }
  return "未知";
};

export const getNextIdx = (
  currentIdx: number,
  direction: number,
  menuOptions: RichEditorBoxPanelData[],
  disabledMenu: DisableRichEditorMenu,
): number => {
  const len = menuOptions.length;
  let nextIdx = currentIdx;
  let attempts = 0;

  while (true) {
    nextIdx
      = currentIdx === -1
        ? direction === 1
          ? 0
          : len - 1
        : (nextIdx + direction + len) % len;
    currentIdx = nextIdx;
    if (
      nextIdx > -1
      && menuOptions[nextIdx]?.key
      && disabledMenu[menuOptions[nextIdx].key]
      && !disabledMenu[menuOptions[nextIdx].key].status
    ) {
      break;
    }
    attempts++;
    if (attempts >= len) {
      return -1;
    }
  }
  return nextIdx;
};

export const registerArrowCommands = (
  editor: LexicalEditor,
  shown: boolean,
  changeKeyBoardEvent: (v: boolean) => void,
  callbacks: {
    onMovePrev: () => void;
    onMoveNext: () => void;
  },
) => {
  const upDispose = shown
    ? editor.registerCommand(
        KEY_ARROW_UP_COMMAND,
        (e: KeyboardEvent) => {
          if (shown) {
            e.preventDefault();
            disableMouseEventsTimeout
            && clearTimeout(disableMouseEventsTimeout);
            changeKeyBoardEvent(true);
            callbacks.onMovePrev();
            disableMouseEventsTimeout = setTimeout(() => {
              changeKeyBoardEvent(false);
            }, 500) as unknown as number;
            return true;
          }
          return false;
        },
        COMMAND_PRIORITY_LOW,
      )
    : () => {};

  const downDispose = shown
    ? editor.registerCommand(
        KEY_ARROW_DOWN_COMMAND,
        (e: KeyboardEvent) => {
          if (shown) {
            e.preventDefault();
            disableMouseEventsTimeout
            && clearTimeout(disableMouseEventsTimeout);
            changeKeyBoardEvent(true);
            callbacks.onMoveNext();
            disableMouseEventsTimeout = setTimeout(() => {
              changeKeyBoardEvent(false);
            }, 500) as unknown as number;
            return true;
          }
          return false;
        },
        COMMAND_PRIORITY_LOW,
      )
    : () => {};

  return () => {
    upDispose();
    downDispose();
  };
};

export const triggerEnter = (
  editor: LexicalEditor,
  shown: boolean,
  select: () => void,
) => {
  const dispose = shown
    ? editor.registerCommand(
        KEY_ENTER_COMMAND,
        (e: KeyboardEvent) => {
          e.preventDefault();
          if (shown) {
            select();
            return true;
          }
          return false;
        },
        COMMAND_PRIORITY_HIGH,
      )
    : () => {};

  return () => {
    dispose();
  };
};

export const $insertBlock = (
  mentionNode: MentionNode,
  selection: BaseSelection | null,
  command: string[],
) => {
  if (!selection) {
    return;
  }

  if (!$isRangeSelection(selection)) {
    return;
  }

  if (!selection.isCollapsed()) {
    return;
  }
  const anchor = selection.anchor;
  if (anchor.type !== "text") {
    return;
  }
  const anchorNode = anchor.getNode();
  if (!anchorNode.isSimpleText()) {
    return;
  }

  const textContent = anchorNode.getTextContent();

  const afterTextContent = textContent.slice(anchor.offset);

  const beforeText = textContent.slice(0, anchor.offset);

  const lastIndxArray = command
    .map((c) => {
      return beforeText.lastIndexOf(c);
    })
    .filter(i => i !== -1)
    .sort((a, b) => b - a);

  /** 有一个命令匹配上 */
  if (lastIndxArray[0] !== undefined) {
    const beforeTextContent = textContent.slice(0, lastIndxArray[0]);
    const node = [];
    if (beforeTextContent) {
      node.push($createTextNode(beforeTextContent), $createTextNode(" "));
    }
    node.push(mentionNode);
    const afterTextNode = $createTextNode(" ");
    node.push(
      $createEmptyNode(),
      afterTextNode,
      $createTextNode(afterTextContent),
    );
    anchorNode.replace($createTextNode(""));
    $insertNodes(node);
    selection.setTextNodeRange(afterTextNode, 1, afterTextNode, 1);
    $setSelection(selection);
  }
};

function getTextUpToAnchor(selection: RangeSelection): string | null {
  const anchor = selection.anchor;
  if (anchor.type !== "text") {
    return null;
  }
  const anchorNode = anchor.getNode();
  if (!anchorNode.isSimpleText()) {
    return null;
  }
  const anchorOffset = anchor.offset;
  return anchorNode.getTextContent().slice(0, anchorOffset);
}

export function getQueryTextForSearch(editor: LexicalEditor): string | null {
  let text = null;
  editor.getEditorState().read(() => {
    const selection = $getSelection();
    if (!$isRangeSelection(selection)) {
      return;
    }
    text = getTextUpToAnchor(selection);
  });
  return text;
}

export function getCommandQuery(trigger: string[], text: string | null) {
  if (!text) return null;

  const ia = trigger
    .map((c) => {
      return text.lastIndexOf(c);
    })
    .filter(i => i !== -1)
    .sort((a, b) => b - a);

  if (ia[0] !== undefined) {
    return text.slice(ia[0] + 1);
  }
  return null;
}

export function getSortFileOrDir<T extends { title: string }>(
  list: T[],
  query: string,
  maxLen = 20,
): T[] {
  if (!query) return list;

  const lowerCaseSubstring = query.toLowerCase();

  // 过滤出包含子串的字符串，并保留其原始索引
  const filteredItems = list
    .map((item, index) => ({ item, index }))
    .filter(item => item.item.title.toLowerCase().includes(lowerCaseSubstring));

  // 根据子串的位置进行排序，位置相同的保留原始顺序
  filteredItems.sort((a, b) => {
    const posA = a.item.title.toLowerCase().indexOf(lowerCaseSubstring);
    const posB = b.item.title.toLowerCase().indexOf(lowerCaseSubstring);
    return posA === posB ? a.index - b.index : posA - posB;
  });

  return filteredItems.slice(0, maxLen).map(({ item }) => item);
  // 返回前20个符合条件的字符串
}

export function removeCustomHtmlBlock(question: string) {
  const regexPattern
    = /(<div className="kwaipilot-command-block">(.*?)<\/div>)/;

  // 调用replace方法，分割捕获的部分并进行替换
  let pre = "";
  const parts = question.split(regexPattern).map((part) => {
    // 为每个part创建新的正则表达式实例
    const regex = new RegExp(regexPattern);
    const match = regex.exec(part);
    if (match) {
      pre = match[2];
      return match[2];
    }
    else {
      if (pre === part) {
        pre = "";
        return pre;
      }
      else {
        return part;
      }
    }
  });
  return parts.join("").startsWith("/") || parts.join("").startsWith("#")
    ? parts.join("").slice(1)
    : parts.join("");
}
