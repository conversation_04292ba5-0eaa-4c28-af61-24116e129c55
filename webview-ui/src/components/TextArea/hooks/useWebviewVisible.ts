import { kwaiPilotBridgeAPI } from "@/bridge";
import { useEffect, useState } from "react";
import { share } from "rxjs";

const visibilityObservable = kwaiPilotBridgeAPI.observableAPI.visibility().pipe(share());

export function useWebviewVisible() {
  const [visible, setVisible] = useState(false);
  useEffect(() => {
    const subscription = visibilityObservable.subscribe({
      next: (visible) => {
        setVisible(visible);
      },
    });
    return () => {
      subscription.unsubscribe();
    };
  }, []);
  return visible;
}
