import NewDialogIcon from "@/assets/icons/new-dialog.svg?react";
import { useRecordStore } from "@/store/record";
import { collectClick } from "@/utils/weblogger";
import { Tooltip } from "@/components/Union/chakra-ui";
import { DEFAULT_MODEL_TYPE } from "@/constant";

export const NewDialog: React.FC = () => {
  const setActiveSession = useRecordStore(state => state.setActiveSession);
  const setChatModelType = useRecordStore(state => state.setChatModelType);

  const createNewDialog = () => {
    collectClick("VS_CREATE_OR_CHANGE_CHAT");
    setActiveSession({ value: "" });
    setChatModelType(DEFAULT_MODEL_TYPE);
  };

  return (
    <Tooltip>
      <div
        onClick={createNewDialog}
        className="overflow-hidden flex gap-1 items-center px-[6px] py-[3px] cursor-pointer rounded text-icon-common-secondary hover:bg-toolbar-hoverBackground"
      >
        <NewDialogIcon className="w-[13.5px] h-[13.5px]" />
        <div
          className="text-[13px] leading-[20px] whitespace-nowrap truncate flex items-center"
        >
          新会话
        </div>
      </div>
    </Tooltip>
  );
};
