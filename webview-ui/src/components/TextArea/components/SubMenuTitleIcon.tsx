import { SharpCommand } from "@shared/types";
import { RichEditorBoxPanelData } from "../const";
import { Icon } from "@/components/Union/t-iconify";

interface IProps {
  isDark: boolean;
  submenu: RichEditorBoxPanelData;
}
export const SubMenuTitleIcon: React.FC<IProps> = (props: IProps) => {
  const { isDark, submenu } = props;

  const iconColor = isDark ? "#F7F8F8" : "#262A2F";

  if (submenu.key === SharpCommand.FILE) {
    return (
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M13.5306 4.96997L10.0306 1.46997C9.96097 1.40023 9.87826 1.34491 9.78721 1.30716C9.69616 1.26941 9.59856 1.24997 9.5 1.24997H3.5C3.16848 1.24997 2.85054 1.38167 2.61612 1.61609C2.3817 1.85051 2.25 2.16845 2.25 2.49997V13.5C2.25 13.8315 2.3817 14.1494 2.61612 14.3839C2.85054 14.6183 3.16848 14.75 3.5 14.75H12.5C12.8315 14.75 13.1495 14.6183 13.3839 14.3839C13.6183 14.1494 13.75 13.8315 13.75 13.5V5.49997C13.75 5.30121 13.6711 5.11059 13.5306 4.96997ZM10 3.56247L11.4375 4.99997H10V3.56247ZM3.75 13.25V2.74997H8.5V5.74997C8.5 5.94888 8.57902 6.13965 8.71967 6.2803C8.86032 6.42095 9.05109 6.49997 9.25 6.49997H12.25V13.25H3.75ZM10.75 8.24997C10.75 8.44888 10.671 8.63965 10.5303 8.7803C10.3897 8.92095 10.1989 8.99997 10 8.99997H6C5.80109 8.99997 5.61032 8.92095 5.46967 8.7803C5.32902 8.63965 5.25 8.44888 5.25 8.24997C5.25 8.05106 5.32902 7.86029 5.46967 7.71964C5.61032 7.57899 5.80109 7.49997 6 7.49997H10C10.1989 7.49997 10.3897 7.57899 10.5303 7.71964C10.671 7.86029 10.75 8.05106 10.75 8.24997ZM10.75 10.75C10.75 10.9489 10.671 11.1396 10.5303 11.2803C10.3897 11.421 10.1989 11.5 10 11.5H6C5.80109 11.5 5.61032 11.421 5.46967 11.2803C5.32902 11.1396 5.25 10.9489 5.25 10.75C5.25 10.5511 5.32902 10.3603 5.46967 10.2196C5.61032 10.079 5.80109 9.99997 6 9.99997H10C10.1989 9.99997 10.3897 10.079 10.5303 10.2196C10.671 10.3603 10.75 10.5511 10.75 10.75Z"
          fill={iconColor}
        />
      </svg>
    );
  }
  else if (submenu.key === SharpCommand.FOLDER) {
    return (
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4.75 4C4.75 3.80109 4.82902 3.61032 4.96967 3.46967C5.11032 3.32902 5.30109 3.25 5.5 3.25H13.5C13.6989 3.25 13.8897 3.32902 14.0303 3.46967C14.171 3.61032 14.25 3.80109 14.25 4C14.25 4.19891 14.171 4.38968 14.0303 4.53033C13.8897 4.67098 13.6989 4.75 13.5 4.75H5.5C5.30109 4.75 5.11032 4.67098 4.96967 4.53033C4.82902 4.38968 4.75 4.19891 4.75 4ZM13.5 7.25H5.5C5.30109 7.25 5.11032 7.32902 4.96967 7.46967C4.82902 7.61032 4.75 7.80109 4.75 8C4.75 8.19891 4.82902 8.38968 4.96967 8.53033C5.11032 8.67098 5.30109 8.75 5.5 8.75H13.5C13.6989 8.75 13.8897 8.67098 14.0303 8.53033C14.171 8.38968 14.25 8.19891 14.25 8C14.25 7.80109 14.171 7.61032 14.0303 7.46967C13.8897 7.32902 13.6989 7.25 13.5 7.25ZM13.5 11.25H5.5C5.30109 11.25 5.11032 11.329 4.96967 11.4697C4.82902 11.6103 4.75 11.8011 4.75 12C4.75 12.1989 4.82902 12.3897 4.96967 12.5303C5.11032 12.671 5.30109 12.75 5.5 12.75H13.5C13.6989 12.75 13.8897 12.671 14.0303 12.5303C14.171 12.3897 14.25 12.1989 14.25 12C14.25 11.8011 14.171 11.6103 14.0303 11.4697C13.8897 11.329 13.6989 11.25 13.5 11.25ZM2.75 7C2.55222 7 2.35888 7.05865 2.19443 7.16853C2.02998 7.27841 1.90181 7.43459 1.82612 7.61732C1.75043 7.80004 1.73063 8.00111 1.76922 8.19509C1.8078 8.38907 1.90304 8.56725 2.04289 8.70711C2.18275 8.84696 2.36093 8.9422 2.55491 8.98079C2.74889 9.01937 2.94996 8.99957 3.13268 8.92388C3.31541 8.84819 3.47159 8.72002 3.58147 8.55557C3.69135 8.39112 3.75 8.19778 3.75 8C3.75 7.73478 3.64464 7.48043 3.45711 7.29289C3.26957 7.10536 3.01522 7 2.75 7ZM2.75 3C2.55222 3 2.35888 3.05865 2.19443 3.16853C2.02998 3.27841 1.90181 3.43459 1.82612 3.61732C1.75043 3.80004 1.73063 4.00111 1.76922 4.19509C1.8078 4.38907 1.90304 4.56725 2.04289 4.70711C2.18275 4.84696 2.36093 4.9422 2.55491 4.98079C2.74889 5.01937 2.94996 4.99957 3.13268 4.92388C3.31541 4.84819 3.47159 4.72002 3.58147 4.55557C3.69135 4.39112 3.75 4.19778 3.75 4C3.75 3.73478 3.64464 3.48043 3.45711 3.29289C3.26957 3.10536 3.01522 3 2.75 3ZM2.75 11C2.55222 11 2.35888 11.0586 2.19443 11.1685C2.02998 11.2784 1.90181 11.4346 1.82612 11.6173C1.75043 11.8 1.73063 12.0011 1.76922 12.1951C1.8078 12.3891 1.90304 12.5673 2.04289 12.7071C2.18275 12.847 2.36093 12.9422 2.55491 12.9808C2.74889 13.0194 2.94996 12.9996 3.13268 12.9239C3.31541 12.8482 3.47159 12.72 3.58147 12.5556C3.69135 12.3911 3.75 12.1978 3.75 12C3.75 11.7348 3.64464 11.4804 3.45711 11.2929C3.26957 11.1054 3.01522 11 2.75 11Z"
          fill={iconColor}
        />
      </svg>
    );
  }
  else if (submenu.key === SharpCommand.RULES) {
    return (
      <Icon icon="lucide:file-code"></Icon>
    );
  }

  return null;
};
