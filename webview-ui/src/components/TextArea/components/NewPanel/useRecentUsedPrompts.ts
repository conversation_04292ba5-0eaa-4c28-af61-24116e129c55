import { useCallback, useState } from "react";
import { CustomPromptData } from "shared/lib/CustomVariable";

const STORAGE_KEY_CUSTOM_PROMPT_RECENT_USED = "customPromptRecentUsed";

const CUSTOM_PROMPT_RECENT_USED_MAX_COUNT = 5;

export type SerializedCustomPromptData = Pick<CustomPromptData, "id" | "name">;

function getCustomPromptRecentUsed(): SerializedCustomPromptData[] {
  const value = localStorage.getItem(STORAGE_KEY_CUSTOM_PROMPT_RECENT_USED);
  if (!value) {
    return [];
  }
  return JSON.parse(value);
}
function _reportCustomPromptRecentUsed(data: CustomPromptData) {
  let recentUsed = getCustomPromptRecentUsed();
  recentUsed.unshift({
    id: data.id,
    name: data.name,
  } satisfies SerializedCustomPromptData);
  recentUsed = recentUsed.slice(0, CUSTOM_PROMPT_RECENT_USED_MAX_COUNT);
  localStorage.setItem(STORAGE_KEY_CUSTOM_PROMPT_RECENT_USED, JSON.stringify(recentUsed));
}

export function useRecentUsedPrompts() {
  const [recentUsedPrompts, setRecentUsedPrompts] = useState<SerializedCustomPromptData[]>(getCustomPromptRecentUsed());
  const reportCustomPromptRecentUsed = useCallback((data: CustomPromptData) => {
    _reportCustomPromptRecentUsed(data);
    setRecentUsedPrompts((prev) => {
      const newList = [data, ...prev];
      return newList.slice(0, CUSTOM_PROMPT_RECENT_USED_MAX_COUNT);
    });
  }, []);
  return {
    recentUsedPrompts,
    reportCustomPromptRecentUsed,
  };
}
