import { unified } from "unified";
import remarkParse from "remark-parse";
import { visit } from "unist-util-visit";

export function findModifiedFilesInMarkdown(markdown: string): {
  filepath: string;
  filename: string;
}[] {
  const res = unified().use(remarkParse).parse(markdown);

  const filepaths: string[] = [];
  visit(res, "code", (node) => {
    const filepath = node.lang?.split(":")?.[1];
    filepath && filepaths.push(filepath);
  });

  return filepaths.map(path => ({
    filepath: path,
    filename: path.split("/").pop() || "",
  }));
}
