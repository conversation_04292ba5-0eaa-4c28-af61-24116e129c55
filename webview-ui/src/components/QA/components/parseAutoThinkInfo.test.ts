import { describe, it, expect } from "vitest";
import { parseAutoThinkInfo } from "./parseAutoThinkInfo";

// 创建模拟的 QAItem 数据
describe("parseAutoThinkInfo", () => {
  describe("deepseek R1", () => {
    it("start with think", () => {
      expect(parseAutoThinkInfo("<think>\ncontent\n</think>\nhello", { isJudgeModel: false })).toEqual({
        judge: "",
        thinkOn: "on",
        think: "content",
        content: "hello",
      });
    });
    it("<think>content", () => {
      expect(parseAutoThinkInfo("<think>content", { isJudgeModel: false })).toEqual({
        judge: "",
        thinkOn: "on",
        think: "content",
        content: "",
      });
    });
    it("<thi", () => {
      expect(parseAutoThinkInfo("<thi", { isJudgeModel: false })).toEqual({
        judge: "",
        thinkOn: "unset",
        think: "",
        content: "",
      });
    });
    it("<think>content</thi", () => {
      expect(parseAutoThinkInfo("<think>content</thi", { isJudgeModel: false })).toEqual({
        judge: "",
        thinkOn: "on",
        think: "content</thi",
        content: "",
      });
    });
    it("without think", () => {
      expect(parseAutoThinkInfo("content", { isJudgeModel: false })).toEqual({
        judge: "",
        thinkOn: "unset",
        think: "",
        content: "content",
      });
    });
  });
  describe("当满足自动思考模型条件时", () => {
    const cases: [string, string, ReturnType<typeof parseAutoThinkInfo>][] = [
      [
        "当没有 judge 结束标签时，应该返回 judge 为全部内容",
        "content without end tag",
        {
          judge: "content without end tag",
          thinkOn: "unset",
          think: "",
          content: "",
        },
      ],
      [
        "无需思考",
        `Requires think-off mode.
</judge>

<think off>
Vue2 和 Vue3 的主要区别如下：`,
        {
          judge: "Requires think-off mode.",
          thinkOn: "off",
          think: "",
          content: "Vue2 和 Vue3 的主要区别如下：",
        },
      ],
      [
        "开启思考",
        `该问题涉及天文学概念与编程场景的交叉领域，需要同时理解科学理论和代码上下文。需要开启 think-on 模式。
</judge>

<think on>
<think>
think content
</think>
content`,
        {
          judge: "该问题涉及天文学概念与编程场景的交叉领域，需要同时理解科学理论和代码上下文。需要开启 think-on 模式。",
          thinkOn: "on",
          think: "think content",
          content: "content",
        },
      ],
      [
        "格式异常: 当有 judge 结束标签但没有 think 标志时，应该默认 thinkOn 为 unset 剩余内容作为正文",
        `judge content
</judge>
remaining content`,
        {
          judge: "judge content",
          thinkOn: "unset",
          think: "",
          content: "remaining content",
        },
      ],
      [
        "思考中",
        "judge content\n</judge>\n<think on>\nremaining content",
        {
          judge: "judge content",
          thinkOn: "on",
          think: "",
          content: "remaining content",
        },
      ],
      [
        "格式异常: 开启 think 但没有 think 标签 应该认定没有思考内容",
        "judge content\n</judge>\n<think on>\nsome other content",
        {
          judge: "judge content",
          thinkOn: "on",
          think: "",
          content: "some other content",
        },
      ],
      [
        "应该正确处理带有空白符的内容",
        "judge content</judge>  <think on>  <think>  thinking content  </think>  final content  ",
        {
          judge: "judge content",
          thinkOn: "on",
          think: "thinking content",
          content: "final content  ",
        },
      ],
      [
        "当 think 标志有多余空格时应该正确处理",
        "judge content</judge>   <think off>   <think>thinking</think>content",
        {
          judge: "judge content",
          thinkOn: "off",
          think: "thinking",
          content: "content",
        },
      ],
      [
        "当内容为空字符串时",
        "",
        {
          judge: "",
          thinkOn: "unset",
          think: "",
          content: "",
        },
      ],
      [
        "当只有 judge 结束标签在最后时",
        "judge content</judge>",
        {
          judge: "judge content",
          thinkOn: "unset",
          think: "",
          content: "",
        },
      ],
      [
        "当 think 标签不正确时应该忽略",
        "judge content</judge><think on>not a think tag content",
        {
          judge: "judge content",
          thinkOn: "on",
          think: "",
          content: "not a think tag content",
        },
      ],
      [
        "边界: 标签输出到一半, 认定还在分析中(judging) 把内容过滤掉",
        "judge content</judge><thi",
        {
          judge: "judge content",
          thinkOn: "unset",
          think: "",
          content: "",
        },
      ],
      [
        "边界: 标签输出到一半",
        "judge content</judge><think on>\n<thi",
        {
          judge: "judge content",
          thinkOn: "on",
          think: "",
          content: "",
        },
      ],
    ];
    for (const [description, content, expected] of cases) {
      it(description, () => {
        const result = parseAutoThinkInfo(content, { isJudgeModel: true });
        expect(result).toEqual(expected);
      });
    }
  });
});
