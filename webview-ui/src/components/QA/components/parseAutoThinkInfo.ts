const JUDGE_END_TAG = "</judge>";

const THINK_FLAG_REGEX = /<think (on|off)>/;

const THINK_OPEN_TAG = "<think>";
const THINK_CLOSE_TAG = "</think>";

const THINK_FLAGS = ["<think on>", "<think off>"];

export function parseAutoThinkInfo(content: string, {
  isJudgeModel,
}: {
  /**
   * 自动思考模型, 会包含分析阶段
   */
  isJudgeModel: boolean;
}): {
    judge: string;
    thinkOn: "on" | "off" | "unset";
    think: string;
    content: string;
  } {
  let restContent = content;
  let judgeContent = "";
  let thinkOn: "on" | "off" | "unset" = "unset";

  if (isJudgeModel) {
    const judgeEndIndex = restContent.indexOf(JUDGE_END_TAG);
    if (judgeEndIndex === -1) {
    /* judge 还未结束 */
      return {
        judge: restContent,
        thinkOn: "unset",
        think: "",
        content: "",
      };
    }
    judgeContent = content.slice(0, judgeEndIndex).trimEnd();
    restContent = content.slice(judgeEndIndex + JUDGE_END_TAG.length).trimStart();

    const thinkFlagRes = restContent.match(THINK_FLAG_REGEX);

    if (!thinkFlagRes) {
      if (THINK_FLAGS.some(flag => flag.startsWith(restContent))) {
      // potential think flag, but not a full think tag
        return {
          judge: judgeContent,
          thinkOn: "unset",
          think: "",
          content: "",
        };
      }
      // 兼容处理 没有的话 视为 think unset
      thinkOn = "unset";
    }
    else {
      thinkOn = thinkFlagRes[1] === "on" ? "on" : "off";
      restContent = restContent.slice((thinkFlagRes.index || 0) + thinkFlagRes[0].length).trimStart();
    }
  }
  else {
    restContent = restContent.trimStart();
    thinkOn = restContent.startsWith(THINK_OPEN_TAG) ? "on" : "unset";
  }

  // 处理 think 标签
  if (!restContent.startsWith(THINK_OPEN_TAG)) {
    if (THINK_OPEN_TAG.startsWith(restContent)) {
      // think 标签未开始: <thi...
      return {
        judge: judgeContent,
        thinkOn,
        think: "",
        content: "",
      };
    }
    // 非法, 无<think>标签
    return {
      judge: judgeContent,
      thinkOn,
      think: "",
      content: restContent.trimStart(),
    };
  }
  const thinkEndIndex = restContent.indexOf(THINK_CLOSE_TAG);
  if (thinkEndIndex === -1) {
    // think 标签未结束
    return {
      judge: judgeContent,
      thinkOn,
      think: restContent.slice(THINK_OPEN_TAG.length).trimStart(),
      content: "",
    };
  }
  const thinkContent = restContent.slice(THINK_OPEN_TAG.length, thinkEndIndex).trim();
  restContent = restContent.slice(thinkEndIndex + THINK_CLOSE_TAG.length).trimStart();
  return {
    judge: judgeContent,
    thinkOn,
    think: thinkContent,
    content: restContent,
  };
}
