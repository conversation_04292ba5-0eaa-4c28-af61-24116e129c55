import { UserMessage } from "./components/UserMessage";
import { AssisantMessage } from "./components/AssistantMessage";
import { ICachedMessage } from "@/utils/sessionUtils";
import { useRecordStore } from "@/store/record";

import WarningIcon from "@/assets/images/warning-bold.svg?react";
import { IChatModelType } from "@shared/types/business";
import { memo } from "react";
import { QAItem } from "@shared/types/chatHistory";

export interface QAProps {
  data: QAItem;
  section?: any;
  fullPath?: string;
  onResend: (modelType?: IChatModelType) => void;
  onLikeOrUnlike: (item: ICachedMessage, isLike?: boolean) => void;
  isLast?: boolean;
}

export const QAComponent = ({
  data,
  onLikeOrUnlike,
  onResend,
  isLast,
}: QAProps) => {
  const { Q, A } = data;
  const loadingStatu = useRecordStore(state => state.loadingStatu);
  return (
    <div className="flex flex-col gap-[16px]">
      <UserMessage data={Q} />
      <AssisantMessage
        data={A}
        onLikeOrUnlike={onLikeOrUnlike}
        onResend={onResend}
        isLast={isLast}
        qaItem={data}
      />
      {loadingStatu && loadingStatu.status !== "loading" && (
        <div
          className="flex items-center dialog-text-dark text-[13px] my-[8px]"
        >
          <div
            className="flex-1 h-[1px] dialog-separator-dark mr-[12px]"
          >
          </div>
          <div
            className="flex overflow-hidden flex-shrink-0 items-center max-w-full text-ellipsis"
          >
            <WarningIcon className="flex-shrink-0 icon-fill-dark mr-[4px]" />
            {loadingStatu.status === "timeout"
              ? "抱歉，当前访问人数较多，请稍后重试"
              : "抱歉，服务异常，请联系 oncall 解决"}
          </div>
          <div
            className="flex-1 h-[1px] dialog-separator-dark ml-[12px]"
          >
          </div>
        </div>
      )}
    </div>
  );
};

export const QA = memo(QAComponent);
