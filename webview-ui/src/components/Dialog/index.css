.dialog-text-dark{
  color: #F0F0F0;
}
.dialog-text-light{
  color: #212429;
}

.dialog-separator-light {
  background-color: #676D75;
}

.dialog-separator-dark {
  background-color: #8B949E;
}

.icon-dialog-fill-light {
  fill: #676D75;
}
.icon-dialog-fill-light:hover {
  fill: #212429;
}

.icon-dialog-fill-dark {
  fill: #8B949E;
}
.icon-dialog-fill-dark:hover {
  fill: #E5EBF1;
}

.dialog-border-light {
  border-bottom: 0.5px solid #DCE1E6;
}

.dialog-border-dark {
  border-bottom: 0.5px solid #40474E;
}

.dialog-footer-border-light {
  border-top: 0.5px solid #DCE1E6;
}

.dialog-footer-border-dark {
  border-top: 0.5px solid #40474E;
}

.dialog-item-bg-dark {
  border: 1px solid #40474E;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.10) 0%, rgba(255, 255, 255, 0.07) 100%);
}

.dialog-item-bg-light {
  background: #FFF;
  box-shadow: 0px 0px 2px 0px rgba(12, 18, 31, 0.12), 0px 2px 4px 0px rgba(12, 18, 31, 0.06);
}

.dialog-item-error-dark {
  color: #DA3633;
}

.dialog-item-error-light {
  color: #CF222E;
}

.dialog-code-light {
  background: #F7F9FA;
}

.dialog-code-dark {
  background: #0F161B;
}

.dialog-code-header-light {
  background: #EEF1F4;
}

.dialog-code-header-dark {
  background: #252D35;
}

.dialog-item-syntax-wrapper code {
  background-color: transparent;
  border-radius: 0 0 4px 4px;
}

.dialog-like-icon-light {
  fill: #F047AA;
}

.dialog-like-icon-dark {
  fill: #DB61A2;
}

.dialog-item-container pre.blog-pre+* {
  margin-top: 8px;
}

.dialog-item-container *+pre.blog-pre {
  margin-top: 8px;
}

.dialog-file-card {
  width: 100%;
}

.blog-pre {
  text-wrap: wrap;
}

.dialog-item-container ul,
.dialog-item-container li,
.dialog-item-container menu {
  list-style: unset;
  padding: unset;
  margin: unset;
}

.dialog-item-container ul {
  padding-left: 16px;
}

.content-title-text-dark {
  color: #F0F6FC;
}
.content-title-text-light {
  color: #1F2328;
}
.inline-dialog-item-bg-base-dark {
  background: #0F161B;
}
.inline-dialog-item-bg-base-light {
  background: #FFF;
}

.code-container > code {
  font-size: 12px !important;
}