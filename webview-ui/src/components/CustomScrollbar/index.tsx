import clsx from "clsx";
import { MacScrollbar } from "mac-scrollbar";
import "mac-scrollbar/dist/mac-scrollbar.css";

import css from "./index.module.less";
import { createContext, RefObject, useContext, useEffect, useRef } from "react";

interface CustomScrollBarProps {
  children: React.ReactNode;
  /** 禁止X轴滚动 */
  suppressScrollX?: boolean;
  /** 禁止Y轴滚动 */
  suppressScrollY?: boolean;
  className?: string;
  suppressAutoHide?: boolean;
  onWheel?: React.WheelEventHandler<HTMLElement>;
  scrollContainerCallback?: (ele: RefObject<HTMLDivElement>) => void;
  style?: React.CSSProperties;
}

const CustomScrollBarContext = createContext<{
  container: RefObject<HTMLElement>;
} | null>(null);

export function useCustomScrollBar() {
  const scrollbarContext = useContext(CustomScrollBarContext);
  if (!scrollbarContext) {
    throw new Error("useCustomScrollBar must be used within a CustomScrollBar");
  }
  return scrollbarContext;
}

export const CustomScrollBar: React.FC<CustomScrollBarProps> = (
  props: CustomScrollBarProps,
) => {
  const {
    suppressScrollX,
    suppressScrollY,
    children,
    className,
    suppressAutoHide,
    onWheel,
    scrollContainerCallback,
    style,
  } = props;
  const scrollContainer = useRef<HTMLDivElement>(null);

  useEffect(() => {
    scrollContainerCallback && scrollContainerCallback(scrollContainer);
    // FIXME: eslint
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CustomScrollBarContext.Provider value={{ container: scrollContainer }}>
      <MacScrollbar
        suppressScrollX={suppressScrollX}
        suppressScrollY={suppressScrollY}
        suppressAutoHide={suppressAutoHide}
        className={clsx(
          css["k-custom-ms"],
          className,
        )}
        style={style}
        onWheel={onWheel}
        ref={scrollContainer}
      >
        {children}
      </MacScrollbar>
    </CustomScrollBarContext.Provider>
  );
};
