.tips-light {
  --tips-border-color: #d0d5dc;
  --tips-bg-color: linear-gradient(92deg, #fff 6.43%, #d7f0ff 92.4%);
  --tips-bg-image: url("https://ali.a.yximgs.com/kos/nlav12119/woNnsfyP_2024-08-01-19-05-35.png");
  --tips-icon: url("https://ali.a.yximgs.com/kos/nlav12119/kqteCFGM_2024-08-01-19-01-22.svg");
  --tips-close-color: #676d75;
  --tips-close-hover-color: #212429;
  --tips-color: #262a2f;
  --tips-btn-color: #326bfb;
  --tips-btn-hover-color: #5c8fff;
  --tips-btn-icon: url("https://ali.a.yximgs.com/kos/nlav12119/SAfHhBBB_2024-08-02-18-17-19.png");
}
.tips-dark {
  --tips-border-color: #40474e;
  --tips-bg-color: linear-gradient(92deg, #000 6.49%, #002e92 95.65%);
  --tips-bg-image: url("https://ali.a.yximgs.com/kos/nlav12119/nmzjEnzn_2024-08-01-19-06-57.png");
  --tips-close-color: #8b949e;
  --tips-close-hover-color: #e5ebf1;
  --tips-color: #f0f6fc;
  --tips-btn-color: #588bfc;
  --tips-btn-hover-color: #4679fb;
  --tips-btn-icon: url("https://ali.a.yximgs.com/kos/nlav12119/HrxtqgkU_2024-08-02-18-16-20.png");
}
.tips-card {
  width: 100%;
  background-image: var(--tips-bg-color);
  border-top: 1px solid var(--tips-border-color);
  border-bottom: 1px solid var(--tips-border-color);
}
.tips-container {
  width: 100%;
  height: 48px;
  padding: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  /* background-color: var(--tips-bg-color); */
  background-image: var(--tips-bg-image);
  background-position: right center;
  background-repeat: no-repeat;
  background-size: auto 100%;
}

.tips-icon {
  width: 16px;
  min-width: 16px;
  height: 16px;
  margin: 0 8px;
  background: var(--tips-icon, url("https://ali.a.yximgs.com/kos/nlav12119/kqteCFGM_2024-08-01-19-01-22.svg"))
    center / 100% no-repeat;
}

.tips-content {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  color: var(--tips-color);
  overflow: hidden;
  font-family: "PingFang SC";
  text-align: left;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  user-select: none;
}
.tips-content-text {
  max-width: max-content;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.tips-btn-link {
  min-width: max-content;
  display: inline-block;
  color: var(--tips-btn-color);
  text-decoration: none;
  margin-left: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
}
.tips-btn-link:hover {
  color: var(--tips-btn-hover-color);
}
.tips-btn-link::after {
  content: "";
  width: 12px;
  height: 12px;
  display: inline-block;
  margin-left: 2px;
  background-color: var(--tips-btn-color);
  mask: url("https://ali.a.yximgs.com/kos/nlav12119/HrxtqgkU_2024-08-02-18-16-20.png")
    center / 100% no-repeat;
}
.tips-btn-link:hover::after {
  background-color: var(--tips-btn-hover-color);
}

.tips-close {
  width: 16px;
  height: 16px;
  position: relative;
  margin: 0 8px;
  cursor: pointer;
  background-color: var(--tips-close-color);
  mask: url("https://ali.a.yximgs.com/kos/nlav12119/qICzUbIW_2024-08-02-18-29-41.svg")
    center / 100% no-repeat;
}
.tips-close:hover {
  background-color: var(--tips-close-hover-color);
}
