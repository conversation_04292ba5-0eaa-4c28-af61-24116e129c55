import { useDesignToken } from "@/hooks/useDesignToken";
import { vsCss } from "@/style/vscode";
import { Alert, AlertDescription, chakra, Link } from "@chakra-ui/react";

export function WorkspaceUnavailableHint() {
  const { tokens } = useDesignToken();
  return (
    <div className=" px-3 py-3">
      <Alert
        status="warning"
        alignItems="start"
        borderRadius="md"
        boxShadow="lg"
        paddingEnd={8}
        textAlign="start"
        width="auto"
        textColor={vsCss.notificationsForeground}
        borderColor={vsCss.notificationToastBorder}
        borderWidth={1}
        borderStyle="solid"
        bgColor={vsCss.notificationsBackground}
        px="20px"
        py="10px"
      >
        <chakra.div flex="1" maxWidth="100%">
          <AlertDescription display="block">
            <span>暂不支持在 VSCode 工作区模式下使用智能体对话</span>
            <br />
            <Link
              color={tokens.colorIconBrandDefault}
              href="https://docs.corp.kuaishou.com/k/home/<USER>/fcADF8uHstyNaMu55twjio6FE?ro=false"
              target="_blank"
            >
              查看详情
            </Link>
          </AlertDescription>
        </chakra.div>
      </Alert>
    </div>
  );
}
