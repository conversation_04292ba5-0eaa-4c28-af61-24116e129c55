import WarningIcon from "@/assets/waring-blue.svg?react";
import CloseIcon from "@/assets/icons/close.svg?react";
import clsx from "clsx";
import { useCallback } from "react";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { saveActiveSessionIdToStorage } from "../activeSessionStorage";

interface IProps {
  onCancel: () => void;
}

export const IsNotCurrentWorkspace: React.FC<IProps> = (props) => {
  const { onCancel } = props;

  const onOpen = useCallback(() => {
    kwaiPilotBridgeAPI.clearTask();
    saveActiveSessionIdToStorage("");
  }, []);

  return (
    <div className="p-3 pb-0">
      <div className={clsx("px-4 py-3 border-[0.6px] border-commandCenter-inactiveBorder rounded-lg", "kwaipilot-checkpoint-confirm-block")}>
        <div className="flex items-center justify-between pb-3">
          <div className=" flex gap-[5px]">
            <WarningIcon className="translate-y-[1px]"></WarningIcon>
            <div className="text-text-common-primary leading-[18px] font-medium text-[13px]">非当前工作区对话</div>
          </div>
          <div onClick={onCancel} className="cursor-pointer">
            <CloseIcon></CloseIcon>
          </div>
        </div>
        <div className="pl-5  text-text-common-secondary leading-[20px] text-[13px]">
          该会话不属于当前工作区，为保障您的体验可以
          <span className="text-text-brand-default cursor-pointer" onClick={onOpen}> 新建会话</span>
          。
        </div>
      </div>
    </div>
  );
};
