import { Tooltip } from "antd";
import BackSpaceIcon from "@/assets/backspace.svg?react";
import clsx from "clsx";

interface IProps {
  children: React.ReactNode;
  onClick: (e: any) => void;
  hiddenCheckPointConfirmBlock?: boolean;
}
export function WithCheckPointButton(props: IProps) {
  const { children, onClick, hiddenCheckPointConfirmBlock } = props;

  return (
    <div className="relative group flex self-end">
      {children}
      <div className="">
        <Tooltip title="回退到本轮对话发起前" placement="topLeft">
          <div className={clsx({ "group-hover:flex": !hiddenCheckPointConfirmBlock }, "z-10 hidden cursor-pointer items-center  absolute bottom-0 -left-0 rounded border-[0.6px] border-border-common bg-[rgba(255,255,255,0.16)] backdrop-blur-[2px] px-[6px] py-[2px]  gap-1")} onClick={onClick}>
            <BackSpaceIcon></BackSpaceIcon>
            <div className="text-text-common-secondary text-[12px] leading-[18px] whitespace-nowrap ">回退到当前</div>
          </div>
        </Tooltip>
      </div>
    </div>
  );
}
