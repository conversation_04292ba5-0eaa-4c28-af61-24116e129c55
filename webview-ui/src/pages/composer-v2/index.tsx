import { HistoryBar } from "@/components/HistoryBar";
import VideoBg from "@/components/VideoBg";
import { useColorMode } from "@chakra-ui/react";
import { useCallback, useEffect, useRef, useState } from "react";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { NewComposerButton } from "./NewComposerButton";
import { withProviders } from "@udecode/cn";
import { ComposerSession } from "@/logics/composer/ComposerSession";
import {
  ComposerStateContextProvider,
  useComposerState,
} from "@/logics/composer/context/ComposerStateContext";
import { useBlocker, useSearchParams } from "react-router-dom";
import { IsNotCurrentWorkspace } from "./components/IsNotCurrentWorklspace";
import {
  LeaveConfirmDialog_DirtyWorkingSet,
  LeaveConfirmDialog_Streaming,
} from "./components/LeaveConfirmDialog";
import {
  getActiveSessionIdFromStorage,
  saveActiveSessionIdToStorage,
} from "./activeSessionStorage";
import { useUserStore } from "@/store/user";
import { NotLoggedHint } from "@/components/NotLoggedHint";
import {
  RestoreConfirmDialog,
  RestoreConfirmDialogContext,
} from "@/logics/composer/components/HumanMessage/RestoreConfirmDialog";
import {
  RestoreAndSendDialog,
  RestoreAndSendDialogContext,
} from "@/logics/composer/components/HumanMessage/RestoreAndSendDialog";
import {
  SendConfirmDialog,
  SendConfirmDialogContext,
} from "@/logics/composer/components/HumanMessage/SendConfirmDialog";
import { LocalServiceConnectionLostAlert } from "@/logics/composer/components/LocalServiceConnectionLostAlert";
import { LexicalEditor } from "lexical";
import Logo from "../home/<USER>";
import { useIdeEnv } from "@/hooks/useIdeEnv";
import clsx from "clsx";
import { useAsync } from "react-use";
import { WorkspaceUnavailableHint } from "./components/WorkspaceUnavailableHint";
import { useSubmit } from "@/logics/composer/useSubmit";
import ApplyStatus from "@/logics/composer/components/ApplyStatus";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { McpStatusBar } from "@/logics/composer/components/Mcp/McpStatusBar";
import { ModelSelector } from "@/logics/composer/components/ModelSelector";
import { ComposerUserInputTextarea } from "@/logics/composer/components/ComposerUserInputTextarea";

function WelcomeContent() {
  const [, isKwaiPilotIDE] = useIdeEnv();

  if (isKwaiPilotIDE) return null;

  return (
    <div className="w-full mt-[15vh] relative flex px-[16px] flex-col justify-start items-center overflow-auto pb-[48px]">
      <div className=" w-full  flex items-end justify-center relative">
        <Logo></Logo>
      </div>
    </div>
  );
}

const PageComposerBody = withProviders(
  RestoreAndSendDialogContext,
  RestoreConfirmDialogContext,
  SendConfirmDialogContext,
)(() => {
  const [
    isNotCurrentWorkspaceDialogShown,
    setIsNotCurrentWorkspaceDialogShown,
  ] = useState(false);

  const {
    isCurrentWorkspaceSession,
    setMessageScrollContainerHeight,
    localServiceConnectionLost,
    localMessages,
    isStreaming,
    sessionId,
    editingMessageTs,
  } = useComposerState();

  const isInWelcomeMode = localMessages.length === 0;

  const messageScrollContainer = useRef<HTMLDivElement>(null);

  const editorRef = useRef<LexicalEditor>(null);

  const openUserManual = useCallback(() => {
    kwaiPilotBridgeAPI.openUrl(
      "https://docs.corp.kuaishou.com/k/home/<USER>",
    );
  }, []);

  useEffect(() => {
    if (!messageScrollContainer.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setMessageScrollContainerHeight(entry.contentRect.height);
      }
    });

    resizeObserver.observe(messageScrollContainer.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [
    messageScrollContainer,
    setMessageScrollContainerHeight,
    isInWelcomeMode,
  ]);

  const onReadonlyMaskClick = useCallback(() => {
    if (editingMessageTs) {
      kwaiPilotBridgeAPI.extensionComposer.$setEditingMessageTs(undefined);
    }
  }, [editingMessageTs]);

  const { doSubmit } = useSubmit({
    role: "bottom",
  });

  const userInfo = useUserStore(state => state.userInfo);
  // 是否展示[停止生成

  const [, isKwaiPilotIDE] = useIdeEnv();

  const handleCloseIsNotCurrentWorkspaceDialog = useCallback(() => {
    setIsNotCurrentWorkspaceDialogShown(false);
  }, []);

  useEffect(() => {
    if (!isCurrentWorkspaceSession && !isInWelcomeMode) {
      setIsNotCurrentWorkspaceDialogShown(true);
    }
    else {
      setIsNotCurrentWorkspaceDialogShown(false);
    }
  }, [
    handleCloseIsNotCurrentWorkspaceDialog,
    isCurrentWorkspaceSession,
    isInWelcomeMode,
  ]);

  const isDeveloperMode = useBridgeObservableAPI("isDeveloperMode");

  const moreOpt = (
    <>
      <McpStatusBar />
      {isDeveloperMode ? <ModelSelector /> : null}
    </>
  );

  const scrollOuterClickHandler = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (e.target === e.currentTarget) {
        kwaiPilotBridgeAPI.extensionComposer.$setEditingMessageTs(undefined);
      }
    },
    [],
  );

  const stopCurrentTask = useCallback(() => {
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "stop",
      params: {
        sessionId,
      },
    });
  }, [sessionId]);

  if (!userInfo) {
    return <NotLoggedHint p={4} />;
  }
  return (
    <>
      {isInWelcomeMode
        ? (
            <WelcomeContent />
          )
        : (
            <div
              className="flex flex-col justify-between w-full h-full overflow-hidden"
              onClick={scrollOuterClickHandler}
            >
              <div
                ref={messageScrollContainer}
                className="overflow-y-scroll overscroll-none px-[24px] pt-3 flex text-text-main flex-col-reverse chat-dialog-container"
                id="composer-v2-message-scroll-container"
                style={{
                  scrollbarColor:
                "var(--vscode-scrollbarSlider-background) transparent",
                  scrollbarWidth: "thin",
                }}
              >
                <div className="w-full flex-auto">
                  <ComposerSession />
                </div>
              </div>
            </div>
          )}
      <div
        className={clsx(
          "flex-none relative",
          isKwaiPilotIDE && isInWelcomeMode
            ? "h-full flex flex-col justify-center"
            : "",
        )}
      >
        {isNotCurrentWorkspaceDialogShown && (
          <IsNotCurrentWorkspace
            onCancel={handleCloseIsNotCurrentWorkspaceDialog}
          >
          </IsNotCurrentWorkspace>
        )}

        <RestoreConfirmDialog />
        <RestoreAndSendDialog />
        <SendConfirmDialog />
        {localServiceConnectionLost && (
          <LocalServiceConnectionLostAlert px={6} mb={4} />
        )}
        {(isCurrentWorkspaceSession || isInWelcomeMode) && (
          <ComposerUserInputTextarea
            editorRef={editorRef}
            editorClassName={isInWelcomeMode ? "h-[80px]" : "min-h-[44px]"}
            wrapperClassName="p-3 pt-0"
            enable={
              isInWelcomeMode
                ? false
                : {
                    top: true,
                    right: false,
                    bottom: false,
                    left: false,
                    topRight: false,
                    bottomRight: false,
                    bottomLeft: false,
                    topLeft: false,
                  }
            }
            role="bottom"
            doSubmit={doSubmit}
            doStop={stopCurrentTask}
            isStreaming={isStreaming}
            sessionId={sessionId}
            isContextConsumer={/* 有正在编辑的历史消息, 则置后 */!editingMessageTs}
            onClick={onReadonlyMaskClick}
            applyStatusElement={<ApplyStatus />}
            moreOpt={moreOpt}
          />
        )}
        {isInWelcomeMode && !isKwaiPilotIDE && (
          <div className="mt-2 flex gap-[6px] text-[12px] justify-center leading-[18px] text-[#B4BCD0]">
            更多能力详见
            <div
              className="text-[#5AA7FF] cursor-pointer"
              onClick={openUserManual}
            >
              Kwaipilot使用手册
            </div>
          </div>
        )}
      </div>
    </>
  );
});

const InnerPageComposer = () => {
  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";
  const { sessionId, isStreaming, indeterminatedWorkingSetEffects, localMessages }
    = useComposerState();

  const isInWelcomeMode = localMessages.length === 0;

  const [searchParams] = useSearchParams();

  const [isLoading, setIsLoading] = useState(true);

  const userInfo = useUserStore(state => state.userInfo);

  useEffect(() => {
    const initSession = async () => {
      const sessionIdFromQuery = searchParams.get("sessionId");
      if (sessionIdFromQuery === sessionId) {
        setIsLoading(false);
        return;
      }
      // 由于getActiveSessionIdFromStorage是异步的，需要使用await
      let targetSessionId = sessionIdFromQuery;
      if (!targetSessionId) {
        targetSessionId = await getActiveSessionIdFromStorage();
      }
      if (targetSessionId) {
        saveActiveSessionIdToStorage(targetSessionId);
        setIsLoading(true);
        kwaiPilotBridgeAPI.extensionComposer
          .$showTaskWithId(targetSessionId)
          .finally(() => {
            setIsLoading(false);
          });
      }
      else {
        setIsLoading(false);
      }
    };

    initSession();
    // 这个 hook 作用是在 url 变化时更新 task，因此 sessionId 变化时 不应该执行
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);

  useEffect(() => {
    if (sessionId) {
      saveActiveSessionIdToStorage(sessionId);
    }
  }, [sessionId]);

  const stopCurrentTask = useCallback(() => {
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "stop",
      params: {
        sessionId,
      },
    });
  }, [sessionId]);

  const routeBlocker = useBlocker(
    () =>
      Boolean(userInfo)
      && (isStreaming || indeterminatedWorkingSetEffects.length > 0),
  );
  const [newComposerBlocked, setNewComposerBlocked] = useState(false);
  const onStreamingContinue = useCallback(() => {
    stopCurrentTask();
    if (routeBlocker.state === "blocked") {
      routeBlocker.proceed?.();
    }
    else if (newComposerBlocked) {
      setNewComposerBlocked(false);
      kwaiPilotBridgeAPI.clearTask();
      saveActiveSessionIdToStorage("");
    }
  }, [newComposerBlocked, routeBlocker, stopCurrentTask]);

  const showBlockDialog
    = routeBlocker.state === "blocked" || newComposerBlocked;
  const onDirtyWorkingSetContinue = useCallback(
    async (action: "accept" | "reject") => {
      if (action === "accept") {
        await kwaiPilotBridgeAPI.editor.keepDiff({ abortChat: true });
      }
      else {
        await kwaiPilotBridgeAPI.editor.undoDiff({ abortChat: true });
      }
      if (routeBlocker.state === "blocked") {
        routeBlocker.proceed?.();
      }
      else if (newComposerBlocked) {
        setNewComposerBlocked(false);
        kwaiPilotBridgeAPI.clearTask();
        saveActiveSessionIdToStorage("");
      }
    },
    [newComposerBlocked, routeBlocker],
  );

  const clearBlockState = useCallback(() => {
    routeBlocker.reset?.();
    setNewComposerBlocked(false);
  }, [routeBlocker]);

  const onNewComposerButtonClick = useCallback(() => {
    if (isStreaming || indeterminatedWorkingSetEffects.length > 0) {
      setNewComposerBlocked(true);
    }
    else {
      kwaiPilotBridgeAPI.clearTask();
      saveActiveSessionIdToStorage("");
    }
  }, [indeterminatedWorkingSetEffects.length, isStreaming]);

  const { value: workspaceFile } = useAsync(
    () => kwaiPilotBridgeAPI.extensionComposer.$getWorkspaceFile(),
    [],
  );

  const isInWorkspace = Boolean(workspaceFile);

  return (
    <div className="flex flex-col h-full min-w-[299px] overflow-scroll overflow-y-hidden bg-sideBar-background">
      {!isDark && <VideoBg />}
      <HistoryBar
        current="composer-v2"
        action={
          userInfo && !isInWorkspace && !isInWelcomeMode
            ? (
                <NewComposerButton onClick={onNewComposerButtonClick} />
              )
            : null
        }
      />
      {isLoading
        ? null
        : isInWorkspace
          ? (
              <WorkspaceUnavailableHint />
            )
          : (
              <PageComposerBody />
            )}
      {showBlockDialog
      && (indeterminatedWorkingSetEffects.length > 0
        ? (
            <LeaveConfirmDialog_DirtyWorkingSet
              indeterminatedWorkingSetFileNum={
                indeterminatedWorkingSetEffects.length
              }
              isOpen={showBlockDialog}
              onCancel={clearBlockState}
              onContinue={onDirtyWorkingSetContinue}
              onClose={clearBlockState}
            />
          )
        : (
            <LeaveConfirmDialog_Streaming
              isOpen={showBlockDialog}
              onCancel={clearBlockState}
              onContinue={onStreamingContinue}
              onClose={clearBlockState}
            />
          ))}
    </div>
  );
};

export const PageComposerV2 = withProviders(ComposerStateContextProvider)(
  InnerPageComposer,
);
