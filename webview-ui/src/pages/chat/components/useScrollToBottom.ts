import { useEventListener } from "@chakra-ui/react";
import { RefObject, useState, useCallback, useEffect } from "react";
import useResizeObserver from "use-resize-observer";

export function useScrollToBottom(
  scrollRef: RefObject<HTMLDivElement>,
  contentRef: RefObject<HTMLDivElement>,
) {
  const [autoScroll, setAutoScroll] = useState(true);

  const scrollDomToBottom = useCallback(() => {
    const dom = scrollRef.current;
    if (dom) {
      requestAnimationFrame(() => {
        setAutoScroll(true);
        dom.scrollTo(0, dom.scrollHeight);
      });
    }
  }, [scrollRef]);

  useEventListener(scrollRef.current, "scroll", () => {
    const isScrolledToBottom = scrollRef?.current
      ? Math.abs(
        scrollRef.current.scrollHeight
        - (scrollRef.current.scrollTop + scrollRef.current.clientHeight),
      ) <= 10
      : false;
    if (isScrolledToBottom) {
      setAutoScroll(true);
    }
    else {
      setAutoScroll(false);
    }
  });

  const { height: contentHeight } = useResizeObserver({ ref: contentRef });

  useEffect(() => {
    if (autoScroll) {
      scrollDomToBottom();
    }
  }, [autoScroll, scrollDomToBottom, contentHeight]);

  useEffect(() => {
    scrollDomToBottom();
  }, [scrollDomToBottom]);

  return {
    scrollRef,
    autoScroll,
    setAutoScroll,
    scrollDomToBottom,
  };
}
