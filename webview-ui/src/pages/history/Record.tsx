import AutoTooltip from "@/components/AutoTooltip";

import CancelIcon from "@/assets/icons/cancel.svg?react";
import CheckMarkIcon from "@/assets/icons/check-mark.svg?react";
import EditIcon from "@/assets/icons/edit.svg?react";
import TrashIcon from "@/assets/icons/trash.svg?react";
import NameInput from "./NameInput";
import { Tooltip } from "@/components/Union/chakra-ui";
import { useColorMode } from "@chakra-ui/react";
import { getRecordStoreByVendor } from "@/store/record";
import { BriefSessionItem } from "@shared/types/chatHistory";
import { useCallback, useEffect, useState } from "react";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useHistoryStore } from "@/store/history";
import { useStore } from "zustand";
import dayjs from "dayjs";
import { getActiveSessionIdFromStorage, saveActiveSessionIdToStorage } from "@/pages/composer-v2/activeSessionStorage";

type RecordProps = {
  record: BriefSessionItem;
  fullDate?: boolean;
  editingSession: BriefSessionItem | null;
  selectHistory: (record: BriefSessionItem) => void;
  setEditSession: (v: BriefSessionItem | null) => void;
  deleteHistory: (sessionId: string) => void;
};

const Record = ({
  record,
  editingSession,
  selectHistory,
  setEditSession,
  deleteHistory,
}: RecordProps) => {
  const { colorMode: theme } = useColorMode();
  const recordStoreChatVendor = getRecordStoreByVendor("chat");
  const activeChatSession = useStore(
    recordStoreChatVendor,
    s => s.activeSession,
  );
  const [activeComposerSession, setActiveComposerSession] = useState<string | undefined>();

  useEffect(() => {
    const fetchActiveSession = async () => {
      const sessionId = await getActiveSessionIdFromStorage();
      setActiveComposerSession(sessionId);
    };
    fetchActiveSession();
  }, []);
  const setHistoryList = useHistoryStore(state => state.setHistoryList);
  const historyList = useHistoryStore(state => state.historyList);
  const updateHistoryItemName = useHistoryStore(state => state.updateHistoryItemName);
  const [editingSessionName, setEditingSessionName] = useState(
    editingSession?.sessionName ?? "",
  );

  const isEditable = record.sessionId === editingSession?.sessionId;
  const isDark = theme === "dark";

  const resetEditingSession = () => {
    setEditSession(null);
    setEditingSessionName("");
  };
  const cancelEditing = () => {
    if (editingSession) {
      setEditingSessionName(editingSession.sessionName);
      resetEditingSession();
    }
  };
  const removeSession = async (record: BriefSessionItem) => {
    deleteHistory(record.sessionId);

    const recordStoreState = getRecordStoreByVendor(
      record.isComposer ? "composer" : "chat",
    ).getState();

    if (record.isComposerV2) {
      if (record.sessionId === activeComposerSession) {
        const nextSessionId = historyList.find(v => v.sessionId !== record.sessionId && v.isComposerV2);
        saveActiveSessionIdToStorage(nextSessionId?.sessionId ?? "");
      }
    }
    else if (record.sessionId === recordStoreState.activeSession) {
      const nextSessionId
        = historyList.find(
          h =>
            h.sessionId !== record.sessionId
            && Boolean(h.isComposer) === Boolean(record.isComposer)
            && !h.isComposerV2,
        )?.sessionId ?? "";
      recordStoreState.setActiveSession({
        value: nextSessionId,
        navigateToChat: false,
      });
    }
    const newHistoryList = historyList.filter(
      h => h.sessionId !== record.sessionId,
    );
    setHistoryList(newHistoryList);
    kwaiPilotBridgeAPI.deleteSession(record.sessionId);
  };
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEditingSessionName(value);
  };
  const submitNameChange = () => {
    if (
      editingSession
      && editingSessionName !== editingSession.sessionName
      && editingSessionName.trim() !== ""
    ) {
      updateHistoryItemName({
        sessionId: editingSession.sessionId,
        sessionName: editingSessionName,
      });
    }
  };

  const formatTimestamp = useCallback((timestamp: string) => {
    const ts = Number(timestamp);
    const date = dayjs(ts);
    const today = dayjs();

    // 判断是否是今天
    if (date.format("YYYY-MM-DD") === today.format("YYYY-MM-DD")) {
      // 如果是今天，返回时:分格式
      return date.format("HH:mm");
    }
    else {
      // 如果不是今天，返回年/月/日格式
      return date.format("YYYY/MM/DD");
    }
  }, []);

  return (
    <div
      className={`group text-foreground h-[36px] leading-[19.5px] text-[13px] w-full rounded-[4px] px-3 py-[6px] hover:bg-statusBarItem-remoteHoverBackground cursor-pointer bg-settings-focusedRowBackground flex items-center hover:border-solid hover:border-focusBorder hover:border border ${
        isEditable ? "border-text-brand-hover" : "border-transparent"
      }`}
      onClick={() => {
        selectHistory(record);
      }}
    >
      {isEditable
        ? (
            <div className="flex justify-between w-full items-center">
              <NameInput
                value={editingSessionName}
                onChange={handleChange}
                onSubmit={submitNameChange}
                reset={resetEditingSession}
                onCancel={cancelEditing}
              >
              </NameInput>
              <div
                className="flex gap-3"
                style={{
                  color: isDark ? "#808893" : "#676D75",
                }}
              >
                <Tooltip placement="top" label="取消">
                  <div
                    className="w-[14px] h-[14px] flex items-center justify-center text-icon-common-secondary"
                    onMouseDown={e => e.preventDefault()}
                    onClick={(e) => {
                      e.stopPropagation();
                      cancelEditing();
                    }}
                  >
                    <CancelIcon />
                  </div>
                </Tooltip>
                <Tooltip placement="top" label="确定">
                  <div
                    className="w-[14px] h-[14px] flex items-center justify-center text-icon-common-secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      submitNameChange();
                    }}
                  >
                    <CheckMarkIcon />
                  </div>
                </Tooltip>
              </div>
            </div>
          )
        : (
            <div className="flex justify-between gap-2 items-center w-full text-foreground  group-hover:text-foreground">
              <div className="flex flex-nowrap items-center gap-2">
                <AutoTooltip title={record.sessionName} lineClamp={1}>
                  {record.sessionName}
                </AutoTooltip>
                <div
                  className="bg-[var(--custom-menu-trigger-bg)] px-[4px] py-[1px] rounded-[4px] leading-[18px]  text-foreground shrink-0"
                >
                  {record.isComposer ? "智能体" : "问答"}
                </div>
                {(record.sessionId === activeChatSession
                  || record.sessionId == activeComposerSession) && (
                  <div
                    className="py-[3px] rounded-[4px] leading-[18px] text-tab-inactiveForeground shrink-0"
                  >
                    {record.sessionId === activeChatSession
                      ? "· 当前"
                      : record.sessionId === activeComposerSession
                        ? "· 当前"
                        : "unknown"}
                  </div>
                )}
              </div>
              <div className="group-hover:hidden shrink-0 text-tab-inactiveForeground">
                {formatTimestamp(record.sessionTime)}
              </div>
              <div className="group-hover:flex hidden items-center gap-3">
                <Tooltip placement="top" label="编辑">
                  <div
                    className="text-icon-common-secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditSession(record);
                      setEditingSessionName(record.sessionName);
                    }}
                  >
                    <EditIcon />
                  </div>
                </Tooltip>
                <Tooltip placement="top" label="删除">
                  <div
                    className=" text-icon-common-secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeSession(record);
                    }}
                  >
                    <TrashIcon />
                  </div>
                </Tooltip>
              </div>
            </div>
          )}
    </div>
  );
};

export default Record;
