import { CustomScrollBar } from "@/components/CustomScrollbar";
import { HistoryBar } from "@/components/HistoryBar";
import HistoryContent from "./Content";
import ClearHistory from "./ClearHistory";

const History = () => {
  return (
    <div className="flex flex-col  h-full min-w-[299px] overflow-scroll overflow-y-hidden bg-sideBar-background">
      <HistoryBar current="history" action={<ClearHistory />} />
      <div className="flex flex-col justify-between w-full h-full overflow-hidden">
        <CustomScrollBar className="overflow-y-scroll overscroll-none px-[12px] pt-3">
          <HistoryContent />
        </CustomScrollBar>
      </div>
    </div>
  );
};

export default History;
