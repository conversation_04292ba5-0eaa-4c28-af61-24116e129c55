import { useEffect, useRef } from "react";

interface NameInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: () => void;
  reset: () => void;
  onCancel: (e?: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
}
const NameInput: React.FC<NameInputProps> = (props: NameInputProps) => {
  const nameInputRef = useRef<HTMLInputElement>(null);
  const { value, onChange, onSubmit, reset, onCancel } = props;
  const onBlur = () => {
    onSubmit();
    reset();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      onBlur();
      return;
    }
    else if (e.key === "Escape") {
      e.preventDefault();
      onCancel();
      return;
    }
  };
  useEffect(() => {
    nameInputRef.current?.focus();
  }, []);

  return (
    <input
      value={value}
      onInput={onChange}
      ref={nameInputRef}
      onKeyDown={handleKeyDown}
      className="w-full pl-1 bg-transparent"
      onBlur={onBlur}
      style={{
        outline: "none",
        border: "none",
      }}
      onClick={e => e.stopPropagation()}
    >
    </input>
  );
};

export default NameInput;
