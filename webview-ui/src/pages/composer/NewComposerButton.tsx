import NewDialogIcon from "@/assets/icons/new-dialog.svg?react";
import { useRecordStore } from "@/store/record";
import { Tooltip } from "@/components/Union/chakra-ui";
import { DEFAULT_MODEL_TYPE } from "@/constant";

export const NewComposerButton: React.FC = () => {
  const setActiveSession = useRecordStore(state => state.setActiveSession);
  const setChatModelType = useRecordStore(state => state.setChatModelType);

  const createNewDialog = () => {
    // TODO: 埋点
    // collectClick("VS_CREATE_OR_CHANGE_CHAT");
    setActiveSession({ value: "", navigateToChat: false });
    setChatModelType(DEFAULT_MODEL_TYPE);
  };

  return (
    <Tooltip>
      <div
        onClick={createNewDialog}
        className="overflow-hidden flex gap-1 items-center p-[2px] cursor-pointer rounded text-icon-common-secondary hover:text-text-brand-default"
      >
        <NewDialogIcon className="w-[13.5px] h-[13.5px]" />
        <div
          className="text-[13px] leading-[20px] whitespace-nowrap truncate flex items-center"
        >
          新会话
        </div>
      </div>
    </Tooltip>
  );
};
