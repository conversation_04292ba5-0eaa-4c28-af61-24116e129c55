import { useInlineChatStore } from "@/store/inline-chat";
import { reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import { useColorMode } from "@chakra-ui/react";
import { useState } from "react";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useNavigate } from "react-router-dom";

export const Header = () => {
  const { colorMode: theme } = useColorMode();
  const [hoverIcon, setHoverIcon] = useState<boolean>(false);
  const clearInlineChatHistory = useInlineChatStore(
    state => state.clearInlineChatHistory,
  );
  const navigate = useNavigate();
  return (
    <div className="w-full h-[32px] px-[16px] py-[4px] flex items-center justify-between">
      <div
        className={`font-normal text-[13px] content-title-text-${theme} line-height-[18px]`}
      >
        自然语言生成代码
      </div>
      <div
        onClick={() => {
          const param: ReportOpt<"inline_chat"> = {
            key: "inline_chat",
            type: "close",
          };
          reportUserAction(param);
          clearInlineChatHistory();
          kwaiPilotBridgeAPI.clearInlineInfo();
          navigate("/composer-v2");
        }}
        className="w-[16px] h-[16px] flex items-center justify-center cursor-pointer"
        onMouseEnter={() => setHoverIcon(true)}
        onMouseLeave={() => setHoverIcon(false)}
      >
        <svg
          width="9"
          height="9"
          viewBox="0 0 10 10"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M0.720073 0.72C0.860693 0.57955 1.05132 0.50066 1.25007 0.50066C1.44882 0.50066 1.63944 0.57955 1.78007 0.72L5.00007 3.94L8.22007 0.72C8.28877 0.64631 8.37157 0.58721 8.46357 0.54622C8.55557 0.50523 8.65487 0.48319 8.75557 0.48141C8.85627 0.47963 8.95627 0.49816 9.04967 0.53588C9.14307 0.5736 9.22787 0.62974 9.29907 0.70096C9.37037 0.77218 9.42647 0.85702 9.46417 0.9504C9.50187 1.04379 9.52047 1.14382 9.51867 1.24452C9.51687 1.34523 9.49487 1.44454 9.45387 1.53654C9.41287 1.62854 9.35377 1.71134 9.28007 1.78L6.06007 5L9.28007 8.22C9.35377 8.2887 9.41287 8.3715 9.45387 8.4635C9.49487 8.5555 9.51687 8.6548 9.51867 8.7555C9.52047 8.8562 9.50187 8.9562 9.46417 9.0496C9.42647 9.143 9.37037 9.2278 9.29907 9.299C9.22787 9.3703 9.14307 9.4264 9.04967 9.4641C8.95627 9.5018 8.85627 9.5204 8.75557 9.5186C8.65487 9.5168 8.55557 9.4948 8.46357 9.4538C8.37157 9.4128 8.28877 9.3537 8.22007 9.28L5.00007 6.06L1.78007 9.28C1.63789 9.4125 1.44984 9.4846 1.25554 9.4812C1.06124 9.4777 0.875862 9.399 0.738442 9.2616C0.601032 9.1242 0.522323 8.9388 0.518893 8.7445C0.515463 8.5502 0.587593 8.3622 0.720073 8.22L3.94007 5L0.720073 1.78C0.579623 1.63938 0.500732 1.44875 0.500732 1.25C0.500732 1.05125 0.579623 0.86063 0.720073 0.72Z"
            fill={
              theme === "dark"
                ? hoverIcon
                  ? "#E5EBF1"
                  : "#8B949E"
                : hoverIcon
                  ? "#676D75"
                  : "#212429"
            }
          />
        </svg>
      </div>
    </div>
  );
};
