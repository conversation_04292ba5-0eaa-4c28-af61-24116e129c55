import { MainLogo } from "@/components/MainLogo";
import { useColorMode } from "@chakra-ui/react";

const IconMap = {
  dark: {
    buttonUp: (
      <img src="https://ali.a.yximgs.com/kos/nlav12119/BGfjeeae_2024-08-29-15-58-26.png" />
    ),
    buttonCommand: (
      <img src="https://ali.a.yximgs.com/kos/nlav12119/EBfNKJyq_2024-08-29-15-58-45.png" />
    ),
    buttonK: (
      <img src="https://ali.a.yximgs.com/kos/nlav12119/yxlRBbpg_2024-08-29-15-58-59.png" />
    ),
    codeIntro: (
      <img src="https://ali.a.yximgs.com.com/kos/nlav10725/kwaipilot/dark-code-v1.gif" />
    ),
    qaIntro: (
      <img src="https://ali.a.yximgs.com.com/kos/nlav10725/kwaipilot/dark-qa-v1.gif" />
    ),
  },
  light: {
    buttonUp: (
      <img src="https://ali.a.yximgs.com/kos/nlav12119/OQWnyIEY_2024-08-29-15-56-37.png" />
    ),
    buttonCommand: (
      <img src="https://ali.a.yximgs.com/kos/nlav12119/mupYsGPM_2024-08-29-15-57-34.png" />
    ),
    buttonK: (
      <img src="https://ali.a.yximgs.com/kos/nlav12119/vXryjOQu_2024-08-29-15-58-01.png" />
    ),
    codeIntro: (
      <img src="https://ali.a.yximgs.com/kos/nlav10725/kwaipilot/light-code-v1.gif" />
    ),
    qaIntro: (
      <img src="https://ali.a.yximgs.com.com/kos/nlav10725/kwaipilot/light-qa-v1.gif" />
    ),
  },
};

export default function Logo() {
  const { colorMode: theme } = useColorMode();
  return (
    <div className="relative main flex flex-col items-center justify-end">
      <MainLogo></MainLogo>
      <div className="w-[100%] mt-[24px] flex-col justify-start items-center gap-3 inline-flex">
        <div className="rounded-lg justify-center items-start gap-2.5 inline-flex">
          <div className="flex justify-center items-center w-[40px] h-[40px]">
            {IconMap[theme].buttonUp}
          </div>
          <div
            className="inline-flex justify-center items-center w-[40px] h-[40px]"
          >
            {IconMap[theme].buttonCommand}
          </div>
          <div className="flex justify-center items-center w-[40px] h-[40px]">
            {IconMap[theme].buttonK}
          </div>
        </div>
      </div>
    </div>
  );
}
