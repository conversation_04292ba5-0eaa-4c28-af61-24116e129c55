// 保证 globalObject 存在
import "@/bridge";
import "@/utils/logger";
import { beforeAll, vi } from "vitest";

vi.hoisted(() => {
  class MockedWebviewBridge {
    private handlers = new Map();
    private messageListeners = new Set<any>();
    private callbacks = new Map();
    private callbackId = 0;
    private mockResponses = new Map();

    constructor() {
    // 初始化状态
    }

    /**
     * 调用原生处理程序
     */
    callHandler(handlerName: string, data: any, callback: any) {
      const callbackId = this.callbackId++;
      if (callback) {
        this.callbacks.set(callbackId, callback);
      }

      // 如果有模拟响应，直接调用回调
      if (callback && this.mockResponses.has(handlerName)) {
        const response = this.mockResponses.get(handlerName);
        callback(typeof response === "function" ? response(data) : response);
      }
    }

    /**
     * 注册处理程序
     */
    registerHandler(name: string, handler: any) {
      this.handlers.set(name, handler);
      return this; // 支持链式调用
    }

    /**
     * 发送单向消息
     */
    postMessage(message: any) {
    // 如果有监听器，通知消息监听器
      if (this.messageListeners.size > 0) {
        this.messageListeners.forEach((listener) => {
          listener(message);
        });
      }
    }

    /**
     * 添加消息监听器
     */
    addMessageListener(listener: any) {
      this.messageListeners.add(listener);
    }

    /**
     * 移除消息监听器
     */
    removeMessageListener(listener: any) {
      this.messageListeners.delete(listener);
    }

    // 测试辅助方法 - 设置模拟响应
    mockHandlerResponse(handlerName: string, response: any) {
      this.mockResponses.set(handlerName, response);
    }

    // 测试辅助方法 - 触发已注册的处理程序
    triggerHandler(name: string, data: any) {
      const handler = this.handlers.get(name);
      if (handler) {
        const responseData = JSON.stringify(data);
        return handler(responseData);
      }
      return null;
    }

    // 测试辅助方法 - 模拟回调调用
    mockCallback(callbackId: number, data: any) {
      const callback = this.callbacks.get(callbackId);
      if (callback) {
        callback(typeof data === "string" ? data : JSON.stringify(data));
        return true;
      }
      return false;
    }

    // 测试辅助方法 - 触发消息监听
    triggerMessage(eventName: string, payload: any) {
      if (this.messageListeners.size > 0) {
        const message = {
          protocol: "message",
          event: eventName,
          payload,
        };
        this.messageListeners.forEach((listener) => {
          listener(message);
        });
        return true;
      }
      return false;
    }

    // 获取处理程序列表
    getHandlers() {
      return Array.from(this.handlers.keys());
    }

    // 获取消息监听器数量
    getListenerCount() {
      return this.messageListeners.size;
    }

    // 清除所有模拟状态
    reset() {
      this.handlers.clear();
      this.messageListeners.clear();
      this.callbacks.clear();
      this.mockResponses.clear();
      this.callbackId = 0;
      return this; // 支持链式调用
    }
  }
  // 创建一个增强的 WebviewBridge 模拟实现
  vi.stubGlobal("bridge", new MockedWebviewBridge());
});

beforeAll(() => {
  global.ResizeObserver = class ResizeObserver {
    observe() {
      // do nothing
    }

    unobserve() {
      // do nothing
    }

    disconnect() {
      // do nothing
    }
  };
});
