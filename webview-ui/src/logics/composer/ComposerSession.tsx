import { useMemo } from "react";
import { ComposerConversation } from "./ComposerConversation";
import { useComposerState } from "./context/ComposerStateContext";
import { combineCommandSequences, combineMcpSequences, InternalLocalMessage, isHumanMessage, LocalMessage } from "shared/lib/agent";
import { ComposerTaskRow } from "./ComposerTaskRow";
import { Flex } from "@chakra-ui/react";
import { ComposerConversationContextProvider } from "./context/ComposerConversationContext";
import { createPlainTextEditorState } from "@/components/TextArea/lexical/editorState";

/**
 * 主要是过滤 browser message
 * @param msg
 * @returns
 */
function filterValidMessages(msg: LocalMessage | LocalMessage[]): msg is LocalMessage;
function filterValidMessages(msg: InternalLocalMessage | InternalLocalMessage[]): msg is InternalLocalMessage;
function filterValidMessages(msg: LocalMessage | InternalLocalMessage | LocalMessage[] | InternalLocalMessage[]): boolean {
  return !Array.isArray(msg);
}

interface ConversationStructure {
  humanMessage: InternalLocalMessage;
  taskRows: InternalLocalMessage[];
}

export function ComposerSession() {
  const { localMessages: messages } = useComposerState();
  const modifiedMessages = useMemo(() => combineMcpSequences(combineCommandSequences(messages)), [messages]);
  const visibleMessages = useMemo(() => {
    return modifiedMessages.filter((message) => {
      const ask = message.ask;
      const say = message.say;
      if (message.type === "ask") {
        if (!ask) {
          return false;
        }
        switch (ask) {
          case "api_req_failed": // this message is used to update the latest api_req_started that the request failed
          case "followup":
            return true;
          case "command":
            return true;
          case "command_output":
            return true;
          case "use_mcp_tool":
            return true;
          case "tool":
            return true;
          case "mistake_limit_reached":
            return true;
          default:{
            const _exhaustiveCheck: never = ask;
            console.log("exhaustiveCheck", _exhaustiveCheck);
            return false;
          }
        }
      }
      else if (message.type === "say") {
        if (!say) {
          return false;
        }
        switch (say) {
          case "api_req_started":
          // cline 会展示 api 开始，但我们不需要
            return false;
          case "checkpoint_created":
          // 检查点创建，我们不需要
            return false;
          case "api_req_finished": // combineApiRequests removes this from modifiedMessages anyways
          case "api_req_retried": // this message is used to update the latest api_req_started that the request was retried
          // case "deleted_api_reqs": // aggregated api_req metrics from deleted messages
            return false;
          case "text":
          // Sometimes cline returns an empty text message, we don't want to render these. (We also use a say text for user messages, so in case they just sent images we still render that)
            if ((message.text ?? "") === "") {
              return false;
            }
            return true;
          case "edit_file_result":
            return false;
          case "command_output":
            // 经过 combine 之后不会出现 command_output 但还是枚举一下
            return false;
          case "error":
            return true;
          case "completion_result":
            return true;
          case "command":
            return true;
          case "tool":
            return true;
          case "task":
            return true;
          case "user_feedback":
            return true;
          case "api_req_failed":
          case "tool_error":
            return true;
          case "use_mcp_tool_result":
            // 经过 combine 之后不会出现 use_mcp_tool_result 但还是枚举一下
            return false;
          default:{
            const _exhaustiveCheck: never = say;
            console.log("exhaustiveCheck", _exhaustiveCheck);
            return true;
          }
        }
      }
      return true;
    });
  }, [modifiedMessages]);
  const groupedMessages = useMemo<InternalLocalMessage[]>(() => {
    // cline 中 groupedMessage 主要用来处理 browser 类型的消息， 我们这里不需要 原样返回
    return visibleMessages.filter<InternalLocalMessage>(filterValidMessages);
  }, [visibleMessages]);

  /**
   * 游离的消息， 不包含在 conversation 中，其实预期不应该出现这种情况，但调试阶段经常有消息被覆盖，导致找不到所归属的 humanMessage
   */
  const detatchedTaskMessages = useMemo(() => {
    const firstHumanMessageIndex = groupedMessages.findIndex(message => isHumanMessage(message));
    if (firstHumanMessageIndex === -1) {
      return groupedMessages;
    }
    return groupedMessages.slice(0, firstHumanMessageIndex);
  }, [groupedMessages]);

  /* conversation = humanMessage + taskRows */
  const conversations = useMemo(() => {
    const result: ConversationStructure[] = [];
    const firstMessage = groupedMessages.at(0);
    if (!firstMessage) {
      return result;
    }
    for (const message of groupedMessages) {
      if (isHumanMessage(message)) {
        result.push({
          humanMessage: message,
          taskRows: [],
        });
      }
      else {
        const lastConversation = result.at(-1);
        if (lastConversation) {
          lastConversation.taskRows.push(message);
        }
      }
    }
    return result;
  }, [groupedMessages]);
  console.log("conversations", detatchedTaskMessages, conversations, messages);
  return (
    <Flex direction="column" gap="16px">
      {detatchedTaskMessages.length
        ? (
            <ComposerConversationContextProvider
              humanMessage={{
                role: "user",
                ts: 0,
                sessionId: "",
                type: "say",
                text: "",
                editorState: createPlainTextEditorState(""),
                contextItems: [],
                diagnostics: [],
              }}
              taskRows={detatchedTaskMessages}
              isLastConversation={false}
            >
              <div>
                {detatchedTaskMessages.map((message, index) => (
                  <ComposerTaskRow key={message.ts} message={message} isLast={!conversations.length && index === detatchedTaskMessages.length - 1} />
                ))}
                <div className="text-center text-xs text-gray-500">
                  以上消息未找到所归属的对话，联系 oncall 解决
                </div>
              </div>
            </ComposerConversationContextProvider>
          )
        : null}
      {conversations.map((conversation, index) => (
        <ComposerConversation
          key={conversation.humanMessage.ts}
          {...conversation}
          isLastConversation={index === conversations.length - 1}
        />
      ))}

    </Flex>
  );
}
