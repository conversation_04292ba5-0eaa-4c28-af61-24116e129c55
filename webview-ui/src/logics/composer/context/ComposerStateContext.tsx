import { kwaiPilotBridgeAPI } from "@/bridge";
import { useKwaipilotBridgeMessageListener } from "@/hooks/useKwaipilotBridgeMessageListener";
import { DOM } from "@/utils/dom";
import { WEBVIEW_BRIDGE_EVENT_NAME } from "@shared/types/bridge";
import { createContext, useCallback, useContext, useMemo, useState } from "react";
import { Ask, ComposerState, InternalLocalMessage, Say } from "shared/lib/agent";
import { SupportedModelEnum } from "shared/lib/agent/supportedModels";

export interface ComposerDerivedState {
  isStreaming: boolean;
}

interface ComposerStateContextType extends ComposerState, ComposerDerivedState {
  updateSessionSummary: (summary: string) => void;
  isCurrentWorkspaceSession: boolean;
  messageScrollContainerHeight: number;
  setMessageScrollContainerHeight: (height: number) => void;
}

const EndSayMessage: Say[] = ["completion_result", "error"];

const ComposerStateContext = createContext<ComposerStateContextType | undefined>(undefined);

export function ComposerStateContextProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<ComposerState>({
    localMessages: [],
    workspaceUri: "",
    sessionId: "",
    currentTaskInterrupted: false,
    indeterminatedWorkingSetEffects: [],
    isCurrentWorkspaceSession: true,
    editingMessageTs: undefined,
    currentMessageTs: undefined,
    userPreferredModel: SupportedModelEnum.claude3,
    localServiceConnectionLost: false,
  });

  const scrollToBottom = useCallback(() => {
    const scrollContainer = DOM.$("composer-v2-message-scroll-container");
    if (scrollContainer) {
      // 定义安全距离（像素）
      const safeDistance = 10;

      // 由于使用了 flex-col-reverse，滚动位置是反向的
      // scrollTop 为 0 表示在底部，scrollHeight - clientHeight - scrollTop 为 0 表示在顶部
      // 当前距离底部的距离
      const distanceFromBottom = scrollContainer.scrollTop;

      // 如果距离底部的距离小于安全距离，则滚动到底部
      if (Math.abs(distanceFromBottom) <= safeDistance) {
        scrollContainer.scrollTop = 0;
      }
    }
  }, []);

  const onComposerStateUpdate = useCallback((newState: ComposerState) => {
    setState(newState);
  }, []);
  useKwaipilotBridgeMessageListener(WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_STATE_UPDATE, onComposerStateUpdate);

  const onComposerPartialMessage = useCallback(({ partialMessage }: { partialMessage: InternalLocalMessage }) => {
    setState((prevState) => {
      // worth noting it will never be possible for a more up-to-date message to be sent here or in normal messages post since the presentAssistantContent function uses lock
      const lastIndex = prevState.localMessages.findLastIndex(msg => msg.ts === partialMessage.ts);
      if (lastIndex !== -1) {
        const newLocalMessages = [...prevState.localMessages];
        newLocalMessages[lastIndex] = partialMessage;
        return { ...prevState, localMessages: newLocalMessages };
      }
      return prevState;
    });
    scrollToBottom();
  }, [scrollToBottom]);
  useKwaipilotBridgeMessageListener(WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_PARTIAL_MESSAGE, onComposerPartialMessage);

  const lastMessage = useMemo(() => state.localMessages.at(-1), [state.localMessages]);

  const currentAsking = useMemo<Ask | undefined>(() => {
    if (!lastMessage) {
      return undefined;
    }
    if (lastMessage.type === "say") {
      return undefined;
    }
    return lastMessage.ask;
  }, [lastMessage]);

  const isStreaming = useMemo(() => {
    const lastMessage = state.localMessages.at(-1);

    if (currentAsking && !lastMessage?.partial) {
      return false;
    }
    if (state.currentTaskInterrupted) {
      return false;
    }

    if (lastMessage?.type === "say" && lastMessage.say && !EndSayMessage.includes(lastMessage.say)) {
      // 最后一条是 api_req_start 表示一定没有完成
      return true;
    }
    const isLastMessagePartial = lastMessage?.partial === true;
    if (isLastMessagePartial) {
      return true;
    }

    return false;
  }, [currentAsking, state.currentTaskInterrupted, state.localMessages]);

  const [messageScrollContainerHeight, setMessageScrollContainerHeight] = useState(0);

  const contextValue: ComposerStateContextType = {
    ...state,
    isStreaming,
    updateSessionSummary: (summary: string) => {
      kwaiPilotBridgeAPI.updateComposerSessionName({
        sessionId: state.sessionId,
        name: summary,
      });
    },
    messageScrollContainerHeight,
    setMessageScrollContainerHeight,
  };

  return (
    <ComposerStateContext.Provider value={contextValue}>
      {children}
    </ComposerStateContext.Provider>
  );
}

export const useComposerState = () => {
  const context = useContext(ComposerStateContext);
  if (context === undefined) {
    throw new Error("useComposerState must be used within an ComposerStateContextProvider");
  }
  return context;
};
