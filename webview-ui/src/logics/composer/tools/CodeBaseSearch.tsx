import { SayTool } from "shared/lib/agent";
import { Icon } from "./components/Icon";
import { Title } from "./components/Title";
import { useComposerTaskContext } from "../context/ComposerTaskContext";
import { Loading } from "./components/loading";
import { SubTitle } from "./components/SubTitle";
import { Error } from "./components/Error";
import { useMemo } from "react";
import { Box, Flex, FlexProps, useDisclosure } from "@chakra-ui/react";
import KidIcon from "@/components/Union/kid";

import IconArrowDown from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_smallarrow_down";
import { getIcon } from "@/utils/fileIcon";
import { Icon as FileIcon } from "@/components/Union/t-iconify";
import clsx from "clsx";
import AutoTooltip from "@/components/AutoTooltip";
import { uniqBy } from "lodash";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { FilenameDisplay } from "./components/FilenameDisplay";

interface IProps {
  tool: SayTool;
}

interface CodeSearchItem {
  _sub_node: boolean;
  code_content: string;
  metadata: {
    callees: string[];
    callers: string[];
    code_type: string;
    file_path: string;
    language: string;
    name: string;
    signature: string;
  };
  node_id: string;
  sub_node_id: number;
}

function CodeSearchItemUI({ item, ...rest }: { item: CodeSearchItem } & FlexProps) {
  const filename = item.metadata.file_path?.split("/").pop();

  return (
    <Flex
      rounded="4px"
      align="center"
      gap={1}
      py="2px"
      px={1}
      {...rest}
      className="hover:bg-list-hoverBackground cursor-pointer"
      onClick={() => kwaiPilotBridgeAPI.extensionComposer.$locateByPath(item.metadata.file_path)}
    >
      <div className=" flex-none w-[14px] h-[14px] float-none">
        <FileIcon icon={getIcon(filename || "", false)} width={14} height={14}></FileIcon>
      </div>
      <span className=" flex-none max-w-[200px] whitespace-nowrap text-text-common-secondary text-[12px] leading-[18px] flex">
        <FilenameDisplay filename={filename || ""}></FilenameDisplay>

      </span>
      <AutoTooltip label={item.metadata.file_path}>
        <span className=" text-text-common-tertiary text-[12px] leading-[18px]">
          {item.metadata.file_path}
        </span>
      </AutoTooltip>
    </Flex>
  );
}

export const CodeBaseSearch = (props: IProps) => {
  const tool = props.tool;
  const { isToolExecuting, isToolError } = useComposerTaskContext();
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: false });

  const codeSearchResult = useMemo(() => {
    if (isToolExecuting || isToolError) {
      return [];
    }
    try {
      const raw = JSON.parse(tool.content || "{}").code_context_list as CodeSearchItem[];
      return uniqBy(raw, "metadata.file_path");
    }
    catch (error) {
      return [];
    }
  }, [isToolError, isToolExecuting, tool.content]);

  const extra = useMemo(() => {
    return `${codeSearchResult.length}个相关结果`;
  }, [codeSearchResult.length]);

  const shouldShowResult = !isToolError && !isToolExecuting;

  return (
    <div
      className={clsx(
        "w-full rounded-lg border-radio-inactiveBorder border-[0.6px] overflow-hidden bg-statusBarItem-remoteHoverBackground",
      )}
    >
      {/* header */}
      <Flex align="center" className="h-[34px] hover:bg-list-hoverBackground cursor-pointer" px={3} gap={1} onClick={() => shouldShowResult && onToggle()}>
        <Icon type="search"></Icon>
        <Title type={tool.tool}></Title>
        {isToolExecuting
          ? <Loading></Loading>
          : isToolError
            ? <Error></Error>
            : tool.query && <SubTitle content={tool.query} extra={extra}></SubTitle>}
        <Flex align="center" w="14px" h="14px" justify="center" ml="auto" className={clsx(!isOpen && " -rotate-90")}>
          <KidIcon config={IconArrowDown} size={14} color="#8A94A6" />
        </Flex>
      </Flex>
      {isOpen && (
        <Box px={3} maxH="120px" overflowY="auto">
          {Array.isArray(codeSearchResult) && codeSearchResult.length > 0
            ? codeSearchResult.map((item: CodeSearchItem, i) => (
                <CodeSearchItemUI key={i} mt="2px" item={item} />
              ))
            : (
                <div className=" text-center py-2 text-text-common-tertiary text-[12px] leading-[18px]">
                  暂无结果
                </div>
              )}
        </Box>
      )}
    </div>
  );
};
