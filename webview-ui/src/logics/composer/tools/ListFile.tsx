import { SayTool } from "shared/lib/agent";
import { Icon } from "./components/Icon";
import { Title } from "./components/Title";
import { useComposerTaskContext } from "../context/ComposerTaskContext";
import { Loading } from "./components/loading";
import { SubTitle } from "./components/SubTitle";
import { Error } from "./components/Error";
import { useMemo } from "react";
import { Box, Flex, FlexProps, useDisclosure } from "@chakra-ui/react";
import KidIcon from "@/components/Union/kid";

import IconArrowDown from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_smallarrow_down";
import { getIcon } from "@/utils/fileIcon";
import { Icon as FileIcon } from "@/components/Union/t-iconify";
import clsx from "clsx";
import AutoTooltip from "@/components/AutoTooltip";
import { basename, join } from "path-browserify";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { FilenameDisplay } from "./components/FilenameDisplay";
import { useAsync } from "react-use";

interface IProps {
  tool: SayTool;
}

function FileItemUI({ path, ...rest }: { path: string } & FlexProps) {
  const filename = basename(path);
  const isDir = path.endsWith("/");
  const handleFileItemClick = () => {
    if (!isDir) {
      kwaiPilotBridgeAPI.extensionComposer.$locateByPath(path);
    }
  };
  return (
    <Flex
      rounded="4px"
      align="center"
      gap={1}
      py="2px"
      px={1}
      {...rest}
      className="hover:bg-list-hoverBackground cursor-pointer"
      onClick={handleFileItemClick}
    >
      <div className=" flex-none w-[14px] h-[14px] float-none">

        <FileIcon icon={getIcon(filename || "", isDir)} width={14} height={14}></FileIcon>
      </div>
      <span className=" flex-none max-w-[200px] whitespace-nowrap text-text-common-secondary text-[12px] leading-[18px] flex">
        <FilenameDisplay filename={filename || ""}></FilenameDisplay>
      </span>
      <AutoTooltip label={path}>
        <span className=" text-text-common-tertiary text-[12px] leading-[18px]">
          {path}
        </span>
      </AutoTooltip>
    </Flex>
  );
}

export const ListFile = (props: IProps) => {
  const tool = props.tool;
  const { isToolExecuting, isToolError } = useComposerTaskContext();
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: false });

  const { value: workspaceInfo } = useAsync(() => kwaiPilotBridgeAPI.getWorkspaceUri());

  const extra = useMemo(() => {
    let len = 0;
    try {
      const arr = JSON.parse(tool.content || "[]");
      len = arr?.length;
    }
    // eslint-disable-next-line no-empty
    catch (error) {}
    if (len) {
      return `${len}个相关结果`;
    }
    return "";
  }, [tool.content]);

  const fileList = useMemo(() => {
    if (isToolExecuting || isToolError) {
      return false;
    }
    try {
      const isRoot = workspaceInfo?.result && tool.path === basename(workspaceInfo.result);
      /*
      如果是 root 的话, 返回格式为 path: your-root  list: some-file
      否则,返回格式为   path: your-path/to/your-file  list: some-file
      */
      const base = isRoot ? "" : tool.path || "";
      return (JSON.parse(tool.content || "[]") as string[]).map(v => join(base, v));
    }
    catch (error) {
      return false;
    }
  }, [isToolError, isToolExecuting, tool.content, tool.path, workspaceInfo?.result]);

  const shouldShowResult = !isToolError && !isToolExecuting;

  return (
    <div
      className={clsx(
        "w-full rounded-lg border-radio-inactiveBorder border-[0.6px] overflow-hidden bg-statusBarItem-remoteHoverBackground",
      )}
    >
      {/* header */}
      <Flex align="center" className="h-[34px] hover:bg-list-hoverBackground cursor-pointer" px={3} gap={1} onClick={() => shouldShowResult && onToggle()}>
        <Icon type="search"></Icon>
        <Title type={tool.tool}></Title>
        {isToolExecuting
          ? <Loading></Loading>
          : isToolError
            ? <Error></Error>
            : tool.path && <SubTitle content={tool.path} extra={extra}></SubTitle>}
        <Flex align="center" w="14px" h="14px" justify="center" ml="auto" className={clsx(!isOpen && " -rotate-90")}>
          <KidIcon config={IconArrowDown} size={14} color="#8A94A6" />
        </Flex>
      </Flex>
      {isOpen && (
        <Box px={3} maxH="120px" overflowY="auto">
          {Array.isArray(fileList) && fileList.length > 0
            ? fileList.map((path, i) => (
                <FileItemUI key={i} mt="2px" path={path} />
              ))
            : (
                <div className=" text-center py-2 text-text-common-tertiary text-[12px] leading-[18px]">
                  暂无结果
                </div>
              )}
        </Box>
      )}
    </div>
  );
};
