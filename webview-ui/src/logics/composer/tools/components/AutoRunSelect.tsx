import { useAutoRunSetting } from "@/store/autoRunSetting";
import { Popover, Portal } from "@/components/Union/chakra-ui";
import { Button, ChakraProvider, extendTheme, PopoverContent, PopoverTrigger, useDisclosure } from "@chakra-ui/react";

import { Icon } from "@/components/Union/t-iconify";

import { useCallback } from "react";
import { vsCss } from "@/style/vscode";
import { ButtonStyle } from "@/utils/theme";

const theme = extendTheme({
  components: {
    Button: ButtonStyle,
  },
});

export function AutoRunSelect({
  handleEnableChange,
}: {
  handleEnableChange?: (enabled: boolean) => void;
}) {
  const autoRunEnabled = useAutoRunSetting(s => s.autoRunEnabled);
  const autoRunMcpEnabled = useAutoRunSetting(s => s.autoRunMcpEnabled);
  const setAutoRunEnabled = useAutoRunSetting(s => s.setAutoRunEnabled);

  const {
    isOpen,
    onClose,
    onOpen,
  } = useDisclosure();
  const {
    isOpen: isConfirmOpen,
    onOpen: onConfirmOpen,
    onClose: onConfirmClose,
  } = useDisclosure();
  const handleSelect = useCallback(async (enabled: boolean) => {
    if (enabled === autoRunEnabled) {
      return;
    }
    if (enabled) {
      onConfirmOpen();
    }
    else {
      await setAutoRunEnabled(false);
      handleEnableChange?.(false);
      onClose();
    }
  }, [autoRunEnabled, handleEnableChange, onClose, onConfirmOpen, setAutoRunEnabled]);

  const onConfirm = useCallback(async () => {
    await setAutoRunEnabled(true);
    handleEnableChange?.(true);
    onConfirmClose();
    onClose();
  }, [setAutoRunEnabled, handleEnableChange, onConfirmClose, onClose]);

  return (
    <Popover
      closeOnBlur={true}
      isOpen={isOpen}
      placement="bottom-end"
      onOpen={onOpen}
      onClose={() => {
        onClose();
      }}
    >
      <PopoverTrigger>
        <button className="flex ml-auto whitespace-nowrap items-center gap-1 text-foreground text-[12px] hover:cursor-pointer">
          <span>
            {autoRunEnabled ? "自动运行" : "运行前询问"}
          </span>
          <Icon icon="codicon:chevron-down" width="16" height="16" />
        </button>
      </PopoverTrigger>
      <Portal>
        <PopoverContent maxW="95vw" _focusVisible={{ outline: "none" }} bg={vsCss.dropdownBackground} width="128px" p="2px">
          <Popover isOpen={isConfirmOpen} placement="bottom-end" onClose={onConfirmClose}>
            <PopoverTrigger>
              <div>
                <button
                  className=" flex w-full items-center justify-between hover:cursor-pointer hover:bg-editorHoverWidget-background text-foreground px-2 py-1"
                  onClick={() => { handleSelect(false); }}
                >
                  运行前询问
                  {autoRunEnabled ? null : <Icon icon="codicon:check" width="14" height="14" />}
                </button>
                <button
                  className=" flex w-full items-center justify-between hover:cursor-pointer hover:bg-editorHoverWidget-background text-foreground px-2 py-1"
                  onClick={() => { handleSelect(true); }}
                >
                  自动运行
                  {autoRunEnabled ? <Icon icon="codicon:check" width="14" height="14" /> : null}
                </button>
              </div>
            </PopoverTrigger>
            <PopoverContent maxW="95vw" bgColor={vsCss.notificationsBackground} width="370px" border="none" p={4} boxShadow="0px 4px 24px 0px #0000005C,0px -4px 8px 0px #0000003D">
              <div className=" flex items-center font-medium text-foreground text-[13px] gap-2">
                <Icon icon="codicon:info" width="16" height="16" />
                <span className="text-[13px]">
                  切换为自动运行
                </span>
              </div>
              <div className="py-2 text-foreground text-[13px]">
                开启后，智能体将自动执行除黑名单外的命令
                {autoRunMcpEnabled ? "和 mcp" : ""}
                ，请注意可能的潜在安全风险
              </div>
              <div className="pt-2 flex items-center justify-end gap-2">
                <ChakraProvider theme={theme}>
                  <Button variant="secondary" height="28px" onClick={onConfirmClose}>取消</Button>
                  <Button variant="blueSolid" height="28px" onClick={onConfirm}>确认</Button>
                </ChakraProvider>
              </div>
            </PopoverContent>
          </Popover>
        </PopoverContent>
      </Portal>
    </Popover>
  );
}

export function AutoRunMcpSelect({
  handleEnableChange,
}: {
  handleEnableChange?: (enabled: boolean) => void;
}) {
  const autoRunMcp = useAutoRunSetting(s => s.autoRunEnabled && s.autoRunMcpEnabled);
  const setAutoRunMcpEnabled = useAutoRunSetting(s => s.setAutoRunMcpEnabled);
  const autoRunEnabled = useAutoRunSetting(s => s.autoRunEnabled);
  const setAutoRunEnabled = useAutoRunSetting(s => s.setAutoRunEnabled);

  const {
    isOpen,
    onClose,
    onOpen,
  } = useDisclosure();
  const {
    isOpen: isConfirmOpen,
    onOpen: onConfirmOpen,
    onClose: onConfirmClose,
  } = useDisclosure();
  const handleSelect = useCallback(async (enabled: boolean) => {
    if (enabled === autoRunMcp) {
      return;
    }
    if (enabled) {
      onConfirmOpen();
    }
    else {
      await setAutoRunMcpEnabled(false);
      handleEnableChange?.(false);
      onClose();
    }
  }, [autoRunMcp, handleEnableChange, onClose, onConfirmOpen, setAutoRunMcpEnabled]);

  const onConfirm = useCallback(async () => {
    if (!autoRunEnabled) {
      // 如果启用MCP自动运行，但未启用自动运行，则先启用
      await setAutoRunEnabled(true);
    }
    // 更新设置
    await setAutoRunMcpEnabled(true);
    handleEnableChange?.(true);
    onConfirmClose();
    onClose();
  }, [autoRunEnabled, setAutoRunMcpEnabled, handleEnableChange, onConfirmClose, onClose, setAutoRunEnabled]);

  return (
    <Popover
      closeOnBlur={true}
      isOpen={isOpen}
      placement="bottom-end"
      onOpen={onOpen}
      onClose={() => {
        onClose();
      }}
    >
      <PopoverTrigger>
        <button className="flex ml-auto whitespace-nowrap items-center gap-1 text-foreground text-[12px] hover:cursor-pointer">
          <span>
            {autoRunMcp ? "自动运行" : "运行前询问"}
          </span>
          <Icon icon="codicon:chevron-down" width="16" height="16" />
        </button>
      </PopoverTrigger>
      <Portal>
        <PopoverContent maxW="95vw" _focusVisible={{ outline: "none" }} bg={vsCss.dropdownBackground} width="128px" p="2px">
          <Popover isOpen={isConfirmOpen} placement="bottom-end" onClose={onConfirmClose}>
            <PopoverTrigger>
              <div>
                <button
                  className=" flex w-full items-center justify-between hover:cursor-pointer hover:bg-editorHoverWidget-background text-foreground px-2 py-1"
                  onClick={() => { handleSelect(false); }}
                >
                  运行前询问
                  {autoRunMcp ? null : <Icon icon="codicon:check" width="14" height="14" />}
                </button>
                <button
                  className=" flex w-full items-center justify-between hover:cursor-pointer hover:bg-editorHoverWidget-background text-foreground px-2 py-1"
                  onClick={() => { handleSelect(true); }}
                >
                  自动运行
                  {autoRunMcp ? <Icon icon="codicon:check" width="14" height="14" /> : null}
                </button>
              </div>
            </PopoverTrigger>
            <PopoverContent maxW="95vw" bgColor={vsCss.notificationsBackground} width="370px" border="none" p={4} boxShadow="0px 4px 24px 0px #0000005C,0px -4px 8px 0px #0000003D">
              <div className=" flex items-center font-medium text-foreground text-[13px] gap-2">
                <Icon icon="codicon:info" width="16" height="16" />
                <span className="text-[13px]">
                  切换为自动运行
                </span>
              </div>
              <div className="py-2 text-foreground text-[13px]">
                开启后，智能体将自动执行mcp和除黑名单外的命令，请注意可能的潜在安全风险
              </div>
              <div className="pt-2 flex items-center justify-end gap-2">
                <ChakraProvider theme={theme}>
                  <Button variant="secondary" height="28px" onClick={onConfirmClose}>取消</Button>
                  <Button variant="blueSolid" height="28px" onClick={onConfirm}>确认</Button>
                </ChakraProvider>
              </div>
            </PopoverContent>
          </Popover>
        </PopoverContent>
      </Portal>
    </Popover>
  );
}
