import { GrepSearchResult, GrepSearchResultItem, SayTool } from "shared/lib/agent";
import { Icon } from "./components/Icon";
import { Title } from "./components/Title";
import { useComposerTaskContext } from "../context/ComposerTaskContext";
import { Loading } from "./components/loading";
import { SubTitle } from "./components/SubTitle";
import { Error } from "./components/Error";
import { useMemo } from "react";
import { Box, Flex, FlexProps, useDisclosure } from "@chakra-ui/react";
import KidIcon from "@/components/Union/kid";

import IconArrowDown from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_smallarrow_down";
import { getIcon } from "@/utils/fileIcon";
import { Icon as FileIcon } from "@/components/Union/t-iconify";
import clsx from "clsx";
import AutoTooltip from "@/components/AutoTooltip";
import { uniqBy } from "lodash";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { FilenameDisplay } from "./components/FilenameDisplay";

interface IProps {
  tool: SayTool;
}

function GrepSearchItemUI({ item, ...rest }: { item: GrepSearchResultItem } & FlexProps) {
  const filename = item.filePath?.split("/").pop();
  return (
    <Flex
      rounded="4px"
      align="center"
      gap={1}
      py="2px"
      px={1}
      {...rest}
      className="hover:bg-list-hoverBackground cursor-pointer"
      onClick={() => kwaiPilotBridgeAPI.extensionComposer.$locateByPath(item.filePath)}
    >
      <div className=" flex-none w-[14px] h-[14px] float-none">
        <FileIcon icon={getIcon(filename || "", filename?.endsWith("/") || false)} width={14} height={14}></FileIcon>
      </div>
      <span className=" flex-none max-w-[200px] whitespace-nowrap text-text-common-secondary text-[12px] leading-[18px] flex">
        <FilenameDisplay filename={filename || ""}></FilenameDisplay>
      </span>
      <AutoTooltip label={item.filePath}>
        <span className=" text-text-common-tertiary text-[12px] leading-[18px]">
          {item.filePath}
        </span>
      </AutoTooltip>
    </Flex>
  );
}

export const GrepSearch = (props: IProps) => {
  const tool = props.tool;
  const { isToolExecuting, isToolError } = useComposerTaskContext();

  const grepResult = useMemo(() => {
    if (isToolExecuting || isToolError) {
      return false;
    }
    try {
      return JSON.parse(tool.content || "{}") as GrepSearchResult;
    }
    catch (error) {
      return false;
    }
  }, [isToolError, isToolExecuting, tool.content]);

  const flattenGrepResult = useMemo<GrepSearchResultItem[]>(() => {
    if (grepResult) {
      const result = Object.values(grepResult).flat();
      return uniqBy(result, "filePath");
    }
    return [];
  }, [grepResult]);

  const extra = useMemo(() => {
    return `${flattenGrepResult.length}个相关结果`;
  }, [flattenGrepResult.length]);

  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: false });

  const shouldShowResult = !isToolError && !isToolExecuting;

  return (
    <div
      className={clsx(
        "w-full  rounded-lg  border-radio-inactiveBorder border-[0.6px] overflow-hidden bg-statusBarItem-remoteHoverBackground",
      )}
    >
      {/* header */}
      <Flex align="center" className="h-[34px] hover:bg-list-hoverBackground cursor-pointer" px={3} gap={1} onClick={() => shouldShowResult && onToggle()}>
        <Icon type="search"></Icon>
        <Title type={tool.tool}></Title>
        {isToolExecuting
          ? <Loading></Loading>
          : isToolError
            ? <Error></Error>
            : tool.regex && <SubTitle content={tool.regex} extra={extra}></SubTitle>}
        <Flex align="center" w="14px" h="14px" justify="center" ml="auto" className={clsx(!isOpen && " -rotate-90")}>
          <KidIcon config={IconArrowDown} size={14} color="#8A94A6" />
        </Flex>
      </Flex>
      {isOpen && (
        <Box px={3} maxH="120px" overflowY="auto">
          {flattenGrepResult?.length
            ? flattenGrepResult.map(item => (
                <GrepSearchItemUI mt="2px" item={item} />
              ))
            : (
                <div className=" text-center py-2 text-text-common-tertiary text-[12px] leading-[18px]">
                  暂无结果
                </div>
              )}
        </Box>
      )}
    </div>
  );
};
