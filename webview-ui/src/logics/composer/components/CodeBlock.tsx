import { Markdown<PERSON>ode<PERSON>enderer } from "@/components/QA/components/MarkdownCodeRender";
import { memo, useEffect } from "react";
import { useRemark } from "react-remark";
import { visit } from "unist-util-visit";

/*
overflowX: auto + inner div with padding results in an issue where the top/left/bottom padding renders but the right padding inside does not count as overflow as the width of the element is not exceeded. Once the inner div is outside the boundaries of the parent it counts as overflow.
https://stackoverflow.com/questions/60778406/why-is-padding-right-clipped-with-overflowscroll/77292459#77292459
this fixes the issue of right padding clipped off
“ideal” size in a given axis when given infinite available space--allows the syntax highlighter to grow to largest possible width including its padding
minWidth: "max-content",
*/

interface CodeBlockProps {
  source?: string;
  forceWrap?: boolean;
}

function StyledMarkdown({ forceWrap, children }: { forceWrap: boolean; children: React.ReactNode }) {
  return <div className={`markdown-body ${forceWrap ? "forceWrap" : ""}`}>{children}</div>;
}

function StyledPre({ children }: { children: React.ReactNode }) {
  return <pre className="blog-pre">{children}</pre>;
}

const CodeBlock = memo(({ source, forceWrap = false }: CodeBlockProps) => {
  const [reactContent, setMarkdownSource] = useRemark({
    remarkPlugins: [
      () => {
        return (tree) => {
          visit(tree, "code", (node: any) => {
            if (!node.lang) {
              node.lang = "javascript";
            }
            else if (node.lang.includes(".")) {
              // if the language is a file, get the extension
              node.lang = node.lang.split(".").slice(-1)[0];
            }
          });
        };
      },
    ],
    rehypePlugins: [
    ],
    rehypeReactOptions: {
      components: {
        pre: ({ node: _node, ...preProps }: any) => <StyledPre {...preProps} />,
        code: ({ node: _node, ...codeProps }: any) => <MarkdownCodeRenderer {...codeProps} />,
      },
    },
  });

  useEffect(() => {
    console.log("source", source);
    setMarkdownSource(source || "");
  }, [source, setMarkdownSource]);
  console.log("reactContent", reactContent);

  return (
    <div
      style={{
        overflowY: forceWrap ? "visible" : "auto",
        maxHeight: forceWrap ? "none" : "100%",
        backgroundColor: "var(--vscode-editor-background, --vscode-sideBar-background, rgb(30 30 30))",
      }}
    >
      <StyledMarkdown forceWrap={forceWrap}>{reactContent}</StyledMarkdown>
    </div>
  );
});

export default CodeBlock;
