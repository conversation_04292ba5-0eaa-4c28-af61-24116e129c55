import { kwai<PERSON>ilotBridgeAPI } from "@/bridge";
import { useCallback, useMemo } from "react";
import { useComposerState } from "../context/ComposerStateContext";
import { Tooltip } from "@/components/Union/chakra-ui";
import { Flex, useBoolean } from "@chakra-ui/react";
import clsx from "clsx";
import { isToolEditFileMessage } from "shared/lib/agent/isToolMessage";
import { FileStateUIType, InternalLocalMessage_Tool_EditFile } from "shared/lib/agent";
import { getIcon } from "@/utils/fileIcon";
import { Icon } from "@/components/Union/t-iconify";
import IconReapply from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_refresh";
import IconReject from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_close";
import IconAccept from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_correct";
import IconArrowDown from "@kid/enterprise-icon/icon/output/common/system/common_system_arrowxia";
import KidIcon from "@/components/Union/kid";

import AutoTooltip from "@/components/AutoTooltip";
import { CustomScrollBar } from "@/components/CustomScrollbar";

function IconDot({ className }: { className?: string }) {
  return (
    <svg width="8" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
      <circle cx="4" cy="4.70728" r="2" fill="currentColor" />
    </svg>
  );
}

function IconBreathingDot({ className }: { className?: string }) {
  return (
    <svg width="9" height="9" viewBox="0 0 9 9" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
      <circle opacity="0.5" cx="4.5" cy="4.71094" r="3" fill="#FFBB26" />
      <circle cx="4.5" cy="4.71094" r="2" fill="#FFBB26" />
    </svg>
  );
}

const ApplyStatus = () => {
  const { isStreaming, localMessages, indeterminatedWorkingSetEffects } = useComposerState();
  const editFileMessages = useMemo(() => localMessages.filter<InternalLocalMessage_Tool_EditFile>(isToolEditFileMessage), [localMessages]);
  const fileList = useMemo< {
    iconType: string ;
    path: string;
    filename: string;
    status: FileStateUIType;
    languageId: string;
    message: InternalLocalMessage_Tool_EditFile;
  }[]>(() => {
    const fileSet/* path->message */ = new Map<string, InternalLocalMessage_Tool_EditFile[]>();
    for (const message of editFileMessages) {
      if (message.workingSetEffect?.path) {
        fileSet.set(message.workingSetEffect.path, [...(fileSet.get(message.workingSetEffect.path) || []), message]);
      }
    }
    const indeterminatedSet = new Map(indeterminatedWorkingSetEffects.map(v => [v.messageTs, v]));
    return Array.from(fileSet.values()).map((messages) => {
      const indeterminatedMessage = messages.find(v => indeterminatedSet.has(v.ts));
      // 有中间态，优先取中间态，否则取最后一条消息  将来添加版本信息后 取最新版本的
      const bestMatched = indeterminatedMessage || messages.at(-1);
      if (!bestMatched) {
        throw new Error("bestMatched is null");
      }
      return {
        ...bestMatched.workingSetEffect,
        iconType: getIcon(bestMatched.workingSetEffect?.path.split("/").pop() ?? "", false),
        status: indeterminatedSet.get(bestMatched.ts)?.state || bestMatched.workingSetEffect?.status,
        message: bestMatched,
        filename: bestMatched.workingSetEffect?.path.split("/").pop() ?? "",
      };
    });
  }, [editFileMessages, indeterminatedWorkingSetEffects]);

  const handleAcceptAll = useCallback(() => {
    kwaiPilotBridgeAPI.editor.keepDiff();
  }, []);
  const handleRejectAll = useCallback(() => {
    kwaiPilotBridgeAPI.editor.undoDiff();
  }, []);

  const handleReapplyClick = useCallback((e: React.MouseEvent, message: InternalLocalMessage_Tool_EditFile) => {
    e.stopPropagation();
    kwaiPilotBridgeAPI.editor.applyFile({
      message,
    });
  }, []);

  const handleAcceptSingleClick = useCallback((e: React.MouseEvent, message: InternalLocalMessage_Tool_EditFile) => {
    e.stopPropagation();
    kwaiPilotBridgeAPI.editor.keepDiff({ filepath: message.workingSetEffect?.path });
  }, []);

  const handleRejectSingleClick = useCallback((e: React.MouseEvent, message: InternalLocalMessage_Tool_EditFile) => {
    e.stopPropagation();
    kwaiPilotBridgeAPI.editor.undoDiff({ filepath: message.workingSetEffect?.path });
  }, []);

  const [collapsed, { toggle: toggleCollapsed }] = useBoolean(true);

  const workingSetHasAnyFile = useMemo(() => {
    return fileList.length > 0;
  }, [fileList]);

  const finalState = useMemo<FileStateUIType>(() => {
    if (fileList.length === 0) {
      return "init";
    }
    if (indeterminatedWorkingSetEffects.some(v => v.state === "applying")) {
      // 只要有一个 applying 就判定为 applying
      return "applying";
    }
    if (fileList.some(v => v.status === "accepted")) {
      // 只要有一个 accepted 且不是（applied） 就判定为 accepted
      return "accepted";
    }
    if (editFileMessages.every(v => v.workingSetEffect?.status === "rejected")) {
      return "rejected";
    }
    if (workingSetHasAnyFile) {
      return "applied";
    }
    return "init";
  }, [editFileMessages, fileList, indeterminatedWorkingSetEffects, workingSetHasAnyFile]);

  const onClickHeader = useCallback(() => {
    if (workingSetHasAnyFile) {
      toggleCollapsed();
    }
  }, [workingSetHasAnyFile, toggleCollapsed]);

  const toBeDeterminatedFileNum = useMemo(() => {
    return new Set(
      indeterminatedWorkingSetEffects
        .filter(v => v.state === "applied")
        .map(v => v.path))
      .size;
  }, [indeterminatedWorkingSetEffects]);

  if (!isStreaming && !workingSetHasAnyFile) {
    return null;
  }
  return (
    <div className={clsx(
      " w-[calc(100%-16px)] m-auto rounded-t-[8px] border-solid border border-commandCenter-inactiveBorder border-b-0 bg-input-background text-editorWidget-foreground",
    )}
    >
      {/* header */}
      <Flex align="center" className=" px-2 h-[38px] w-full text-[12px] cursor-pointer" gap={1} onClick={onClickHeader}>
        {workingSetHasAnyFile && (
          <KidIcon
            size={12}
            config={IconArrowDown}
            className={clsx("hover:text-icon-brand-hover", collapsed ? "-rotate-90" : "")}
          >
          </KidIcon>
        )}
        {isStreaming
          ? (
              <Flex align="center" gap={1}>
                <span>生成中</span>
              </Flex>
            )
          : finalState === "applying"
            ? (
                <div className="text-input-text-placeholder">
                  应用中
                  {toBeDeterminatedFileNum > 0 && (
                    <span>
                      待审查&nbsp;
                      {toBeDeterminatedFileNum}
                          &nbsp;个文件
                    </span>
                  )}
                </div>
              )
            : finalState === "applied"
              ? (
                  <div className="text-descriptionForeground">
                    待审查&nbsp;
                    {toBeDeterminatedFileNum}
                          &nbsp;个文件
                  </div>
                )
              : finalState === "accepted"
                ? (
                    <div className="text-descriptionForeground">
                      共变更&nbsp;
                      {fileList.length || 0}
                          &nbsp;个文件
                    </div>
                  )
                : finalState === "rejected"
                  ? (
                      <div className=" text-[#ED5140]">
                        共变更&nbsp;
                        {fileList.length}
                        &nbsp;个文件&nbsp;(已拒绝)
                      </div>
                    )

                  : null}
        {toBeDeterminatedFileNum > 0 && (
          <div className="flex items-center justify-center gap-[7px] ml-auto">
            <div
              className="flex items-center leading-[18px] font-medium justify-center px-[6px] py-[2px] text-button-secondaryForeground bg-button-secondaryBackground hover:bg-button-secondaryHoverBackground rounded-[4px] cursor-pointer"
              onClick={handleRejectAll}
            >
              <div className="text-[12px]">全部拒绝</div>
            </div>
            <div
              className="flex items-center leading-[18px] font-medium justify-center px-[6px] py-[2px]  text-button-foreground bg-button-background hover:bg-button-hoverBackground rounded-[4px] cursor-pointer"
              onClick={handleAcceptAll}
            >
              <div className="text-[12px]">全部接受</div>
            </div>
          </div>
        )}
      </Flex>
      {/* body */}
      {!collapsed && workingSetHasAnyFile && (
        <CustomScrollBar className="px-1 pb-1 overflow-auto max-h-[128px] border-solid border-t border-commandCenter-inactiveBorder">
          {fileList.map(v => (
            <Flex
              align="center"
              gap={1}
              key={v.path}
              className={clsx(" cursor-pointer h-[26px] rounded hover:bg-list-hoverBackground", "flex gap-2")}
              mt="2px"
              onClick={() => kwaiPilotBridgeAPI.editor.openFileToEditorMaybeDiffEditor(v.path)}
            >
              <Icon icon={v.iconType} width={16} height={16} className="flex-none" />
              <AutoTooltip className="" title={v.filename} lineClamp={1}>
                {v.filename}
              </AutoTooltip>
              {/* 状态 */}
              {v.status === "accepted"
                ? <KidIcon config={IconAccept} size={12} color="#27C241" className=" flex-none"></KidIcon>
                : v.status === "rejected"
                  ? <KidIcon config={IconReject} size={12} color="#ED5140" className=" flex-none"></KidIcon>
                  : v.status === "applying"
                    ? <IconBreathingDot className=" flex-none" />
                    : v.status === "applied"
                      ? <IconDot className="text-[#27C241] flex-none" />
                      : null}
              <Flex align="center" gap={2} ml="auto" pr="6px">
                {v.status === "applied"
                  ? (
                      <>
                        <Tooltip label="拒绝" placement="top">
                          <div>
                            <KidIcon
                              size={12}
                              config={IconReject}
                              className="text-icon-foreground"
                              onClick={e => handleRejectSingleClick(e, v.message)}
                            >
                            </KidIcon>
                          </div>
                        </Tooltip>
                        <Tooltip label="接受" placement="top">

                          <div>
                            <KidIcon
                              size={12}
                              config={IconAccept}
                              className="text-icon-foreground"
                              onClick={e => handleAcceptSingleClick(e, v.message)}
                            >
                            </KidIcon>
                          </div>
                        </Tooltip>
                      </>
                    )

                  : v.status === "applying"
                    ? null
                    : (
                        <Tooltip label="重新应用" placement="top">
                          <div>
                            <KidIcon
                              size={12}
                              config={IconReapply}
                              className="!text-icon-foreground"
                              onClick={e => handleReapplyClick(e, v.message)}
                            >
                            </KidIcon>
                          </div>
                        </Tooltip>
                      )}
              </Flex>
            </Flex>
          ))}
        </CustomScrollBar>
      )}
    </div>
  );
};

export default ApplyStatus;
