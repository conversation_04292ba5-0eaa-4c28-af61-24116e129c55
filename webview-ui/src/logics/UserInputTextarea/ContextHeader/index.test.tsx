import { MentionNodeV2 } from "@/components/TextArea/lexical/MentionNodeV2/MentionNodeV2";
import { createEditor, LexicalEditor } from "lexical";
import { createRef, forwardRef, useImperativeHandle, useRef } from "react";
import { SelectionOrFileContext } from "shared";
import { describe, expect, it, Mock, vi } from "vitest";
import { ContextHeaderContext, ContextHeaderItem, ContextHeaderState, useContextHeaderState } from "./ContextHeaderContext";
import { ComposerStateContextProvider } from "@/logics/composer/context/ComposerStateContext";
import { ContextHeader } from ".";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { collectMentionNodeV2 } from "./collectMentionNode";
import { render, waitFor } from "@testing-library/react";
import { TypeaheadMenuOptionType } from "../MentionPanel/useOptions";
import { useContextInitiation } from "../useContextInitiation";
import { Subject } from "rxjs";
import { useObservable } from "react-use";
import { IdeEnvProvider } from "@/providers/IdeEnvProvider";

// 模拟依赖
vi.mock("./collectMentionNode", () => ({
  collectMentionNodeV2: vi.fn(),
}));
vi.mock("@/bridge/useBridgeObservableAPI");
vi.mock("@/store/composerMessageConsumer", () => ({
  useComposerMessageConsumer: vi.fn(() => ({
    toBeAddedComposerContext: [],
    consumeComposerContext: vi.fn(),
  })),
}));

const mockUseBridgeObservableAPI = useBridgeObservableAPI as unknown as Mock<typeof useBridgeObservableAPI>;
const mockCollectMentionNodeV2 = collectMentionNodeV2 as unknown as Mock<typeof collectMentionNodeV2>;
type TextContentListener = Parameters<LexicalEditor["registerTextContentListener"]>[0];

interface ContextHeaderTestWrapperRef {
  getState: () => ContextHeaderState;
}

interface ContextHeaderTestWrapperProps {
  initialNodes: ContextHeaderItem[];
  editor: LexicalEditor;
}

// 使用useContextInitiation的测试包装器
const _ContextHeaderWithContextInitiation = forwardRef<ContextHeaderTestWrapperRef, ContextHeaderTestWrapperProps>(function ContextHeaderWithContextInitiation({
  initialNodes,
  editor,
}, ref) {
  const contextHeaderState = useContextHeaderState({
    initialNodes,
    sessionId: "",
  });

  // 使用useRef创建可变的ref对象
  const editorRef = useRef<LexicalEditor>(editor);

  // 使用真实的useContextInitiation来处理文件切换逻辑
  useContextInitiation({
    isContextConsumer: true,
    contextHeaderState,
    disabledCurrentFileBinding: false,
    editor: editorRef,
    sessionId: "",
  });

  useImperativeHandle(ref, () => ({
    getState: () => contextHeaderState,
  }));

  return (
    <ContextHeaderContext.Provider value={{
      state: contextHeaderState,
    }}
    >
      <ContextHeader mode="composer" editor={editor} />
    </ContextHeaderContext.Provider>
  );
});

const ContextHeaderTestWrapper = forwardRef<ContextHeaderTestWrapperRef, ContextHeaderTestWrapperProps>(function ContextHeaderTestWrapper(props, ref) {
  return (
    <IdeEnvProvider value={{ isKwaiPilotIDE: false }}>
      <ComposerStateContextProvider>
        <_ContextHeaderWithContextInitiation {...props} ref={ref} />
      </ComposerStateContextProvider>
    </IdeEnvProvider>
  );
});

describe("index2 test", () => {
  it("在输入框# 添加了当前文件，后从输入框删除当前文件: 知识区域胶囊不应该删除", async () => {
    // 当前文件结构
    const file1: SelectionOrFileContext = {
      uri: "file:///path/to/file1.tsx",
      relativePath: "path/to/file1.tsx",
      content: "",
    };
      // 初始知识区域有当前文件胶囊
    const initialNodes: ContextHeaderItem[] = [
      {
        structure: {
          type: "file",
          uri: file1.uri,
          relativePath: file1.relativePath,
        },
        followActiveEditor: true,
        isVirtualContext: false,
      },
    ];

    const currentFileAndSelectionSubject = new Subject<SelectionOrFileContext | null>();
    currentFileAndSelectionSubject.next(file1);

    mockUseBridgeObservableAPI.mockImplementation(function (api) {
      if (api === "currentFileAndSelection") {
        return useObservable(currentFileAndSelectionSubject);
      }
      return null;
    });
    mockCollectMentionNodeV2.mockImplementation(() => [
      {
        __structure: {
          type: "file",
          uri: file1.uri,
          relativePath: file1.relativePath,
        },
      } as MentionNodeV2,
    ]);
    const testRef = createRef<ContextHeaderTestWrapperRef>();
    const textContentListeners: TextContentListener[] = [];
    const triggerTextContentListeners = () => {
      textContentListeners.forEach(listener => listener(""));
    };
    const registerTextContentListener: LexicalEditor["registerTextContentListener"] = (listener) => {
      textContentListeners.push(listener);
      return () => {
        const index = textContentListeners.indexOf(listener);
        if (index !== -1) {
          textContentListeners.splice(index, 1);
        }
      };
    };
    const mockEditor = {
      registerTextContentListener,
    } as unknown as LexicalEditor;
      // 渲染组件
    render(
      <ContextHeaderTestWrapper ref={testRef} initialNodes={initialNodes} editor={mockEditor} />,
    );

    expect(testRef.current?.getState().nodes).toEqual(initialNodes);

    triggerTextContentListeners();

    mockCollectMentionNodeV2.mockImplementation(() => []);
    triggerTextContentListeners();
    await waitFor(() => {
      expect(testRef.current?.getState().nodes).toEqual(initialNodes);
    });
  });

  it("在输入框内# 了当前文件1，切换到文件2，会将原有文件1的知识胶囊固定，在所有文件胶囊的最前方展示切换到的文件2的胶囊", async () => {
    // 当前文件1结构
    const file1: SelectionOrFileContext = {
      uri: "file:///path/to/file1.tsx",
      relativePath: "path/to/file1.tsx",
      content: "",
    };
    // 文件2结构
    const file2: SelectionOrFileContext = {
      uri: "file:///path/to/file2.tsx",
      relativePath: "path/to/file2.tsx",
      content: "",
    };

    // 初始知识区域只有当前文件1胶囊
    const initialNodes: ContextHeaderItem[] = [
      {
        structure: {
          type: "file",
          uri: file1.uri,
          relativePath: file1.relativePath,
        },
        followActiveEditor: true,
        isVirtualContext: false,
      },
    ];
    // 第一步：模拟当前文件是file1，且输入框中#了file1

    const currentFileAndSelectionSubject = new Subject<SelectionOrFileContext | null>();
    currentFileAndSelectionSubject.next(file1);

    mockUseBridgeObservableAPI.mockImplementation(function (api) {
      if (api === "currentFileAndSelection") {
        return useObservable(currentFileAndSelectionSubject);
      }
      return null;
    });
    mockCollectMentionNodeV2.mockImplementation(() => [
      {
        __structure: {
          type: "file",
          uri: file1.uri,
          relativePath: file1.relativePath,
        },
      } as MentionNodeV2,
    ]);

    const testRef = createRef<ContextHeaderTestWrapperRef>();
    const textContentListeners: TextContentListener[] = [];
    const triggerTextContentListeners = () => {
      textContentListeners.forEach(listener => listener(""));
    };
    const registerTextContentListener: LexicalEditor["registerTextContentListener"] = (listener) => {
      textContentListeners.push(listener);
      return () => {
        const index = textContentListeners.indexOf(listener);
        if (index !== -1) {
          textContentListeners.splice(index, 1);
        }
      };
    };
    const mockEditor = {
      registerTextContentListener,
    } as unknown as LexicalEditor;

    // 渲染组件
    render(
      <ContextHeaderTestWrapper ref={testRef} initialNodes={initialNodes} editor={mockEditor} />,
    );

    // 验证初始状态
    await waitFor(() => {
      expect(testRef.current?.getState().nodes).toHaveLength(1);
      expect(testRef.current?.getState().nodes[0].structure.uri).toBe(file1.uri);
      expect(testRef.current?.getState().nodes[0].followActiveEditor).toBe(true);
    });

    // 触发文本内容监听器，确保组件已经处理了初始状态
    triggerTextContentListeners();

    // 模拟用户通过#手动添加file1，这会将原有的followActiveEditor节点转为虚拟节点
    testRef.current?.getState().tryInsertNode({
      structure: {
        type: TypeaheadMenuOptionType.file,
        uri: file1.uri,
        relativePath: file1.relativePath,
      },
      followActiveEditor: false,
      isVirtualContext: true,
    }, {
      source: "textarea",
    });

    await waitFor(() => {
      const currentNodes = testRef.current?.getState().nodes;
      expect(currentNodes?.length).toBe(1);
      expect(currentNodes?.[0].isVirtualContext).toBe(true);
      expect(currentNodes?.[0].followActiveEditor).toBe(true);
    });

    // 第二步：模拟切换到file2，输入框中仍保留着file1的#引用
    // 输入框中仍然#了file1
    mockCollectMentionNodeV2.mockImplementation(() => [
      {
        __structure: {
          type: "file",
          uri: file1.uri,
          relativePath: file1.relativePath,
        },
      } as MentionNodeV2,
    ]);
    currentFileAndSelectionSubject.next(file2);

    // 等待useContextInitiation中的useEffect响应文件变化
    await waitFor(() => {
      const currentNodes = testRef.current?.getState().nodes;

      // 验证结果：
      // 1. 应该有两个胶囊：file2(followActiveEditor) 和 file1(isVirtualContext)
      // 2. file2胶囊应在前面（作为followActiveEditor节点）
      expect(currentNodes?.length).toBe(2);
    });

    const currentNodes = testRef.current?.getState().nodes;

    // 第一个节点应该是file2，并且followActiveEditor为true
    expect(currentNodes?.[0].structure.uri).toBe(file2.uri);
    expect(currentNodes?.[0].followActiveEditor).toBe(true);
    expect(currentNodes?.[0].isVirtualContext).toBe(false);

    // 第二个节点应该是file1，并且isVirtualContext为true（因为是在输入框中#的）
    expect(currentNodes?.[1].structure.uri).toBe(file1.uri);
    expect(currentNodes?.[1].followActiveEditor).toBe(true);
    expect(currentNodes?.[1].isVirtualContext).toBe(true);
  });
  it("上下文初始带有当前文件, 从#面板取消勾选当前文件后, 上下文应当消失", async () => {
    const editor = createEditor({});
    const testRef = createRef<ContextHeaderTestWrapperRef>();

    const currentFile: SelectionOrFileContext = {
      uri: "file:///path/to/current/file.tsx",
      relativePath: "path/to/current/file.tsx",
      content: "",
    };

    const currentFileAndSelectionSubject = new Subject<SelectionOrFileContext | null>();

    mockUseBridgeObservableAPI.mockImplementation(function (api) {
      if (api === "currentFileAndSelection") {
        return useObservable(currentFileAndSelectionSubject);
      }
      return null;
    });

    mockCollectMentionNodeV2.mockImplementation(() => [
    ]);
    render(
      <ContextHeaderTestWrapper
        initialNodes={[]}
        editor={editor}
        ref={testRef}
      />,
    );
    currentFileAndSelectionSubject.next(currentFile);

    await waitFor(() => {
      expect(testRef.current?.getState().nodes[0].structure.uri).toEqual(currentFile.uri);
      expect(testRef.current?.getState().nodes[0].followActiveEditor).toBe(true);
      expect(testRef.current?.getState().nodes[0].isVirtualContext).toBe(false);
    });
    // 应当预期被删除
    testRef.current?.getState().tryDeleteNode({
      type: "file",
      uri: currentFile.uri,
      relativePath: currentFile.relativePath,
    });

    await waitFor(() => {
      expect(testRef.current?.getState().nodes).toHaveLength(0);
    });
  });
});
