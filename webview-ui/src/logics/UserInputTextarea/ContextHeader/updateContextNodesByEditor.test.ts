import { describe, it, expect, vi, beforeEach } from "vitest";
import { updateContextNodesByEditor } from "./updateContextNodesByEditor";
import { ContextHeaderItem } from "./ContextHeaderContext";
import { collectMentionNodeV2 } from "./collectMentionNode";
import { LexicalEditor } from "lexical";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";

// 模拟依赖
vi.mock("./collectMentionNode", () => ({
  collectMentionNodeV2: vi.fn(),
}));

describe("EditorState 变化-更新 Context", () => {
  // 公共变量
  const mockSetNodes = vi.fn();
  const mockMarkAsModified = vi.fn();
  const mockEditor = {} as LexicalEditor;
  const mockCollectMentionNodeV2 = collectMentionNodeV2 as unknown as ReturnType<typeof vi.fn>;

  // 每个测试前重置模拟
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it("同步删除 context node when:编辑器删除了节点", () => {
    // 初始状态：有一个虚拟节点
    const initialNodes: ContextHeaderItem[] = [
      {
        structure: {
          type: "file",
          uri: "file:///path/to/file1.ts",
          relativePath: "path/to/file1.ts",
        },
        followActiveEditor: false,
        isVirtualContext: true, // 这是一个虚拟节点
      },
    ];

    // 模拟编辑器中没有任何节点
    mockCollectMentionNodeV2.mockReturnValue([]);

    // 执行函数
    updateContextNodesByEditor({
      nodes: initialNodes,
      setNodes: mockSetNodes,
      markAsModified: mockMarkAsModified,
      editor: mockEditor,
    });

    // 验证节点被删除
    expect(mockSetNodes).toHaveBeenCalledTimes(1);
    const updatedNodes = mockSetNodes.mock.calls[0][0];
    expect(updatedNodes).toEqual([]);
  });
  it("新增context node when:编辑器添加了节点", () => {
    // 初始状态：没有节点
    const initialNodes: ContextHeaderItem[] = [];

    // 模拟编辑器中有一个节点
    const editorNodeStructure: MentionNodeV2Structure = {
      type: "file",
      uri: "file:///path/to/file2.ts",
      relativePath: "path/to/file2.ts",
    };

    mockCollectMentionNodeV2.mockReturnValue([
      { __structure: editorNodeStructure },
    ]);

    // 执行函数
    updateContextNodesByEditor({
      nodes: initialNodes,
      setNodes: mockSetNodes,
      markAsModified: mockMarkAsModified,
      editor: mockEditor,
    });

    // 验证节点被添加
    expect(mockSetNodes).toHaveBeenCalledTimes(1);
    const updatedNodes = mockSetNodes.mock.calls[0][0];
    expect(updatedNodes).toEqual([
      {
        structure: editorNodeStructure,
        followActiveEditor: false,
        isVirtualContext: true,
      },
    ]);

    // 验证标记已修改
    expect(mockMarkAsModified).toHaveBeenCalledTimes(1);
  });

  it("当节点已经存在时不应添加重复节点", () => {
    // 初始状态：有一个节点
    const existingStructure: MentionNodeV2Structure = {
      type: "file",
      uri: "file:///path/to/file3.ts",
      relativePath: "path/to/file3.ts",
    };

    const initialNodes: ContextHeaderItem[] = [
      {
        structure: existingStructure,
        followActiveEditor: false,
        isVirtualContext: false,
      },
    ];

    // 模拟编辑器中有同一个节点
    mockCollectMentionNodeV2.mockReturnValue([
      { __structure: existingStructure },
    ]);

    // 执行函数
    updateContextNodesByEditor({
      nodes: initialNodes,
      setNodes: mockSetNodes,
      markAsModified: mockMarkAsModified,
      editor: mockEditor,
    });

    // 验证节点没有改变
    expect(mockSetNodes).not.toHaveBeenCalled();
    expect(mockMarkAsModified).not.toHaveBeenCalled();
  });

  it("应同时处理删除和添加操作", () => {
    // 初始状态：有两个节点，一个虚拟节点将被删除，一个非虚拟节点将被保留
    const initialNodes: ContextHeaderItem[] = [
      {
        structure: {
          type: "file",
          uri: "file:///path/to/file4.ts",
          relativePath: "path/to/file4.ts",
        },
        followActiveEditor: false,
        isVirtualContext: true, // 将被删除
      },
      {
        structure: {
          type: "file",
          uri: "file:///path/to/file5.ts",
          relativePath: "path/to/file5.ts",
        },
        followActiveEditor: false,
        isVirtualContext: false, // 将被保留
      },
    ];
    // 模拟编辑器中有一个新节点和一个已存在的节点
    const newNodeStructure: MentionNodeV2Structure = {
      type: "file",
      uri: "file:///path/to/file6.ts",
      relativePath: "path/to/file6.ts",
    };

    mockCollectMentionNodeV2.mockReturnValue([
      { __structure: initialNodes[1].structure }, // 保留的节点
      { __structure: newNodeStructure }, // 新增的节点
    ]);

    // 执行函数
    updateContextNodesByEditor({
      nodes: initialNodes,
      setNodes: mockSetNodes,
      markAsModified: mockMarkAsModified,
      editor: mockEditor,
    });

    // 验证结果
    expect(mockSetNodes).toHaveBeenCalledTimes(1);
    const updatedNodes = mockSetNodes.mock.calls[0][0];

    // 应该有两个节点：保留的节点和新增的节点
    expect(updatedNodes.length).toBe(2);

    // 确认第一个节点是保留的节点
    expect(updatedNodes[0]).toEqual(initialNodes[1]);

    // 确认第二个节点是新增的节点
    expect(updatedNodes[1].structure).toEqual(newNodeStructure);
    expect(updatedNodes[1].isVirtualContext).toBe(true);

    // 验证标记已修改
    expect(mockMarkAsModified).toHaveBeenCalledTimes(1);
  });
  it("当没有变化时不应更新节点", () => {
    // 初始状态：有一个非虚拟节点
    const initialNodes: ContextHeaderItem[] = [
      {
        structure: {
          type: "file",
          uri: "file:///path/to/file7.ts",
          relativePath: "path/to/file7.ts",
        },
        followActiveEditor: false,
        isVirtualContext: false,
      },
    ];

    // 模拟编辑器中有同一个节点
    mockCollectMentionNodeV2.mockReturnValue([
      { __structure: initialNodes[0].structure },
    ]);

    // 执行函数
    updateContextNodesByEditor({
      nodes: initialNodes,
      setNodes: mockSetNodes,
      markAsModified: mockMarkAsModified,
      editor: mockEditor,
    });

    // 验证没有调用setNodes和markAsModified
    expect(mockSetNodes).not.toHaveBeenCalled();
    expect(mockMarkAsModified).not.toHaveBeenCalled();
  });

  it("即使节点列表改变了，如果没有新增节点也不应调用markAsModified", () => {
    // 初始状态：有一个虚拟节点将被删除
    const initialNodes: ContextHeaderItem[] = [
      {
        structure: {
          type: "file",
          uri: "file:///path/to/file8.ts",
          relativePath: "path/to/file8.ts",
        },
        followActiveEditor: false,
        isVirtualContext: true, // 将被删除
      },
    ];
    // 模拟编辑器中没有节点
    mockCollectMentionNodeV2.mockReturnValue([]);

    // 执行函数
    updateContextNodesByEditor({
      nodes: initialNodes,
      setNodes: mockSetNodes,
      markAsModified: mockMarkAsModified,
      editor: mockEditor,
    });

    // 验证调用了setNodes但没有调用markAsModified
    expect(mockSetNodes).toHaveBeenCalledTimes(1);
    expect(mockMarkAsModified).not.toHaveBeenCalled();
  });
});
