import { useComposerMessageConsumer } from "@/store/composerMessageConsumer";
import { RefObject, useCallback, useEffect, useState } from "react";
import { ContextHeaderItem, ContextHeaderState, isIdentical } from "./ContextHeader/ContextHeaderContext";
import { firstValueFrom } from "rxjs";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { isRuleFile } from "shared/lib/util";
import { ReportOpt } from "@shared/types/logger";
import { reportUserAction } from "@/utils/weblogger";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { MentionNodeV2Structure_File } from "shared/lib/MentionNodeV2/nodes";
import { produce } from "immer";
import { LexicalEditor } from "lexical";
import { collectMentionNodeV2 } from "./ContextHeader/collectMentionNode";

/**
 * context 知识初始化
 *
 * 包含:
 * 1. 监听extension 层的事件
 * 2. 添加当前文件到上下文, 以及文件变化时跟随
 *
 *
 * * 添加followActiveEditor
 * * 同步followActiveEditor
 */
export function useContextInitiation({
  isContextConsumer,
  contextHeaderState,
  disabledCurrentFileBinding,
  editor,
  sessionId,
}: {
  // 是否是  addComposerContext 的消费者
  isContextConsumer: boolean;
  contextHeaderState: ContextHeaderState;
  disabledCurrentFileBinding: boolean;
  editor: RefObject<LexicalEditor | null>;
  sessionId: string;
}) {
  const toBeAddedComposerContext = useComposerMessageConsumer(s => s.toBeAddedComposerContext);
  const { consumeComposerContext } = useComposerMessageConsumer();

  const [editorStateInitiationDone, setEditorStateInitializationDone] = useState(false);

  const resetContext = useCallback(async () => {
    try {
      contextHeaderState.markAsModified(false);
      const webEnabled = contextHeaderState.nodes.some(v => v.structure.type === "web");
      const codebaseSearchEnabled = contextHeaderState.nodes.some(v => v.structure.type === "codebase");
      const nodesRemaining: ContextHeaderItem[] = ([] as ContextHeaderItem[]).concat(
        webEnabled
          ? [{
              structure: {
                type: "web",
                uri: "web://",
                relativePath: "",
              },
              followActiveEditor: false,
              isVirtualContext: false,
            }]
          : [],
        codebaseSearchEnabled
          ? [{
              structure: {
                type: "codebase",
                uri: "codebase://",
                relativePath: "",
              },
              followActiveEditor: false,
              isVirtualContext: false,
            }]
          : [],
      );
      const currentFile = await firstValueFrom(kwaiPilotBridgeAPI.observableAPI.currentFileAndSelection());
      if (!disabledCurrentFileBinding && currentFile && !isRuleFile(currentFile.relativePath)) {
        contextHeaderState.setNodes(() => {
          const parms: ReportOpt<"input_add_context"> = {
            key: "input_add_context",
            type: "default",
            content: "currentFile",
            subType: "",
          };
          reportUserAction(parms, /* 输入框没有 chatId */"", sessionId);
          return [
            ...nodesRemaining,
            {
              structure: {
                type: "file",
                uri: currentFile?.uri,
                relativePath: currentFile?.relativePath,
              },
              followActiveEditor: true,
              isVirtualContext: false,
            },
          ];
        });
      }
      else {
        contextHeaderState.setNodes([...nodesRemaining]);
      }
      await new Promise(resolve => setTimeout(resolve, 0)); // 等待下一帧 防止下面更新currentFileAndSelection 的 effect 读取到错误的值
    }
    finally {
      setEditorStateInitializationDone(true);
    }
  }, [contextHeaderState, disabledCurrentFileBinding, sessionId]);

  const currentFileAndSelection = useBridgeObservableAPI("currentFileAndSelection");
  /**
   * 更新当前文件到上下文
   */
  useEffect(() => {
    if (disabledCurrentFileBinding) {
      return;
    }
    if (!currentFileAndSelection || isRuleFile(currentFileAndSelection.relativePath)) {
      return;
    }
    const nodes = contextHeaderState.nodes;
    const followActiveEditorNodeI = nodes.findIndex(v => v.followActiveEditor && !v.isVirtualContext);
    const virtualFollowedNodes = nodes.filter(v => v.followActiveEditor && v.isVirtualContext);

    const newestStructure: MentionNodeV2Structure_File | null = {
      type: "file",
      uri: currentFileAndSelection.uri,
      relativePath: currentFileAndSelection.relativePath,
    };
    if (followActiveEditorNodeI > -1) {
      if (!isIdentical(nodes[followActiveEditorNodeI].structure, newestStructure)) {
        if (virtualFollowedNodes.some(v => isIdentical(v.structure, newestStructure))) {
          // 如果有跟随的虚拟节点, 则需要删除这个节点
          contextHeaderState.setNodes(prev => produce(prev, (draft) => {
            draft.splice(followActiveEditorNodeI, 1);
          }));
        }
        else {
          contextHeaderState.setNodes(prev => produce(prev, (draft) => {
            draft.splice(followActiveEditorNodeI, 1, {
              structure: newestStructure,
              followActiveEditor: true,
              isVirtualContext: false,
            });
          }));
        }
      }
    }
    else {
      // 新增当前文件到上下文
      // virtual node 有可能是 follow 的,此时要先判断这些节点里有没有当前文件, 没有再新增
      if (!contextHeaderState.isModified && !virtualFollowedNodes.some(v => isIdentical(v.structure, newestStructure))) {
        contextHeaderState.setNodes(prev => produce(prev, (draft) => {
          draft.unshift({
            structure: newestStructure,
            followActiveEditor: true,
            isVirtualContext: false,
          });
        }));
      }
    }

    if (editor.current) {
      // 检查是否还在编辑器中
      const editorNodes = collectMentionNodeV2(editor.current);
      const existInEditor = (v: ContextHeaderItem) => editorNodes.some(n => isIdentical(n.__structure, v.structure));
      if (virtualFollowedNodes.some(v => !existInEditor(v))) {
      // 如果跟随的虚拟节点不在编辑器中, 则需要删除这些节点
        contextHeaderState.setNodes(prev => prev.filter(v => existInEditor(v)));
      }
    }
  }, [contextHeaderState, currentFileAndSelection, disabledCurrentFileBinding, editor]);

  useEffect(() => {
    if (!isContextConsumer) {
      return;
    }
    if (!editorStateInitiationDone) {
      // 在 initiation task 中完成 否则 resetEditorState 会把这些 context 覆盖掉
      return;
    }
    // 检查是否有待添加的上下文
    if (toBeAddedComposerContext.length > 0) {
      // 获取第一个待添加的上下文
      const firstContext = toBeAddedComposerContext[0];
      // 添加到编辑器上下文
      contextHeaderState.tryInsertNode({
        structure: firstContext.message,
        followActiveEditor: false,
        isVirtualContext: false,
      }, {
        source: "shortcut",
      });
      // 从队列中移除已处理的上下文
      consumeComposerContext(firstContext);
    }
  }, [toBeAddedComposerContext, consumeComposerContext, contextHeaderState, isContextConsumer, editorStateInitiationDone]);

  return {
    resetContext,
    setEditorStateInitializationDone,
  };
}
