
.colorfulBorder {

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    /* 确保伪元素不会影响鼠标事件 */
    border-radius: 8.5px;
    margin: 0px;
    /* 边框宽度 */

    z-index: -1;
  }
}

.colorfulBorderFocused {
  &::after {
    background: linear-gradient(to right, #2698eb, #2ecfe5, #6d72f4);
  }
}

.colorfulBorderDark {
  &::after {
    background: #33455A;
  }
}

.colorfulBorderLight {
  &::after {
    background: #D6DCE3;
  }
}