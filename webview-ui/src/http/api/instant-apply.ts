import { kwaiPilotBridgeAPI } from "@/bridge";
import { InstantApplyBody } from "./type";
import { v4 as uuidv4 } from "uuid";

const activeControllers = new Map<string, AbortController>();

export const abortInstantApply = () => {
  activeControllers.forEach(controller => controller.abort());
  activeControllers.clear();
  fileSet.clear();
};

// 处理采纳过程中多次点击采纳同一个文件
const fileSet = new Set<string>();

export async function startInstantApply(
  body: Pick<
    InstantApplyBody,
    "files" | "modelOutput" | "sessionId" | "chatId"
  >,
) {
  const filePath = body.files[0].filePath;
  const applyId = uuidv4();
  fileSet.add(filePath);
  await kwaiPilotBridgeAPI.editor.fileEdit({
    filename: body.files[0].filePath,
    modelOutput: body.modelOutput,
    sessionId: body.sessionId,
    chatId: body.chatId,
    applyId,
  });
  fileSet.delete(filePath);
}
