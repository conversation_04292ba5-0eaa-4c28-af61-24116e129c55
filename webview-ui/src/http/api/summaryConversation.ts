import { httpClient } from "@/http";
import baseInfoManager from "@/utils/baseInfo";
import { getUserInfo } from "@/utils/getUserInfo";

export const fetchSummaryConversation = async (
  question: string,
  onMessage: (chunk: string) => void,
) => {
  const abortController = new AbortController();
  let title = "";
  const username = (await getUserInfo())?.name;
  const body = {
    platform: baseInfoManager.platform,
    userQuestion: question,
    username,
  };

  httpClient.fetchEventSource(
    "/eapi/kwaipilot/plugin/tool/summaryConversation",
    {
      body: JSON.stringify(body),
      headers: {
        "Content-Type": "application/json",
      },
      method: "POST",
      signal: abortController.signal,
      onmessage(c) {
        title += c.data;
        onMessage(c.data);
        if (title.length > 100) {
          abortController.abort();
        }
      },
      onerror(e: unknown) {
        console.error(e);
        throw Error("summaryConversation error");
      },
    },
  );
};
