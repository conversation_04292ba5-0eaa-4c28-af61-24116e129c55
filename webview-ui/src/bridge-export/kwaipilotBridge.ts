import {
  ExtractNativeBridgePayload,
  ExtractNativeBridgeResult,
  NATIVE_BRIDGE_EVENT_NAME,
  WEBVIEW_BRIDGE_EVENT_NAME,
  WebviewBridgeParams,
  WebviewBridgeResult,
} from "@shared/types/bridge";
import { Bridge, BridgeMessage, BridgeMessageStateless } from "../bridge-share/types";
import { IKwaiPilotBridge } from "../bridge-share/IKwaiPilotBridge";
import { logger } from "../utils/logger";

export type VSCodeNativeBridge = Bridge & {
  reigisterBridgeCommand: (name: string, fn: () => void) => void;
  dispose: () => void;
};

export class KwaiPilotBridge implements IKwaiPilotBridge {
  public bridge?: VSCodeNativeBridge;
  private loggerScope = "VSCodeKwaiPilotBridge";
  private extensionHostReady = false;

  // 区分不同类型的 pending 消息
  private pendingCallHandlerMessages: {
    handlerName: string;
    data: any;
    callback?: (data: any) => void;
  }[] = [];

  private pendingOneWayMessages: {
    eventName: NATIVE_BRIDGE_EVENT_NAME;
    message: any;
  }[] = [];

  constructor() {
  }

  public setBridge(bridgeImplementation: VSCodeNativeBridge): void {
    this.bridge = bridgeImplementation;
    this.bridge.reigisterBridgeCommand("extensionHostReady", () => {
      console.log(`[${this.loggerScope}] Extension host ready, processing pending messages:`, {
        callHandlerMessages: this.pendingCallHandlerMessages.length,
        oneWayMessages: this.pendingOneWayMessages.length,
      });

      this.extensionHostReady = true;

      // 分别处理不同类型的 pending 消息
      this.pendingCallHandlerMessages.forEach((message) => {
        console.log(`[${this.loggerScope}] Replaying callHandler message:`, message.handlerName);
        this.postMessage(
          message.handlerName as unknown as NATIVE_BRIDGE_EVENT_NAME,
          message.data,
          message.callback,
        );
      });

      this.pendingOneWayMessages.forEach((message) => {
        console.log(`[${this.loggerScope}] Replaying oneWay message:`, message.eventName);
        this.postOneWayMessage(
          message.eventName,
          message.message,
        );
      });

      // 清空 pending 消息
      this.pendingCallHandlerMessages = [];
      this.pendingOneWayMessages = [];
    });
  }

  /**
   * 调用原生端
   * @param eventName
   * @param message
   * @param callback
   */
  public async postMessage<T extends NATIVE_BRIDGE_EVENT_NAME>(
    eventName: T,
    message: BridgeMessage<ExtractNativeBridgePayload<T>>,
    callback?: (params: ExtractNativeBridgeResult<T>) => void,
  ) {
    if (!this.extensionHostReady) {
      this.pendingCallHandlerMessages.push({
        handlerName: eventName,
        data: message,
        callback,
      });
      return;
    }

    const data = JSON.stringify(message);

    if (callback) {
      this.bridge?.callHandler(eventName, data, (d: string) => {
        callback(this.handleResponse<ExtractNativeBridgeResult<T>>(d));
      });
    }
    else {
      this.bridge?.callHandler(eventName, data);
    }
  }

  /**
   * 注册native可以调用的handler 注意只能调用一次 多次调用会覆盖
   * @param eventName
   * @param handler
   */
  public async onMessage<T extends WEBVIEW_BRIDGE_EVENT_NAME>(
    eventName: T,
    handler: (params: WebviewBridgeResult[T]) => void,
  ) {
    // await this.bridgePromise;
    this.bridge?.registerHandler(eventName, (data: string) =>
      this.formatOnMessageHandler<T>(eventName, data, handler),
    );
  }

  /**
   * 发送单向无状态消息， 旧版 bridge API 强行要求有 req - res 的结构， 如果要发送单向消息， 使用这个结构
   * @param eventName
   * @param message
   */
  public postOneWayMessage<T extends NATIVE_BRIDGE_EVENT_NAME>(
    eventName: T,
    message: ExtractNativeBridgePayload<T>,
  ) {
    if (!this.extensionHostReady) {
      console.log(`[${this.loggerScope}] Queuing oneWay message (extension not ready):`, eventName);
      this.pendingOneWayMessages.push({
        eventName,
        message,
      });
      return;
    }
    console.log(`[${this.loggerScope}] Sending oneWay message:`, eventName);
    this.bridge?.postMessage({
      event: eventName,
      payload: message,
    } satisfies BridgeMessageStateless<ExtractNativeBridgePayload<T>>);
  }

  private listenerMap: Map<
    WEBVIEW_BRIDGE_EVENT_NAME,
    Set<(payload: any) => unknown>
  > = new Map();

  /**
   * 添加消息监听器, 无状态
   * @param event
   * @param listener
   */
  public addMessageListener<T extends WEBVIEW_BRIDGE_EVENT_NAME>(
    event: T,
    listener: (payload: WebviewBridgeParams[T]) => unknown,
  ): () => void {
    function isStatelessMessage(
      message: unknown,
    ): message is BridgeMessageStateless {
      return (
        typeof message === "object"
        && message !== null
        && "protocol" in message
        && message.protocol === "message"
      );
    }
    const _listener = (message: unknown) => {
      // 对 event protocol 过滤
      if (isStatelessMessage(message) && message.event === event) {
        listener(message.payload);
      }
    };
    this.bridge?.addMessageListener(_listener);
    return () => {
      this.bridge?.removeMessageListener(_listener);
    };
  }

  private formatOnMessageHandler<T extends WEBVIEW_BRIDGE_EVENT_NAME>(
    eventName: T,
    data: string,
    handler: (params: WebviewBridgeResult[T]) => void,
  ) {
    try {
      const d = JSON.parse(data) as BridgeMessage;
      if (d.code !== 0) {
        logger.error(
          `call native handler: ${eventName} error`,
          this.loggerScope,
          {
            err: d.msg,
          },
        );
      }
      return handler(d.payload as WebviewBridgeResult[T]);
    }
    catch (error) {
      logger.error(
        `call native handler: ${eventName} error`,
        this.loggerScope,
        {
          err: error,
        },
      );
      throw error;
    }
  }

  private handleResponse<T>(res: string): T {
    try {
      const formatedRes = JSON.parse(res) as BridgeMessage;
      if (formatedRes.code !== 0) {
        logger.error(
          `response with error code: ${formatedRes.code}`,
          this.loggerScope,
          {
            value: formatedRes,
          },
        );
      }
      return formatedRes.payload as T;
    }
    catch (err) {
      logger.error("handleResponse error", this.loggerScope, { err });
      throw err;
    }
  }

  // 生成唯一ID
  generateId(): string {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }
}
