// NOTE: 插件 bridge 实现类
import { UserInfo } from "@shared/types";
import {
  NATIVE_BRIDGE_EVENT_NAME,
  WEBVIEW_BRIDGE_EVENT_NAME,
} from "@shared/types/bridge";
import { KwaiPilotBridge } from "./kwaipilotBridge";
import { BridgeEventType } from "../bridge-share/types";
import { LoggerSupplementaryField } from "@shared/types/logger";
import { ObservableAPI } from "shared";
import { createWebviewRpcContext } from "../bridge-share/WebviewRpcContext";
import { logger } from "@/utils/logger";
import { setGlobalObject } from "@/utils/globalObject";
import { BaseKwaiPilotBridgeAPI, proxyExtensionAPI } from "../bridge-share/BaseKwaiPilotBridgeAPI";

export class KwaiPilotBridgeAPI extends BaseKwaiPilotBridgeAPI {
  protected loggerScope = "PluginKwaiPilotBridgeAPI";

  constructor() {
    super(); // 调用父类空构造函数
    // 初始化bridge实例
    this.bridge = new KwaiPilotBridge();
    this.observableAPI = this.createObservableAPI();
    // setupBridge 时机区分：ide 和插件
    this.setupBridge();
  }

  // 实现抽象方法：初始化RPC上下文
  protected initializeRpcContext(): void {
    this.rpcContext = createWebviewRpcContext({
      logger: () => logger,
      protocol: {
        send: (message) => {
          this.bridge.postOneWayMessage(NATIVE_BRIDGE_EVENT_NAME.RPC_MESSAGE, message);
        },
        onMessage: (listener) => {
          return this.addMessageListener(WEBVIEW_BRIDGE_EVENT_NAME.RPC_MESSAGE, (data) => {
            listener(data);
          });
        },
      },
    });
  }

  // 实现抽象方法：直接使用桥接获取用户信息
  public getAndWatchUserInfo(callback: (userinfo: UserInfo) => void) {
    const messageId = this.bridge.generateId();
    this.bridge.postMessage(
      NATIVE_BRIDGE_EVENT_NAME.GET_AND_WATCH_USER_INFO,
      {
        id: messageId,
        event: BridgeEventType.GET_AND_WATCH_USER_INFO,
      },
      callback,
    );
    this.bridge.onMessage(WEBVIEW_BRIDGE_EVENT_NAME.GET_AND_WATCH_USER_INFO_CALLBACK, callback);
  }

  // 实现抽象方法：创建标准的Observable API
  protected createObservableAPI(): ObservableAPI {
    return {
      currentFileAndSelection: proxyExtensionAPI(this.bridge, "currentFileAndSelection"),
      visibility: proxyExtensionAPI(this.bridge, "visibility"),
      currentTheme: proxyExtensionAPI(this.bridge, "currentTheme"),
      isDeveloperMode: proxyExtensionAPI(this.bridge, "isDeveloperMode"),
      latestCopiedContent: proxyExtensionAPI(this.bridge, "latestCopiedContent"),
      indexState: proxyExtensionAPI(this.bridge, "indexState"),
      mcpServers: proxyExtensionAPI(this.bridge, "mcpServers"),
      customPanelPage: proxyExtensionAPI(this.bridge, "customPanelPage"),
      rulesList: proxyExtensionAPI(this.bridge, "rulesList"),
      userInfo: proxyExtensionAPI(this.bridge, "userInfo"),
      settingUpdate: proxyExtensionAPI(this.bridge, "settingUpdate"),
    };
  }

  // 重写打印日志方法以保持兼容性
  public printLogger(data: {
    level: "silly" | "debug" | "verbose" | "info" | "warn" | "error";
    msg: string;
    scope: string;
    tags?: LoggerSupplementaryField;
  }) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.PRINT_LOGGER, {
      id: this.bridge.generateId(),
      event: BridgeEventType.PRINT_LOGGER,
      payload: data,
    });
  }
}

export const kwaiPilotBridgeAPI = new KwaiPilotBridgeAPI();

setGlobalObject("kwaiPilotBridgeAPI", kwaiPilotBridgeAPI);
