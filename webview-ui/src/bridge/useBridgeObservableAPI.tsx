import { useMemo } from "react";
import { ObservableAPI } from "shared";
import { useObservable } from "react-use";
import { kwaiPilotBridgeAPI } from ".";
import { Observable } from "rxjs";

type ExtractObservableValue<V> = V extends Observable<infer T> ? T : V;

/**
 * 统一的 hook 方式调用 observable ，避免一些惯性错误写法
 *
 * 例如 useObservable(kwaiPilotBridgeAPI.observableAPI.currentFileAndSelection())  会造成 observableAPI 重复调用
 * @param observableAPIName
 * @returns
 */
export function useBridgeObservableAPI<T extends keyof ObservableAPI>(observableAPIName: T) {
  const observable = useMemo(() => kwaiPilotBridgeAPI.observableAPI[observableAPIName](), [observableAPIName]);
  return useObservable<ExtractObservableValue<ReturnType<ObservableAPI[T]>>>(observable as any);
}
