// NOTE: ide 专用文件，插件请勿修改
import { useContext, useEffect, useState, useCallback } from "react";
import { useIdeEnv, IdeEnvContext } from "../providers/IdeEnvProvider";
import { kwaiPilotBridgeAPI } from "../bridge-export";

/**
 * 使用 VSCode Services 的自定义 Hook
 *
 * @returns VSCode services accessor 实例
 */
export const useVSCodeServices = () => {
  const ideEnv = useContext(IdeEnvContext);

  // 从 IdeEnvContext 或 bridge API 中获取 accessor
  const accessor = ideEnv?.accessor || kwaiPilotBridgeAPI.getServicesAccessor();

  if (!accessor) {
    console.warn("VSCode services accessor is not available. Make sure you are running in the IDE environment.");
  }

  return accessor;
};

/**
 * 获取特定 VSCode 服务的 Hook (通过 getService 方法，作为备用方案)
 *
 * @param serviceToken - 服务标识符
 * @returns 服务实例或 undefined
 */
export const useVSCodeService = <T>(serviceToken: any): T | undefined => {
  const accessor = useVSCodeServices();

  try {
    return accessor?.getService?.<T>(serviceToken);
  }
  catch (error) {
    console.error(`Failed to get VSCode service with token: ${serviceToken}`, error);
    return undefined;
  }
};

/**
 * 获取 VSCode 命令服务的 Hook
 * @returns 命令服务实例和可用性状态
 */
export function useCommandService() {
  const { accessor } = useIdeEnv();

  const executeCommand = useCallback(async (commandId: string, ...args: any[]) => {
    if (!accessor?.commandService) {
      throw new Error("Command service not available");
    }
    return accessor.commandService.executeCommand(commandId, ...args);
  }, [accessor]);

  return {
    executeCommand,
    isAvailable: !!accessor?.commandService,
  };
}

/**
 * 获取 VSCode 配置服务的 Hook
 * @returns 配置服务实例和可用性状态
 */
export function useConfigurationService() {
  const { accessor } = useIdeEnv();

  const getValue = useCallback(<T>(section: string): T | undefined => {
    if (!accessor?.configurationService) {
      console.warn("Configuration service not available");
      return undefined;
    }
    return accessor.configurationService.getValue<T>(section);
  }, [accessor]);

  const updateValue = useCallback(async (section: string, value: any) => {
    if (!accessor?.configurationService) {
      throw new Error("Configuration service not available");
    }
    return accessor.configurationService.updateValue(section, value);
  }, [accessor]);

  return {
    getValue,
    updateValue,
    isAvailable: !!accessor?.configurationService,
  };
}

/**
 * 获取 VSCode 日志服务的 Hook
 * @returns 日志服务实例和可用性状态
 */
export function useLogService() {
  const { accessor } = useIdeEnv();

  const info = useCallback((message: string, ...args: any[]) => {
    if (!accessor?.logService) {
      console.info(message, ...args);
      return;
    }
    accessor.logService.info(message, ...args);
  }, [accessor]);

  const warn = useCallback((message: string, ...args: any[]) => {
    if (!accessor?.logService) {
      console.warn(message, ...args);
      return;
    }
    accessor.logService.warn(message, ...args);
  }, [accessor]);

  const error = useCallback((message: string | Error, ...args: any[]) => {
    if (!accessor?.logService) {
      console.error(message, ...args);
      return;
    }
    accessor.logService.error(message, ...args);
  }, [accessor]);

  const debug = useCallback((message: string, ...args: any[]) => {
    if (!accessor?.logService) {
      console.debug(message, ...args);
      return;
    }
    accessor.logService.debug(message, ...args);
  }, [accessor]);

  return {
    info,
    warn,
    error,
    debug,
    isAvailable: !!accessor?.logService,
  };
}

/**
 * 获取用户信息并监听变化的 Hook
 * @returns 用户信息状态和监听控制函数
 */
export function useUserInfo() {
  const { accessor } = useIdeEnv();
  const [userInfo, setUserInfo] = useState<any>(undefined);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!accessor?.userInfoWatcherService) {
      console.warn("UserInfoWatcher service not available");
      setIsLoading(false);
      return;
    }

    // 获取当前用户信息
    const currentUserInfo = accessor.userInfoWatcherService.getCurrentUserInfo();
    setUserInfo(currentUserInfo);
    setIsLoading(false);

    // 监听用户信息变化
    const handleUserInfoChange = (newUserInfo: any) => {
      setUserInfo(newUserInfo);
    };

    accessor.userInfoWatcherService.getAndWatchUserInfo(handleUserInfoChange);

    // 清理监听器
    return () => {
      accessor.userInfoWatcherService?.removeUserInfoWatcher(handleUserInfoChange);
    };
  }, [accessor]);

  return {
    userInfo,
    isLoading,
    isLoggedIn: !!userInfo,
    isAvailable: !!accessor?.userInfoWatcherService,
  };
}

/**
 * 获取主题信息的 Hook
 * @returns 主题信息和可用性状态
 */
export function useThemeService() {
  const { accessor } = useIdeEnv();
  const [setTheme] = useState<any>(null);

  useEffect(() => {
    if (!accessor?.themeService) {
      console.warn("Theme service not available");
      return;
    }

    try {
      const currentTheme = accessor.themeService.getColorTheme();
      setTheme(currentTheme);
    }
    catch (error) {
      console.error("Failed to get current theme:", error);
    }
  }, [accessor, setTheme]);

  return accessor?.themeService;
}

/**
 * 获取存储服务的 Hook
 * @returns 存储服务实例和可用性状态
 */
export function useStorageService() {
  const { accessor } = useIdeEnv();

  const get = useCallback((key: string, scope: number): string | undefined => {
    if (!accessor?.storageService) {
      console.warn("Storage service not available");
      return undefined;
    }
    return accessor.storageService.get(key, scope);
  }, [accessor]);

  const store = useCallback((key: string, value: string, scope: number) => {
    if (!accessor?.storageService) {
      console.warn("Storage service not available");
      return;
    }
    accessor.storageService.store(key, value, scope);
  }, [accessor]);

  const remove = useCallback((key: string, scope: number) => {
    if (!accessor?.storageService) {
      console.warn("Storage service not available");
      return;
    }
    accessor.storageService.remove(key, scope);
  }, [accessor]);

  return {
    get,
    store,
    remove,
    isAvailable: !!accessor?.storageService,
  };
}

/**
 * 获取通知服务的便捷 Hook
 */
export const useNotificationService = () => {
  const accessor = useVSCodeServices();

  return accessor?.notificationService || null;
};

/**
 * 获取视图服务的便捷 Hook
 */
export const useViewsService = () => {
  const accessor = useVSCodeServices();

  return accessor?.viewsService || null;
};

/**
 * 获取工作区服务的便捷 Hook
 */
export const useWorkspaceService = () => {
  const accessor = useVSCodeServices();

  return accessor?.workspaceService || null;
};
