import { kwaiPilotBridgeAPI } from "@/bridge";
import { IdeEnvContext } from "@/providers/IdeEnvProvider";
import { useContext, useEffect, useRef, useState } from "react";

export function useIdeEnv() {
  const context = useContext(IdeEnvContext);
  if (context === undefined) {
    throw new Error("useIdeEnv must be used within an IdeEnvProvider");
  }

  const fetched = useRef(false);
  const [ide, setIde] = useState<"kwaipilot-vscode" | "kwaipilot-xcode">(
    "kwaipilot-vscode",
  );

  useEffect(() => {
    if (fetched.current) return;
    fetched.current = true;
    kwaiPilotBridgeAPI.getSystemInfo().then((data) => {
      if (data.ide) {
        setIde(data.ide as "kwaipilot-vscode" | "kwaipilot-xcode");
      }
    });
  }, []);

  return [ide, context.isKwaiPilotIDE] as [
    "kwaipilot-vscode" | "kwaipilot-xcode",
    boolean,
  ];
}
