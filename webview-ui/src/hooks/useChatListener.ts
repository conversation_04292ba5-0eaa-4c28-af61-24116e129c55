import { kwai<PERSON>ilotBridgeAPI } from "@/bridge";
import { DEFAULT_MODEL_TYPE } from "@/constant";
import { fetchSummaryConversation } from "@/http/api/summaryConversation";
import {
  getRecordStoreByVendor,
  RecordStoreVendor,
  ReocrdStoreVendorEnum,
  selectActiveModel,
} from "@/store/record";
import { useUserStore } from "@/store/user";
import { chatId } from "@/utils/chatId";
import { MessageType } from "@/utils/const";

import eventBus from "@/utils/eventBus";
import { getActionInfo } from "@/utils/getActionInfo";
import {
  generateCustomUUID,
  getActionResults,
} from "@/utils/sessionUtils";
import { getCurrentSessionTimeString } from "@/utils/utils";
import { reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import { CodeActionParams } from "@shared/types";
import { QAItem } from "@shared/types/chatHistory";
import { useCallback, useEffect } from "react";
import { useHistoryStore } from "@/store/history";
import { createPlainTextEditorState } from "@/components/TextArea/lexical/editorState";
import { produce } from "immer";
import { httpClient } from "@/http";
import repoChatService from "@/services/repo-chat";
import { navigateWithCache } from "./useNavigateWithCach";
import { postMessageUtil } from "@/utils/postMessageUtil";
import { MentionNodeV2Structure_Selection } from "shared/lib/MentionNodeV2/nodes";
import { firstValueFrom } from "rxjs";

/**
 * 谨慎在这个方法中添加逻辑, 总线结构不利于维护
 * @returns
 */
export const useChatListener = () => {
  const userInfo = useUserStore(state => state.userInfo);
  const updateHistoryList = useHistoryStore(state => state.updateHistoryList);
  const navigate = navigateWithCache;

  const handleActionResult = useCallback(
    (data: { id: string; data: string; vendor: RecordStoreVendor }) => {
      const recordStoreState = getRecordStoreByVendor(data.vendor).getState();
      const { sessionHistory, setSessionHistory } = recordStoreState;
      if (!sessionHistory) {
        return;
      }
      const { id, data: reply } = data;
      const newCachedMessages = produce(
        sessionHistory.cachedMessages,
        (draft) => {
          const QA = draft.find(item => item.id === id);
          if (QA) {
            QA.A[QA.A.length - 1].reply = "";
            QA.A[QA.A.length - 1].actionResults = getActionResults(reply);
          }
        },
      );
      setSessionHistory({
        ...sessionHistory,
        cachedMessages: newCachedMessages,
      });
    },
    [],
  );

  /**
   * 一些从 editor 中传来的解释代码等操作
   */
  const handleActionForCode = useCallback(
    async (params: CodeActionParams) => {
      navigate("/chat");
      const recordStoreState = getRecordStoreByVendor("chat").getState();
      const {
        sessionHistory,
        loadingStatu,
        setSuggestQuestion,
        summaryConversation,
      } = recordStoreState;
      const chatModel = selectActiveModel(recordStoreState);
      const { type, languageId, selectText, section, fullPath } = params;
      const { message, reportType } = getActionInfo(type);
      if (!userInfo) {
        return;
      }
      setSuggestQuestion([]);
      if (loadingStatu && loadingStatu.status === "loading") {
        kwaiPilotBridgeAPI.showToast({
          level: "error",
          message: "正在生成回答，请稍后尝试",
        });
        return;
      }
      const uniqueId
        = Date.now().toString(36) + Math.random().toString(36).substr(2);
      chatId.updateChatId(uniqueId);

      const prompt = await httpClient.getCodeSearchPrompt({
        files: [
          {
            code: selectText,
            language: languageId,
            name: fullPath ?? "",
          },
        ],
        searchTargetDirs: [],
        query: message,
        codebaseSearch: false,
        commit: await repoChatService.getCommit(),
        repoName: repoChatService.repoName,
        username: userInfo?.name ?? "",
        instantApplyMode: true,
      });
      const formatQuestion = prompt.prompt;

      const fromCurrentFileAndSelection = async (): Promise<MentionNodeV2Structure_Selection> => {
        const res = await firstValueFrom(kwaiPilotBridgeAPI.observableAPI.currentFileAndSelection());
        if (!res || !res.range) {
          throw new Error("获取当前文件和选中内容失败");
        }
        return {
          type: "selection",
          range: res.range,
          content: res.content,
          uri: res.uri,
          relativePath: res.relativePath,
        };
      };

      const structure: MentionNodeV2Structure_Selection = section
        ? {
            type: "selection",
            range: {
              start: { line: section.lineStart, character: 0 },
              end: { line: section.lineEnd, character: section.offsetEnd },
            },
            content: selectText,
            uri: params.uri,
            relativePath: params.relativePath,
          }
        : await fromCurrentFileAndSelection();
      const newCacheMessage: QAItem = {
        id: uniqueId,
        Q: {
          formatQuestion,
          isSelf: true,
          id: uniqueId,
          question: message,
          reply: "",
          section,
          fullPath,
          modelType: chatModel.modelType || DEFAULT_MODEL_TYPE,
          v2: true,
          plainText: message,
          editorState: createPlainTextEditorState(message),
          contextItems: [structure],
        },
        A: [
          {
            isSelf: false,
            modelType: chatModel.modelType ?? DEFAULT_MODEL_TYPE,
            id: uniqueId,
            question: formatQuestion,
            reply: "",
            section,
            fullPath,
            answerId: generateCustomUUID(),
            ttfb: 0,
            thinkInfo: undefined,
          },
        ],
      };
      let sessionId: string;
      const newSessionTime = getCurrentSessionTimeString();
      if (sessionHistory) {
        sessionId = sessionHistory.sessionId;
        recordStoreState.setSessionHistory({
          ...sessionHistory,
          cachedMessages: [
            ...(sessionHistory?.cachedMessages ?? []),
            newCacheMessage,
          ],
          sessionTime: newSessionTime,
        });
      }
      else {
        // 新建对话
        sessionId = generateCustomUUID();
        recordStoreState.setSessionHistory({
          cachedMessages: [newCacheMessage],
          sessionTime: newSessionTime,
          sessionName: message,
          sessionId,
          expiredIndex: [],
          clearContextIndex: [],
          isComposer: false,
        });
        if (summaryConversation) {
          fetchSummaryConversation(message, (chunk) => {
            updateHistoryList(sessionId, chunk, false);
          });
        }
        // 切换至该对话
        recordStoreState.setActiveSession({
          value: sessionId,
          updateHistory: false, // 不需要从 sqlite拉取历史记录
        });
      }

      const para: ReportOpt<"inlay_hints"> = {
        key: "inlay_hints",
        type: reportType,
      };
      reportUserAction(para);

      const startTime = Date.now();

      postMessageUtil({
        startTime,
        content: prompt.prompt,
        type: MessageType.SEND_MESSAGE,
        cachedMessages: sessionHistory?.cachedMessages ?? [],
        chatType: "intelligentChat",
        sessionId,
        uniqueId,
        useSearch: false,
        refFiles: [],
        chatModel: chatModel || { modelType: DEFAULT_MODEL_TYPE },
        docId: -1,
        expiredIndex: sessionHistory ? [...sessionHistory.expiredIndex] : [],
        clearContextIndex: sessionHistory
          ? [...sessionHistory.clearContextIndex]
          : [],
        /* action 的操作 tab 是 普通的 chat */
        vendor: ReocrdStoreVendorEnum.chat,
      });
    },
    [navigate, updateHistoryList, userInfo],
  );

  useEffect(() => {
    eventBus.on("actionResult", handleActionResult);
    eventBus.on("actionForCode", handleActionForCode);
    kwaiPilotBridgeAPI.onActionForCode(handleActionForCode);
    return () => {
      eventBus.off("actionResult");
      eventBus.off("actionForCode");
    };
  }, [
    handleActionForCode,
    handleActionResult,
  ]);
  return null;
};
