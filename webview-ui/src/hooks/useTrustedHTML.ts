import { useEffect, useRef } from "react";
import { setTrustedHTML } from "@/utils/trustedHtml";

/**
 * 用于监听 HTML 内容变化并自动更新的 Hook
 * @param html 需要监听的 HTML 内容
 * @returns 返回一个 ref，用于绑定到 DOM 元素
 */
export function useTrustedHTML<T extends HTMLElement = HTMLDivElement>(html: string) {
  const elementRef = useRef<T | null>(null);

  useEffect(() => {
    if (elementRef.current) {
      setTrustedHTML(elementRef.current, html);
    }
  }, [html]);

  return elementRef;
}
