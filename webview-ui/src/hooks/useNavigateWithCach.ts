import { getGlobalObject } from "@/utils/globalObject";
import { setLocalStorageValue } from "@/utils/localStorage";
import { logger } from "@/utils/logger";
import { NavigateOptions, useNavigate } from "react-router-dom";

const useNavigateWithCache = () => {
  const n = useNavigate();

  const navigate = (
    to: "/chat" | "/inline-chat" | "/composer" | "/composer-v2" | number,
    options?: NavigateOptions,
  ) => {
    logger.info("navigate to", "navigate", { value: to });
    setLocalStorageValue("activePath", to.toString());
    typeof to === "number" ? n(to) : n(to, options);
  };

  return navigate;
};
export default useNavigateWithCache;

/**
 * 非hook版本的导航函数，可以在非React组件环境中使用
 * 需要传入一个history对象以执行实际的导航操作
 */
export const navigateWithCache = (
  to: "/chat" | "/inline-chat" | "/composer" | "/composer-v2" | number,
  options?: NavigateOptions,
) => {
  logger.info("navigate to", "navigate", { value: to });
  setLocalStorageValue("activePath", to.toString());
  const router = getGlobalObject("router");
  typeof to === "number" ? router.navigate(to) : router.navigate(to, options);
};
