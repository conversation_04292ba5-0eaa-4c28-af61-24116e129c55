import { kwaiPilotBridgeAPI } from "@/bridge";
import { useEffect } from "react";
import { WEBVIEW_BRIDGE_EVENT_NAME, WebviewBridgeParams } from "shared/lib/bridge";

export function useKwaipilotBridgeMessageListener<T extends WEBVIEW_BRIDGE_EVENT_NAME>(
  event: T,
  listener: (payload: WebviewBridgeParams[T]) => unknown,
) {
  useEffect(() => {
    return kwaiPilotBridgeAPI.addMessageListener(event, listener);
  }, [event, listener]);
}
