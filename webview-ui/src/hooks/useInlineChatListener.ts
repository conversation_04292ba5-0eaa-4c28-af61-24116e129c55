import eventBus from "@/utils/eventBus";
import { useCallback, useEffect } from "react";
import { useInlineChatStore } from "@/store/inline-chat";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { getUserIntention, startInlineChat } from "@/http/api/inline-chat";
import { v4 as uuidv4 } from "uuid";
import baseInfoManager from "@/utils/baseInfo";
import repoChatService from "@/services/repo-chat";
import { InlineChatUserIntention } from "@shared/types/inline-chat";
import { reportUserAction } from "@/utils/weblogger";
import { navigateWithCache } from "./useNavigateWithCach";
import { useUserStore } from "@/store/user";
import { getUserInfo } from "@/utils/getUserInfo";

const useInlineChatListener = () => {
  const inlineChatInfo = useInlineChatStore(
    state => state.inlineChatHistory?.quote,
  );
  const navigate = navigateWithCache;
  const userInfo = useUserStore(state => state.userInfo);

  const handleInlineChat = useCallback(
    async ({
      chatId,
      question,
      sessionId,
    }: {
      chatId: string;
      question: string;
      sessionId: string;
    }) => {
      if (!inlineChatInfo) {
        kwaiPilotBridgeAPI.showToast({
          message: "未选中代码，不支持解释代码",
          level: "error",
        });
        return;
      }
      const { filepath, content, prefix, suffix } = inlineChatInfo;
      const language = filepath?.split(".").pop();
      const username = userInfo?.name ?? (await getUserInfo())?.name;
      const userIntentionBody = {
        sessionId,
        chatId: uuidv4(),
        question,
      };
      const inlinChatBody = {
        username,
        platform: baseInfoManager.ide,
        prefix,
        suffix,
        middle: content,
        language,
        prompt: question,
        projectName: repoChatService.repoName,
        gitRemote: repoChatService.remoteUrl,
        branchName: await repoChatService.getBranch(),
        openedFilePath: filepath,
        deviceId: baseInfoManager.deviceId,
        chatId,
        sessionId,
      };
      const userIntention = await getUserIntention(userIntentionBody);
      if (
        userIntention === InlineChatUserIntention.EXPLAIN
        || userIntention === InlineChatUserIntention.TEXT
      ) {
        reportUserAction({
          key: "inline_chat",
          type:
            userIntention === InlineChatUserIntention.EXPLAIN
              ? "EXPLAIN"
              : "EXPLAIN",
          content: question,
        });
        navigate("/chat");
        eventBus.emit("actionForCode", {
          type:
            userIntention === InlineChatUserIntention.EXPLAIN
              ? "explain"
              : "comment",
          selectText: content,
          languageId: language ?? "",
          range: {
            start: { line: inlineChatInfo.startLine, character: 0 },
            end: { line: inlineChatInfo.endLine + 1, character: 0 },
          },
          uri: inlineChatInfo.uri,
          relativePath: inlineChatInfo.relativePath,
        });
      }
      else {
        // 代码生成
        startInlineChat(inlinChatBody);
      }
    },
    [inlineChatInfo, navigate, userInfo?.name],
  );

  useEffect(() => {
    eventBus.on("startInlineChat", handleInlineChat);
    return () => {
      eventBus.off("startInlineChat", handleInlineChat);
    };
  }, [handleInlineChat]);
};

export default useInlineChatListener;
