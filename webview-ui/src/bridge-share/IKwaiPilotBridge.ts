import {
  ExtractNativeBridgePayload,
  ExtractNativeBridgeResult,
  NATIVE_BRIDGE_EVENT_NAME,
  WEBVIEW_BRIDGE_EVENT_NAME,
  WebviewBridgeParams,
  WebviewBridgeResult,
} from "@shared/types/bridge";
import { BridgeMessage, Bridge } from "./types";

/**
 * KwaiPilot Bridge 接口定义
 * 定义了与原生端通信的公共方法
 */
export interface IKwaiPilotBridge {
  /**
   * 设置 Bridge 实现
   * @param bridgeImplementation Bridge 实现对象
   */
  setBridge(bridgeImplementation: Bridge | any): void;
  /**
   * 调用原生端
   * @param eventName 事件名称
   * @param message 消息内容
   * @param callback 可选的回调函数
   */
  postMessage<T extends NATIVE_BRIDGE_EVENT_NAME>(
    eventName: T,
    message: BridgeMessage<ExtractNativeBridgePayload<T>>,
    callback?: (params: ExtractNativeBridgeResult<T>) => void,
  ): Promise<void>;

  /**
   * 注册native可以调用的handler
   * 注意只能调用一次，多次调用会覆盖
   * @param eventName 事件名称
   * @param handler 处理函数
   */
  onMessage<T extends WEBVIEW_BRIDGE_EVENT_NAME>(
    eventName: T,
    handler: (params: WebviewBridgeResult[T]) => void,
  ): Promise<void>;

  /**
   * 发送单向无状态消息
   * 旧版 bridge API 强行要求有 req - res 的结构，如果要发送单向消息，使用这个结构
   * @param eventName 事件名称
   * @param message 消息内容
   */
  postOneWayMessage<T extends NATIVE_BRIDGE_EVENT_NAME>(
    eventName: T,
    message: ExtractNativeBridgePayload<T>,
  ): void;

  /**
   * 添加消息监听器，无状态
   * @param event 事件名称
   * @param listener 监听器函数
   * @returns 返回移除监听器的函数
   */
  addMessageListener<T extends WEBVIEW_BRIDGE_EVENT_NAME>(
    event: T,
    listener: (payload: WebviewBridgeParams[T]) => unknown,
  ): () => void;

  /**
   * 生成唯一ID
   * @returns 返回UUID格式的字符串
   */
  generateId(): string;
}
