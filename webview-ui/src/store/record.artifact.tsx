import { KwaipilotAction } from "@ks-kwaipilot/artifact-message-parser/lib/actions";
import {
  StreamingMessageParser,
  ArtifactCallbackData,
  ActionCallbackData,
  FileCallbackDataState,
} from "@ks-kwaipilot/artifact-message-parser/lib/messageParser";
import { QAItem } from "@shared/types/chatHistory";
import { produce } from "immer";
import { StateCreator } from "zustand";
import { type RecordBaseSlice } from "./record";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { ArtifactStreamHandler } from "../bridge-share/BaseKwaiPilotBridgeAPI";
import { AUTO_THINK_MODEL_SET, THINK_MODEL_SET } from "shared/lib/business";

export interface ArtifactState {
  id: string;
  title: string;
  attr: Record<string, string>;
  closed: boolean;
  actions: Record<string, ActionState>;
}

export type BaseActionState = KwaipilotAction & {
  closed: boolean;
  actionId: string;
};

export type ActionState = BaseActionState;

export type ArtifactUpdateState = Pick<ArtifactState, "title" | "closed">;
export type ActionUpdateState = Pick<ActionState, "content" | "closed">;

export interface RecordArtifactSlice {
  /**
   * Record<A.answer[].answerId, A.answer[].reply>
   */
  parsedAnswerReply: Record<string, string>;
  parseMessages: (messages: QAItem[]) => void;
  artifacts: Record<string, ArtifactState>;
  addArtifact: (artifact: ArtifactCallbackData) => void;
  updateArtifact: (
    artifact: ArtifactCallbackData,
    state: Partial<ArtifactUpdateState>,
  ) => void;
  updateAction: (
    actionQuery: { actionId: string;messageId: string },
    state: ActionUpdateState,
  ) => void;
  addAction: (data: ActionCallbackData, state: { closed: boolean }) => unknown;
  runAction: (data: ActionCallbackData) => unknown;
  streamingFile: FileCallbackDataState | null;
  setStreamingFile: (state: FileCallbackDataState) => unknown;
}

export const createRecordArtifactSlice: StateCreator<
  RecordArtifactSlice & RecordBaseSlice,
  [],
  [],
  RecordArtifactSlice
> = (set, get) => {
  const artifactStreamMap: Record</* messageId */string, ArtifactStreamHandler> = {};
  function isMessageStreaming(messageId: string): boolean {
    const loadingStatus = get().loadingStatu;

    const loadingQAItem = loadingStatus?.status === "loading"
      ? get().sessionHistory?.cachedMessages.find(v => v.id === loadingStatus.id)
      : undefined;

    return loadingStatus?.status === "loading" && loadingQAItem?.A.at(-1)?.answerId === messageId;
  }
  const messageParser = new StreamingMessageParser({
    callbacks: {
      onArtifactOpen: (data) => {
        console.log("artifactOpen", data);

        if (isMessageStreaming(data.messageId)) {
          if (artifactStreamMap[data.messageId]) {
            artifactStreamMap[data.messageId].end("interrupted by another session");
          }
          artifactStreamMap[data.messageId] = kwaiPilotBridgeAPI.createArtifactPreviewStream();
        }
        get().addArtifact(data);
      },
      onArtifactClose: (data) => {
        console.log("artifactClose", data);

        get().updateArtifact(data, { closed: true });
        if (isMessageStreaming(data.messageId)) {
          if (artifactStreamMap[data.messageId]) {
            artifactStreamMap[data.messageId].end();
            delete artifactStreamMap[data.messageId];
          }
        }
      },
      onActionOpen: (data) => {
        console.log("actionOpen", data);

        // we only add shell actions when when the close tag got parsed because only then we have the content
        get().addAction(data, { closed: false });
      },
      onActionClose: (data) => {
        console.log("actionClose", data);

        // workbenchStore.runAction(data);
        get().updateAction(data, { closed: true, content: data.action.content });
      },
      onFileStream: ({ action, data, actionId, messageId }) => {
        console.log("onFileStream", data.content);

        get().updateAction({
          actionId, messageId,
        }, {
          closed: false,
          content: action.content,
        });
        if (isMessageStreaming(messageId)) {
          get().setStreamingFile(data);

          if (artifactStreamMap[messageId]) {
            const artifact = get().artifacts[messageId];
            // 触发预览
            artifactStreamMap[messageId].sendData({
            // TODO: provider 从 message 中提取
              provider: "navi",
              artifact: {
                id: artifact.id,
                title: artifact.title,
                attr: artifact.attr,
                actions: Object.values(artifact.actions).map(act => ({
                  content: act.actionId === actionId ? data.content : act.content,
                  attr: {
                    filePath: act.filePath,
                    type: act.type,
                  },
                  closed: act.closed,
                })),
              },
            });
          }
        }
      },
    },
  });
  return {
    artifacts: {},
    parsedAnswerReply: {},
    parseMessages(messages) {
      let reset = false;

      const isLoading = get().loadingStatu?.status === "loading";

      if (import.meta.env.DEV && !isLoading) {
        reset = true;
        messageParser.reset();
      }

      for (const qaItem of messages) {
        for (let i = 0; i < qaItem.A.length; i++) {
          const message = qaItem.A[i];
          const useThinkInfo = THINK_MODEL_SET.has(message.modelType) || AUTO_THINK_MODEL_SET.has(message.modelType);
          if (!message.isSelf) {
            const reply = useThinkInfo ? message.thinkInfo?.content || "" : message.reply || "";
            // 兼容没有 answerId 的历史数据
            const key = message.answerId || `${qaItem.id}_${i}`;
            const newParsedContent = messageParser.parse(
              key,
              reply,
            );

            set(s => produce(s, (draft) => {
              draft.parsedAnswerReply[key] = !reset
                ? (draft.parsedAnswerReply[key] || "") + newParsedContent
                : newParsedContent;
            }));
          }
        }
      }
    },

    addArtifact({ messageId, title, id, attr }: ArtifactCallbackData) {
      const artifact = get().artifacts[messageId];

      if (artifact) {
        return;
      }

      /* if (!this.artifactIdList.includes(messageId)) {
        this.artifactIdList.push(messageId);
      } */

      set(s =>
        produce(s, (draft) => {
          draft.artifacts[messageId] = {
            id,
            title,
            closed: false,
            actions: {},
            attr,
          };
        }),
      );
    },
    updateArtifact({ messageId }, state) {
      const artifact = get().artifacts[messageId];

      if (!artifact) {
        return;
      }

      set(s =>
        produce(s, (draft) => {
          draft.artifacts[messageId] = {
            ...draft.artifacts[messageId],
            ...state,
          };
        }),
      );
    },

    async addAction(data: ActionCallbackData, state: { closed: boolean }) {
      const { messageId } = data;

      const artifact = get().artifacts[messageId];

      if (!artifact) {
        throw new Error("Artifact not found");
      }

      set(s =>
        produce(s, (draft) => {
          draft.artifacts[messageId].actions[data.actionId] = {
            ...data.action,
            closed: state.closed,
            actionId: data.actionId,
          };
        }),
      );
    },
    updateAction({ actionId, messageId }, state) {
      set(s =>
        produce(s, (draft) => {
          const artifact = draft.artifacts[messageId];

          if (!artifact) {
            return;
          }
          const action = artifact.actions[actionId];
          if (!action) {
            return;
          }
          console.log("set action!", action, state);

          Object.assign(action, { ...state });
        }),
      );
    },

    async runAction(data: ActionCallbackData) {
      const { messageId } = data;

      const artifact = get().artifacts[messageId];

      if (!artifact) {
        throw new Error("Artifact not found");
      }

      // artifact.runner.runAction(data);
    },

    streamingFile: null,
    setStreamingFile(state: FileCallbackDataState) {
      if (state.status === "start") {
        set({
          streamingFile: state,
        });
      }
      else if (state.status === "stream") {
        set({
          streamingFile: state,
        });
      }
      else if (state.status === "end") {
        set({
          streamingFile: null,
        });
      }
    },
  };
};
