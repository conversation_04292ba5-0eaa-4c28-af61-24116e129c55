import { kwaiPilotBridgeAPI } from "@/bridge";
import { Config, defaultConfig } from "shared/lib/state-manager/types";
import { create } from "zustand";

export interface AutoRunSettingState {
  autoRunEnabled: boolean;
  autoRunMcpEnabled: boolean;
  commandExclude: string[];
  setAutoRunEnabled: (enabled: boolean) => Promise<void>;
  setAutoRunMcpEnabled: (enabled: boolean) => Promise<void>;
  setCommandExclude: (exclude: string[]) => Promise<void>;

}

export const useAutoRunSetting = create<AutoRunSettingState>(set => ({
  autoRunEnabled: defaultConfig[Config.COMPOSER_ENABLE_AUTO_RUN],
  autoRunMcpEnabled: defaultConfig[Config.COMPOSER_ENABLE_AUTO_RUN_MCP],
  commandExclude: defaultConfig[Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE] || [],
  setAutoRunEnabled: async (enabled: boolean) => {
    await kwaiPilotBridgeAPI.extensionSettings.$updateSetting(Config.COMPOSER_ENABLE_AUTO_RUN, enabled);
    set({ autoRunEnabled: enabled });
  },
  setAutoRunMcpEnabled: async (enabled: boolean) => {
    await kwaiPilotBridgeAPI.extensionSettings.$updateSetting(Config.COMPOSER_ENABLE_AUTO_RUN_MCP, enabled);
    set({ autoRunMcpEnabled: enabled });
  },
  setCommandExclude: async (exclude: string[]) => {
    await kwaiPilotBridgeAPI.extensionSettings.$updateSetting(Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE, exclude);
    set({ commandExclude: exclude });
  },
}));

// 初始化自动运行设置
kwaiPilotBridgeAPI.extensionSettings.$getSettings().then((settings) => {
  useAutoRunSetting.setState({
    autoRunEnabled: settings[Config.COMPOSER_ENABLE_AUTO_RUN] ?? defaultConfig[Config.COMPOSER_ENABLE_AUTO_RUN],
    autoRunMcpEnabled: settings[Config.COMPOSER_ENABLE_AUTO_RUN_MCP] ?? defaultConfig[Config.COMPOSER_ENABLE_AUTO_RUN_MCP],
    commandExclude: settings[Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE] || defaultConfig[Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE] || [],
  });
});

kwaiPilotBridgeAPI.observableAPI.settingUpdate().subscribe(({ key, value }) => {
  // 获取所有设置并设置到store中

  if (key === Config.COMPOSER_ENABLE_AUTO_RUN) {
    useAutoRunSetting.setState({ autoRunEnabled: value as boolean });
  }
  else if (key === Config.COMPOSER_ENABLE_AUTO_RUN_MCP) {
    useAutoRunSetting.setState({ autoRunMcpEnabled: value as boolean });
  }
  else if (key === Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE) {
    useAutoRunSetting.setState({ commandExclude: value as string[] });
  }
});
