import { kwaiPilotBridgeAPI } from "@/bridge";
import { getCurrentSessionTimeString } from "@/utils/utils";
import { BriefSessionItem } from "@shared/types/chatHistory";
import { create } from "zustand";
import { getRecordStoreByVendor } from "./record";
import { produce } from "immer";

export interface HistoryState {
  historyList: BriefSessionItem[];
  updateHistoryList: (
    sessionId: string,
    sessionName: string,
    overwrite?: boolean
  ) => void;
  setHistoryList: (newList: BriefSessionItem[]) => void;
  updateHistoryItemName: (data: { sessionId: string; sessionName: string }) => unknown;
}

export const useHistoryStore = create<HistoryState>()((set, get) => ({
  historyList: [],
  updateHistoryList: async (sessionId, sessionName, overwrite = true) => {
    const historyList = get().historyList;
    const newList = [...historyList];
    let newName = "";
    const index = newList.findIndex(h => h.sessionId === sessionId);
    if (index === -1) {
      // 如果不存在就添加
      if (
        sessionId === getRecordStoreByVendor("chat").getState().activeSession
        || sessionId
        === getRecordStoreByVendor("composer").getState().activeSession
      ) {
        set({
          historyList: [
            ...newList,
            {
              sessionId: sessionId,
              sessionName,
              sessionTime: getCurrentSessionTimeString(),
              isComposer: false,
              isComposerV2: false,
              workspaceUri: (await kwaiPilotBridgeAPI.getWorkspaceStorageUri())?.result || "",
            },
          ],
        });
        kwaiPilotBridgeAPI.updateSessionInfo({
          sessionId,
          sessionName,
        });
      }
      return;
    }
    if (overwrite) {
      newName = sessionName;
    }
    else {
      newName = historyList[index].sessionName + sessionName;
    }
    newList[index].sessionName = newName;
    kwaiPilotBridgeAPI.updateSessionInfo({
      sessionId,
      sessionName: newName,
    });
    set({ historyList: newList });
  },
  setHistoryList: (newList) => {
    set({ historyList: newList });
  },
  updateHistoryItemName: async (data) => {
    const { sessionId, sessionName } = data;
    const historyList = get().historyList;
    const index = historyList.findIndex(h => h.sessionId === sessionId);
    const target = historyList[index];
    if (!target) {
      return;
    }
    const modified = produce(historyList, (draft) => {
      draft[index].sessionName = sessionName;
    });
    set({ historyList: modified });
    if (target.isComposerV2) {
      await kwaiPilotBridgeAPI.updateComposerSessionName({
        sessionId,
        name: sessionName,
      });
    }
    else {
      await kwaiPilotBridgeAPI.updateSessionInfo({
        sessionId,
        sessionName,
      });
    }
  },
}));
