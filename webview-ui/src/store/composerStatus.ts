import { FileStateType, SessionItem } from "@shared/types/chatHistory";
import { useMemo } from "react";
import { create } from "zustand";
import { produce } from "immer";
import { findModifiedFilesInMarkdown } from "@/components/QA/findModifiedFilesInMarkdown";

/**
 * 附带了 UI 相关的中间状态的 FileStateType
 */
export type FileStateUIType = FileStateType | "applying" | "applied";

export interface ComposerStatusState {
  updateBySession: (sessionHistory: SessionItem) => void;
  /**
   * 保存每个 chatId 下的文件状态 [chatId]<[filepath]: { state: FileStateUIType }>
   */
  fileStateMap: Record<
    /* chatId */ string,
    Record<
      /* filepath */ string,
      {
        state: FileStateUIType;
      }
    >
  >;
  /* session id, for possible cleaning state */
  sessionId: string;
  updateFileState: (
    chatId: string,
    filepath: string,
    state: FileStateUIType,
  ) => void;

  checkPoint?: {
    questionId: string;
  };

  updateCheckPoint: (params?: ComposerStatusState["checkPoint"]) => void;
}

/**
 * composer 全局状态
 */
export const useComposerStatusStore = create<ComposerStatusState>(
  (set, get) => ({
    fileStateMap: {},
    sessionId: "",
    updateBySession(sessionHistory: SessionItem) {
      if (!sessionHistory.isComposer) {
        set({
          fileStateMap: {},
        });
        return;
      }
      const initialObject: ComposerStatusState["fileStateMap"]
        = get().sessionId === sessionHistory.sessionId ? get().fileStateMap : {};

      const result = produce(initialObject, (draft) => {
        for (const message of sessionHistory.cachedMessages) {
          const lastAnswer = message.A.at(-1);
          if (lastAnswer) {
            draft[message.id] ||= {};
            const modifiedFiles = lastAnswer.reply
              ? findModifiedFilesInMarkdown(lastAnswer.reply)
              : [];
            for (const filepath of modifiedFiles.map(v => v.filepath)) {
              draft[message.id][filepath] = {
                state:
                  draft[message.id][filepath]?.state
                  || lastAnswer.affectedFileState?.[filepath]?.state
                  || "init",
              };
            }
          }
        }
      });
      set({
        fileStateMap: result,
        sessionId: sessionHistory.sessionId,
      });
    },
    updateFileState(chatId: string, filepath: string, state: FileStateUIType) {
      set(s =>
        produce(s, (draft) => {
          if (!draft.fileStateMap[chatId]) {
            draft.fileStateMap[chatId] = {};
          }
          draft.fileStateMap[chatId][filepath] = {
            state,
          };
        }),
      );
    },
    updateCheckPoint(params) {
      set({ checkPoint: params });
    },
  }),
);

export function useGlobalComposerState() {
  const fileStateMap = useComposerStatusStore(state => state.fileStateMap);

  const state = useMemo<FileStateUIType>(() => {
    const defaultState: FileStateUIType = "init";
    for (const chatState of Object.values(fileStateMap)) {
      for (const file of Object.values(chatState)) {
        if (file.state === "rejected") {
          // 只要有一个 reject 就判定为 reject
          return "rejected";
        }
        if (file.state === "applying") {
          // 只要有一个 applying 就判定为 applying
          return "applying";
        }
        if (file.state === "accepted") {
          // 只要有一个 applied 就判定为 applied
          return "accepted";
        }
      }
    }
    return defaultState;
  }, [fileStateMap]);
  return {
    state,
  };
}
