/// <reference types="vitest" />
// NOTE: 用于插件webview 产物编译，修改时需要注意是否同步 ./vite-export.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "tailwindcss";
import autoprefixer from "autoprefixer";
import tsconfigPaths from "vite-tsconfig-paths";
import { resolve } from "path";
import svgr from "vite-plugin-svgr";
import fs from "fs";

// https://vitejs.dev/config/
export default defineConfig({
  base: "./",
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
      "@shared": resolve(__dirname, "../src/shared"),
    },
    /**
     * 这是 vite 的默认配置, 但还是要显式写出来
     *
     * 以下是 vitest 的处理方式
     * ```
     *
          resolve: {
            // by default Vite resolves `module` field, which not always a native ESM module
            // setting this option can bypass that and fallback to cjs version
            mainFields: [],
            alias: testConfig.alias,
            conditions: ["node"]
          },
     * ```

      但 vite 内部初始化逻辑是 resolve?.mainFields ?? DEFAULT_MAIN_FIELDS 会导致变成空数组

      升级 vite 或许可以解决这个配置问题
     *  */
    mainFields: [
      "browser",
      "module",
      "jsnext:main", // moment still uses this...
      "jsnext",
    ],
  },
  plugins: [
    tsconfigPaths(),
    react(),
    svgr(),
    // bodyOperationCheckPlugin(), // @TODO: 太频繁了 先注释掉
  ],
  server: {
    cors: {
      origin: ["vscode-webview://*"],
      methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
      credentials: true,
    },
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers":
        "Content-Type, Authorization, X-Requested-With",
    },
    hmr: {
      protocol: "ws",
      host: "localhost",
      port: 5173,
      clientPort: 5173,
      overlay: true,
      timeout: 30000,
    },
    watch: {
      usePolling: true,
      interval: 100,
    },
    strictPort: true,
    port: 5173,
  },
  build: {
    outDir: "build",
    minify: true,
    rollupOptions: {
      output: {
        entryFileNames: `assets/[name].js`,
        chunkFileNames: `assets/[name].js`,
        assetFileNames: `assets/[name].[ext]`,
        format: "esm",
        generatedCode: {
          symbols: true,
        },
        preserveModules: false,
      },
      onwarn(warning, warn) {
        if (warning.code === "MODULE_LEVEL_DIRECTIVE") {
          return;
        }
        warn(warning);
      },
      plugins: [
        {
          name: "generate-version",
          writeBundle() {
            const version = {
              version: process.env.npm_package_version || "1.0.0",
            };

            fs.writeFileSync(
              resolve(__dirname, "build/version.json"),
              JSON.stringify(version, null, 2),
            );
          },
        },
      ],
    },
  },
  css: {
    postcss: {
      plugins: [tailwindcss, autoprefixer as any],
    },
    modules: { localsConvention: "camelCase" },
  },
  test: {
    environment: "jsdom",
    setupFiles: ["./src/tests/setup.ts"],
  },
});
