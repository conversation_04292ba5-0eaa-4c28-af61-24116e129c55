# `webview-ui` 目录

开发看docs文档 https://docs.corp.kuaishou.com/d/home/<USER>

这个目录包含了将在webview环境中执行的所有代码。它可以被视为webview的"前端"代码所在的地方。

## 用途与渲染环境

该UI组件设计为在两种不同的宿主环境下运行：

### 1. VSCode插件环境

作为VSCode插件的一部分，webview-ui会被渲染到VSCode的webview上下文中（本质上是在一个iframe内）。在这种模式下：
- UI与VSCode主界面隔离
- 通过VSCode提供的消息传递API与插件通信
- 遵循VSCode的webview安全限制和生命周期

### 2. Kwaipilot-IDE环境

作为Kwaipilot-IDE需要的AI助手，webview-ui以组件形式渲染到IDE的渲染进程中。在这种模式下：
- UI与IDE的工作区共享同一个document对象
- 会被挂载到IDE指定的DOM节点上
- 可以与IDE的其他组件进行更直接的交互
- 需要考虑与IDE主题和样式的协调

## DOM层级结构图示

### VSCode插件环境
```html
<body>
  <div id="root"/>
</body>
```
### Kwaipilot-IDE环境
```html
<div data-element-type="body">
  <div data-element-type="root"/>
</div>
```
## 目录内容类型

- 前端框架代码（如React、Svelte、Vue等）
- JavaScript文件
- CSS文件
- 资源文件（如图片、图标等）

## 开发注意事项

由于需要在两种不同的环境中运行，开发时应当：
- 使用响应式设计确保在不同容器大小下的适配性
- 注意DOM操作的兼容性，避免直接操作document全局对象
- 通过适配层处理不同环境下的通信机制差异
- 实现运行时环境检测以调整组件行为

## Lint规则与代码规范

为确保代码质量和兼容性，本项目实施了严格的lint规则，特别是针对两种不同宿主环境的特殊规范：

### 自定义规则

以下自定义规则对于保证两种环境下的兼容性至关重要：

- **no-direct-body-operation**: 禁止直接操作body元素，应通过适配层处理DOM操作
- **no-dangerous-html**: 禁止使用危险的HTML操作（如innerHTML），以防XSS攻击，应该使用 useTrustedHTML 方法
- **no-direct-chakra-import**: 禁止直接从Chakra-UI导入组件，应使用项目封装的组件
- **no-direct-iconify-import**: 禁止直接从Iconify导入图标，应使用项目封装的图标组件
- **no-direct-kid-import**: 禁止直接导入KID库，应通过适配层使用
- **no-navigator-clipboard**: 禁止直接使用navigator.clipboard，应该使用 kwaiPilotBridgeAPI.copyToClipboard 调用 VS 官方提供的剪贴板方法
