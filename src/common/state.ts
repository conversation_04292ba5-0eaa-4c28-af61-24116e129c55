import * as vscode from "vscode";

export interface StateSchema {
  codeSearchSelectFileHistory: string[];
  codeSearchSelectDirHistory: string[];
  codeSearchSelectOpenFileHistory: string[];
  codeSearchSelectOpenDirHistory: string[];
}

export class StateManager {
  private workspaceState: vscode.Memento | null = null;
  private globalState: vscode.Memento | null = null;
  private initialized: boolean = false;

  // 私有化构造函数，防止外部实例化
  private constructor(context: vscode.ExtensionContext) {
    this.initState(context);
  }

  // 单例获取方法
  public static initInstance(context: vscode.ExtensionContext): StateManager {
    if (!StateManager.instance) {
      StateManager.instance = new StateManager(context);
    }
    return StateManager.instance;
  }

  // 私有静态实例
  private static instance: StateManager;

  public static getInstance() {
    if (!StateManager.instance) {
      throw new Error("StateManager not initialized");
    }
    return StateManager.instance;
  }

  // 初始化方法
  private initState(context: vscode.ExtensionContext): void {
    if (!this.initialized) {
      this.workspaceState = context.workspaceState;
      this.globalState = context.globalState;
      this.initialized = true;
    }
  }

  // 确保已初始化
  private ensureInitialized(): void {
    if (!this.initialized) {
      throw new Error("StateManager not initialized");
    }
  }

  // 公开的业务方法
  public get<K extends keyof StateSchema>(key: K, defaultValue: StateSchema[K], global: boolean = false): StateSchema[K] {
    this.ensureInitialized();
    const state = global ? this.globalState : this.workspaceState;
    return state!.get<StateSchema[K]>(key, defaultValue);
  }

  public async update<K extends keyof StateSchema>(key: K, value: StateSchema[K], global: boolean = false): Promise<void> {
    this.ensureInitialized();
    const state = global ? this.globalState : this.workspaceState;
    await state!.update(key, value);
  }

  public async delete<K extends keyof StateSchema>(key: K, global: boolean = false): Promise<void> {
    this.ensureInitialized();
    const state = global ? this.globalState : this.workspaceState;
    await state!.update(key, undefined);
  }

  public exists<K extends keyof StateSchema>(key: K, global: boolean = false): boolean {
    this.ensureInitialized();
    const state = global ? this.globalState : this.workspaceState;
    return state!.get(key) !== undefined;
  }
}
