export const scope = "kwaipilot";
export enum Commands {
  LOGIN = `${scope}.login`, // 登录
  LOGOUT = `${scope}.logout`, // 登出
  HOME = `${scope}.home`, // 打开首页
  INLINE_CHAT = `${scope}.inlineChat`, // 行内会话
  HELP = `${scope}.help`, // 帮助页面
  FEEDBACK = `${scope}.feedback`, // 用户反馈
  OPEN_PANEL = `${scope}.Open Kwaipilot Panel`, // 打开面板

  SETTINGS = `${scope}.settings`, // 打开设置页

  QUICK_ASK = `${scope}.quickAsk`, // 快速问答
  SHOW_DIFF = `${scope}.showDiff`, // 展示diff
  FILE_DIFF = `${scope}.fileDiff`, // 文件diff

  EXPLAIN = `${scope}.explain`, // 代码解释
  GENERATE_COMMENT_MESSAGE = `${scope}.generateCommentMessage`, // 代码注释
  GENERATE_UNIT_TEST = `${scope}.generateUnitTest`, // 生成单元测试
  ADD_DATA_ANNOTATION = `${scope}.addDataAnnotation`, // 添加数据标注
}
