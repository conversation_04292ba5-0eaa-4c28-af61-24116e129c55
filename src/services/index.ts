import { BaseModule } from "../base";
import { <PERSON>textManager } from "../base/context-manager";
import { C<PERSON> } from "../base/context-manager/manager";
import { CoreModule } from "../core";

export class ServiceModule {
  constructor(readonly ext: ContextManager) {}

  protected getBase<T extends BaseModule>(key: Ctor<T>) {
    return this.ext.getBase(key);
  }

  protected getCore<T extends CoreModule>(key: Ctor<T>) {
    return this.ext.getCore<T>(key);
  }

  protected getService<T extends ServiceModule>(key: Ctor<T>) {
    return this.ext.getService<T>(key);
  }

  protected get context() {
    return this.ext.context;
  }
}
