import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import { Commands } from "../../commands";
import { GitUtils } from "../../gitUtils";
import { LRUCache } from "lru-cache";
import { Ast } from "../../core/ast";
import { CodeSection } from "../../shared/types";
import { LoggerManager } from "../../base/logger";
import { ConfigManager } from "../../base/state-manager";
import { Config } from "shared/lib/state-manager/types";

interface CheckMsg {
  msg: string;
}
interface FuncInfo extends CheckMsg {
  pos?: vscode.Position;
  name?: string;
}
interface SelectionInfo extends CheckMsg {
  selection?: vscode.Selection;
}
interface ExistsAnnotation {
  id: number;
  userName: string;
  markTime: number;
  repoName: string;
  branchName: string;
  filePath: string;
  functionName: string;
  funcBeginInd: number;
  funcEndInd: number;
  funcDescription: string;
}

const tags = {
  cb: "[cb] 代码块描述...\n",
  ce: "[ce]\n",
  func: ["/**\n", " * FUNC_DES: 函数功能描述\n", " */\n"],
};

const AnnotationMap: Record<string, string> = {
  "c": "//",
  "cpp": "//",
  "csharp": "//",
  "java": "//",
  "python": "#",
  "javascript": "//",
  "javascriptreact": "//",
  "typescript": "//",
  "typescriptreact": "//",
  "vue": "//",
  "ruby": "#",
  "perl": "#",
  "shell": "#",
  "shellscript": "#",
  "powershell": "#",
  "php": "//",
  "php_alternative": "#",
  "swift": "//",
  "kotlin": "//",
  "rust": "//",
  "go": "//",
  "objective-c": "//",
  "sql": "--",
  "r": "#",
  "matlab": "%",
  "lua": "--",
  "haskell": "--",
  "julia": "#",
  "dart": "//",
};

const gitUtil = new GitUtils();
const cache = new LRUCache<string, Promise<{ list: ExistsAnnotation[] }>>({
  max: 30, // 最大缓存数量
  ttl: 1000 * 60 * 10, // 10分钟
});

export class AnnotationTagService extends ServiceModule {
  constructor(ext: ContextManager) {
    super(ext);
    this.initService();
    this.activateColor();
  }

  initService() {
    // 注册标注命令
    const dataAnnotationCmd = vscode.commands.registerCommand(
      Commands.ADD_DATA_ANNOTATION,
      () => {
        this.dataAnnotation();
      },
    );
    // 监听文本编辑器的变化, 并控制右键菜单的显示
    const selectionChangeListener = vscode.window.onDidChangeTextEditorSelection(
      () => this.updateContextMenu(),
    );

    this.updateContextMenu();

    this.context.subscriptions.push(
      dataAnnotationCmd,
      selectionChangeListener,
    );
  }

  async dataAnnotation() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return;
    }
    const codeSection = this.formatDocument();
    if (!codeSection) {
      return;
    }
    // 获取函数位置
    const { name: funcName = "", pos, msg: funcMsg } = this.getFunctionLine(
      editor,
      codeSection,
    );
    // 获取选取位置
    const { selection, msg } = this.getRealSelectionRange(editor);
    // 检查是否已经标注一期

    if (funcMsg) {
      return this.showInformationMessage(funcMsg);
    }
    const list = await this.findExistingAnnotation(editor.document);
    if (pos && funcName) {
      const exists = list.find((e) => {
        const names = funcName.split(" > ");
        while (names.length) {
          if (e.functionName === names.join(" > ")) {
            return true;
          }
          names.pop();
        }
        return false;
      });
      if (exists) {
        return this.showInformationMessage(
          "当前选中代码已被标注，请选择其他代码块",
        );
      }
      if (await this.addFunctionBlockComment(editor, codeSection)) {
        return;
      }
    }

    if (msg) {
      return this.showInformationMessage(msg);
    }
    if (selection) {
      const startLine = selection.start.line;
      const endLine = selection.end.line;
      const func = codeSection.reverse().find((e) => {
        return (
          endLine + 1 >= e.lineStart
          && startLine + 1 <= e.lineEnd
          && list.find(row => row.functionName === e.name)
        );
      });
      if (func?.name) {
        return this.showInformationMessage(
          "当前选中代码已被标注，请选择其他代码块",
        );
      }
      this.addCodeBlockComment();
    }
  }

  /**
   * 通过判断选区, 控制右键菜单是否显示数据增强功能
   * @todo 节流, 但是节流会导致一个问题，如果直接到新坐标右键，则判断逻辑还是根据之前的光标判断的，会导致判断出错
   */
  updateContextMenu() {
    const editor = vscode.window.activeTextEditor;

    if (!editor) {
      vscode.commands.executeCommand(
        "setContext",
        "kwaipilot.canDataAnnotation",
        false,
      );
      return;
    }
    // 格式化文档
    const sectionList = this.formatDocument();
    if (!sectionList) {
      return;
    }
    // 优先判断选区
    const { selection } = this.getRealSelectionRange(editor);
    if (selection) {
      vscode.commands.executeCommand(
        "setContext",
        "kwaipilot.canDataAnnotation",
        true,
      );
      return;
    }
    const { name } = this.getFunctionLine(editor, sectionList);
    if (name) {
      vscode.commands.executeCommand(
        "setContext",
        "kwaipilot.canDataAnnotation",
        true,
      );
      return;
    }

    vscode.commands.executeCommand(
      "setContext",
      "kwaipilot.canDataAnnotation",
      false,
    );
  }

  // #region 块注释
  async addCodeBlockComment() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) return;
    const { selection, msg } = await this.getRealSelectionRange(editor);

    // 检查选区是否为空
    if (msg) {
      this.logger.info("has message", "annotation-tag", {
        value: msg,
      });
      this.showInformationMessage(msg);
      return false;
    }
    const { document } = editor;
    if (!selection) return;
    const startCol = document.lineAt(selection.start.line)
      .firstNonWhitespaceCharacterIndex;
    const endCol = document.lineAt(selection.end.line)
      .firstNonWhitespaceCharacterIndex;
    const start = new vscode.Position(selection.start.line, 0);
    const end = new vscode.Position(selection.end.line + 1, 0);

    const language = document.languageId || "";
    const cb = this.addSpaces(
      this.generateAnnotationText("cb", language),
      startCol,
    );
    const ce = this.addSpaces(
      this.generateAnnotationText("ce", language),
      endCol,
    );

    const edit = new vscode.WorkspaceEdit();
    edit.insert(document.uri, end, ce);
    edit.insert(document.uri, start, cb);
    vscode.workspace.applyEdit(edit);

    setTimeout(() => {
      const pos = new vscode.Position(start.line, cb.length);
      this.setSelection(pos, editor);
    }, 100);
    return true;
  }

  // 获取选区内真实内容的部分
  getRealSelectionRange(editor?: vscode.TextEditor): SelectionInfo {
    if (!editor) return { msg: "" };
    const { document, selection } = editor;

    let startLine = selection.start.line;
    let endLine = selection.end.line;

    // 查找选区中的第一个非空行
    while (
      startLine <= endLine
      && document.lineAt(startLine).isEmptyOrWhitespace
    ) {
      startLine++;
    }

    // 查找选区中的最后一个非空行
    while (
      endLine >= startLine
      && document.lineAt(endLine).isEmptyOrWhitespace
    ) {
      endLine--;
    }

    if (startLine > endLine) {
      return { msg: "" };
    }
    // 更新选区
    const startPosition = new vscode.Position(startLine, 0);
    const endPosition = document.lineAt(endLine).range.end;
    const realSelection = new vscode.Selection(startPosition, endPosition);
    // 检查选区是否符合规则
    if (!this.checkSelection(realSelection, document)) {
      return { msg: "选中代码块已有标注，请重新框选" };
    }
    return { selection: realSelection, msg: "" };
  }

  // 检查选区内及上下区域是否符合标注规则
  checkSelection(
    selection: vscode.Selection,
    document: vscode.TextDocument,
  ): boolean {
    const start = selection.start.line;
    const end = selection.end.line;
    const flag = this.getAnnotationFlag(document.languageId || "");
    const cb = `${flag} [cb]`;
    const ce = `${flag} [ce]`;

    // 检查当前选区内是否包含[cb]或[ce]
    for (let i = start; i <= end; i++) {
      const line = document.lineAt(i);
      if (line.text.includes(cb) || line.text.includes(ce)) {
        return false;
      }
    }
    // 检查选区前第一个标记是否是ce
    for (let i = start - 1; i >= 0; i--) {
      const line = document.lineAt(i);
      const hasCB = line.text.includes(cb);
      const hasCE = line.text.includes(ce);
      if (hasCB || hasCE) {
        if (hasCB) {
          return false;
        }
        break;
      }
    }
    // 检查选区后第一个标记是否是cb
    for (let i = end + 1; i < document.lineCount; i++) {
      const line = document.lineAt(i);
      const hasCB = line.text.includes(cb);
      const hasCE = line.text.includes(ce);
      if (hasCB || hasCE) {
        if (hasCE) {
          return false;
        }
        break;
      }
    }
    return true;
  }

  // #endregion

  // #region 函数块注释
  async addFunctionBlockComment(
    editor: vscode.TextEditor,
    sectionList: CodeSection[],
  ) {
    if (!editor) return;
    const { pos: funcPos, msg } = await this.getFunctionLine(
      editor,
      sectionList,
    );
    if (msg) {
      this.logger.info("has message", "annotation-tag", {
        value: msg,
      });
      this.showInformationMessage(msg);
      return false;
    }
    if (!funcPos) return false;
    const startCol = editor.document.lineAt(funcPos.line)
      .firstNonWhitespaceCharacterIndex;
    const funcDes = tags.func.map(e => this.addSpaces(e, startCol)).join("");
    this.insertAnnotation(
      editor.document,
      new vscode.Position(funcPos.line, 0),
      funcDes,
    );
    setTimeout(() => {
      const pos = new vscode.Position(
        funcPos.line + 1,
        tags.func[1].length + startCol,
      );
      this.setSelection(pos, editor);
    }, 100);
    return true;
  }

  getFunctionLine(
    editor: vscode.TextEditor,
    sectionList: CodeSection[],
  ): FuncInfo {
    if (!editor)
      return {
        msg: "",
      };
    const selection = editor.selection;

    if (!selection.isEmpty) {
      return { msg: "" };
    }

    const firstNonEmptyLinePos = this.getFirstNonEmptyLineBelowCursor(editor);

    if (!firstNonEmptyLinePos) {
      return { msg: "" };
    }
    // const sectionList = formatDocument(editor.document);
    const func = sectionList?.find(
      s => s.lineStart === firstNonEmptyLinePos.line + 1,
    );
    if (!func) {
      return {
        msg: "",
      };
    }
    return {
      msg: "",
      name: func.name,
      pos: firstNonEmptyLinePos,
    };
  }

  formatDocument() {
    const ast = this.getCore(Ast);
    return ast.getAstByActiveTextEditor();
  }

  getFirstNonEmptyLineBelowCursor(
    editor: vscode.TextEditor,
  ): vscode.Position | undefined {
    let currentLine = editor.selection.active.line;
    const document = editor.document;

    while (currentLine < document.lineCount) {
      const line = document.lineAt(currentLine);
      if (line.isEmptyOrWhitespace) {
        currentLine++;
      }
      else {
        return new vscode.Position(currentLine, 0);
      }
    }

    return undefined;
  }

  // #endregion

  // #region 通用能力

  /**
   * 在指定位置插入指定内容
   * @param document 文档
   * @param pos 指定位置
   * @param marker 内容片段
   */
  insertAnnotation(
    document: vscode.TextDocument,
    pos: vscode.Position,
    marker: string,
  ) {
    const edit = new vscode.WorkspaceEdit();
    edit.insert(document.uri, pos, marker);
    vscode.workspace.applyEdit(edit);
  }

  // 调整光标位置
  setSelection(pos: vscode.Position, edit?: vscode.TextEditor) {
    if (edit) {
      edit.selection = new vscode.Selection(pos, pos);
    }
  }

  // 前面添加一些空格, 保证注释和代码对齐
  addSpaces(str: string, n: number) {
    return " ".repeat(n) + str;
  }

  showInformationMessage(msg: string) {
    const s = msg.trim();
    if (s) {
      vscode.window.showInformationMessage(s);
    }
  }

  getAnnotationFlag(language: string) {
    return AnnotationMap[language] || "//";
  }

  generateAnnotationText(tag: "cb" | "ce", language: string) {
    return `${this.getAnnotationFlag(language)} ${tags[tag]}`;
  }

  // #endregion

  // #region 设置字符高亮
  activateColor() {
    const decorationType = vscode.window.createTextEditorDecorationType({
      color: "#417BFC", // 高亮显示的颜色
    });

    let activeEditor = vscode.window.activeTextEditor;

    const updateDecorations = (editor: vscode.TextEditor) => {
      if (!editor) return;

      const text = editor.document.getText();
      const regexPatterns = [
        /\/\/.*?(\[cb\].*)/g, // 匹配 // [cb]
        /\/\/.*?(\[ce\].*)/g, // 匹配 // [ce]
        /#.*?(\[cb\].*)/g, // 匹配 # [cb]
        /#.*?(\[ce\].*)/g, // 匹配 # [ce]
        /\*.*?(FUNC_DES.*)/g, // 匹配 * FUNC_DES
      ];

      const decorations: vscode.DecorationOptions[] = [];

      regexPatterns.forEach((regEx) => {
        let match;
        while ((match = regEx.exec(text))) {
          const startPos = editor.document.positionAt(
            match.index + match[0].indexOf(match[1]),
          );
          const endPos = editor.document.positionAt(
            match.index + match[0].indexOf(match[1]) + match[1].length,
          );

          const decoration = { range: new vscode.Range(startPos, endPos) };
          decorations.push(decoration);
        }
      });

      editor.setDecorations(decorationType, decorations);
    };

    const triggerUpdateDecorations = () => {
      if (activeEditor) {
        updateDecorations(activeEditor);
      }
    };

    if (activeEditor) {
      triggerUpdateDecorations();
    }

    vscode.window.onDidChangeActiveTextEditor(
      (editor) => {
        activeEditor = editor;
        if (editor) {
          triggerUpdateDecorations();
        }
      },
      null,
      this.context.subscriptions,
    );

    vscode.workspace.onDidChangeTextDocument(
      (event) => {
        if (activeEditor && event.document === activeEditor.document) {
          triggerUpdateDecorations();
        }
      },
      null,
      this.context.subscriptions,
    );
  }
  // #endregion

  // #region 服务相关
  sendExistingAnnotationRequest(repoName: string, filePath: string) {
    const queryParams = {
      repoName,
      filePath,
      pageSize: "100",
      pageNum: "1",
    };
    const queryString = new URLSearchParams(queryParams).toString();
    const url = `${this.getBase(ConfigManager).get(Config.PROXY_URL)}/eapi/kwaipilot/label/query/v2?${queryString}`;
    const option = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    };
    return fetch(url, option)
      .then(res => res.json())
      .then(res => res.data);
  }

  async findExistingAnnotation(document: vscode.TextDocument) {
    try {
      const { url } = await gitUtil.getProjectGitInfo();
      const relativePath = vscode.workspace.asRelativePath(document.fileName);
      const key = `${url}_${relativePath}`;
      let promise = cache.get(key);
      if (!promise) {
        promise = this.sendExistingAnnotationRequest(url, relativePath);
        cache.set(key, promise);
      }
      const { list } = await promise;
      return list;
    }
    catch (error) {
      return [];
    }
  }

  // #endregion
  private get logger() {
    return this.getBase(LoggerManager);
  }
}
