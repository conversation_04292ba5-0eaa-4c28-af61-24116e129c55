import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { ExtensionToLoaclShape } from "shared/lib/bridge/protocol";
import * as vscode from "vscode";
import { FromIdeProtocol } from "shared/lib/LocalService";
import { LocalService } from "../../core/localService";

export class ToLocalService extends ServiceModule implements ExtensionToLoaclShape {
  constructor(ext: ContextManager) {
    super(ext);
  }

  async $parseRules(rules: string[]): Promise<string[]> {
    const { data: parseRuls } = await this.$passThruToLocalService("rules/getRulesList", { rules });
    return parseRuls || [];
  }

  async $passThruToLocalService<T extends keyof FromIdeProtocol>(messageType: T, data: FromIdeProtocol[T][0]): Promise<FromIdeProtocol[T][1]> {
    try {
      // 使用request方法发送请求并等待响应
      const result = await this.localService.request(messageType, data);
      return result;
    }
    catch (error: any) {
      vscode.window.showErrorMessage(`透传消息到local-service失败: ${error.message || error}`);
      throw error;
    }
  }

  private get localService() {
    return this.ext.getCore(LocalService);
  }
}
