import { SerializedEditorState } from "lexical";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";
import { IndeterminatedWorkingSetEffectState } from "shared/lib/agent";
import { Uri } from "vscode";
import * as vscode from "vscode";

export interface NonPersistentSessionState {
  /**
   * 当前对话的 sessionId
   */
  sessionId: string;

  /**
   * 当前对话的 chatId
   */
  chatId: string;

  /**
   * UI 有一些额外的信息需要保存， 比如富文本编辑器的内容， 这些信息engine 不关注，因此需要在 engine 发来消息时延迟存储
   */
  currentConversationDelayedUnsavedState: {
    editorState: SerializedEditorState;
    questionForHumanReading: string;
    contextItems: MentionNodeV2Structure[];
  } | undefined;

  /**
   * 当前对话的 indeterminated 状态
   */
  indeterminatedWorkingSetEffects: IndeterminatedWorkingSetEffectState[];

  /**
   * 当前对话是否被中断
   */
  currentTaskInterrupted: boolean;

  /**
   * 当前对话是否连接中断
   */
  localServiceConnectionLost: boolean;

  /**
   * 待处理的索引状态信息，用于将索引状态与人类消息关联
   */
  pendingIndexedStatus?: {
    taskId: number;
    indexed: boolean;
  };

  diagnosticsWhenNewTaskCreated: [Uri, vscode.Diagnostic[]][];
}

export class SessionState {
  state: NonPersistentSessionState = {
    sessionId: "",
    chatId: "",
    currentConversationDelayedUnsavedState: undefined,
    indeterminatedWorkingSetEffects: [],
    currentTaskInterrupted: false,
    localServiceConnectionLost: false,
    diagnosticsWhenNewTaskCreated: [],
  };
}
