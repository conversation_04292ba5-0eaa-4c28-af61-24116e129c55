import { InternalLocalMessage, InternalLocalMessage_AI, InternalLocalMessage_Human, InternalLocalMessage_Tool_EditFile, isHumanMessage, LocalMessage, McpTextStructure, Say, SayTool, TerminalTextStructure } from "shared/lib/agent";
import { isMcpMessage, isTerminalMessage, isToolEditFileMessage } from "shared/lib/agent/isToolMessage";
import { ComposerSessionStorageService, isPersistedToolEditFileMessage } from "./ComposerSessionStorageService";
import { SessionState } from "./SessionState";
import { getNewDiagnostics } from "../../utils/diagnostics";
import * as vscode from "vscode";
import { DIFF_VIEW_URI_SCHEME } from "../../core/diff/diffViewProvider";
import { WebloggerManager } from "../../base/weblogger";
import { toSerializedDiagnostic } from "shared/lib/misc/diagnostic";
import { ConfigManager } from "../../base/state-manager";
import { Config } from "shared/lib/state-manager/types";
import * as path from "path";

/**
 * 从本地包发来的 LocalMessage 数据, 转换为 InternalLocalMessage
 */
export async function createLocalMessageMapper({
  context,
}: {
  context: {
    state: SessionState["state"];
    composerSessionStorageService: ComposerSessionStorageService;
    webloggerManager: WebloggerManager;
    configManager: ConfigManager;
    cwd: string;
  };
}): Promise<(value: LocalMessage, index: number, array: LocalMessage[]) => InternalLocalMessage> {
  const {
    state: {
      sessionId,
    },
    state: sessionState,
    webloggerManager,
    configManager,
    cwd,
  } = context;

  const storagedMessages = (await context.composerSessionStorageService.getComposerSessionData(sessionId))?.localMessages || [];

  const shouldYieldDiagnostics = ({
    latestMessage,
  }: {
    latestMessage: LocalMessage;
  }) => {
    if (latestMessage.chatId !== sessionState.chatId) {
      return false;
    }
    if (latestMessage.type === "say" && latestMessage.role === "user") {
      // humanMessage 还没开始流式过程
      return false;
    }
    const EndSayMessage: Say[] = ["completion_result", "error"];
    if (latestMessage.type === "ask" && latestMessage.ask) {
      return false;
    }
    if (latestMessage?.type === "say" && latestMessage.say && !EndSayMessage.includes(latestMessage.say)) {
      return false;
    }
    const isLastMessagePartial = latestMessage?.partial === true;
    if (isLastMessagePartial) {
      return false;
    }
    return true;
  };

  const toInternalLocalMessage = (localMessage: LocalMessage, _index: number, array: LocalMessage[]): InternalLocalMessage => {
  // 如果原数据已经存储了，则直接 copy
    const originalMessage = storagedMessages.find(m => m.ts === localMessage.ts);
    if (isHumanMessage(localMessage)) {
      if (originalMessage) {
        if (!isHumanMessage(originalMessage)) {
          throw new Error(`先后数据类型不一致 ${localMessage.ts}:${localMessage.type} - ${originalMessage?.ts}:${originalMessage?.type}`);
        }
        return originalMessage;
      }
      if (!sessionState.currentConversationDelayedUnsavedState) {
        throw new Error("currentConversationDelayedUnsavedState is not set");
      }

      // 将indexed状态添加到人类消息上
      const humanMessageWithIndexed: InternalLocalMessage_Human = {
        ...localMessage,
        role: "user" as const,
        editorState: sessionState.currentConversationDelayedUnsavedState.editorState,
        contextItems: sessionState.currentConversationDelayedUnsavedState.contextItems,
        diagnostics: [],
      };

      // 如果有待处理的索引状态，将其添加到消息中
      if (sessionState.pendingIndexedStatus) {
        (humanMessageWithIndexed as any).indexed = sessionState.pendingIndexedStatus.indexed;
        // 清除待处理状态，避免影响其他消息
        sessionState.pendingIndexedStatus = undefined;
      }

      if (humanMessageWithIndexed.chatId === sessionState.chatId && shouldYieldDiagnostics({ latestMessage: array.at(-1)! }) && configManager.get(Config.ENABLE_DIAGNOSTICS_CHECK)) {
        // 当前一轮的所有 editFile message
        const relatedFiles = array.filter(m => isToolEditFileMessage(m) && m.chatId === sessionState.chatId)
          .map(m => JSON.parse(m.text || "{}") as SayTool).map(i => path.resolve(cwd, i.path || ""));
        const newDiagnostics = getNewDiagnostics(
          sessionState.diagnosticsWhenNewTaskCreated,
          vscode.languages.getDiagnostics()
          // 只展示本轮对话修改的文件中的lint错误
            .filter(([uri, items]) => items.length > 0 && relatedFiles.includes(uri.fsPath))
            .filter(([uri]) => uri.scheme !== DIFF_VIEW_URI_SCHEME) // 过滤掉 diff view 的诊断 B2505123
          // 只展示 error 级别的 lint 问题
            .map(([uri, items]) => [uri, items.filter(detail => detail.severity === vscode.DiagnosticSeverity.Error)]),
        );
        humanMessageWithIndexed.diagnostics = newDiagnostics
          .map(([uri, items]) => [uri.toString(), items.map(item => toSerializedDiagnostic(item))]);
        if (humanMessageWithIndexed.diagnostics.length) {
          webloggerManager.$reportUserAction({
            key: "agent_linter_fix_show",
            type: undefined,
            sessionId: sessionState.sessionId,
            chatId: sessionState.chatId,
          });
        }
      }
      return humanMessageWithIndexed;
    }
    if (isToolEditFileMessage(localMessage)) {
      let modified: InternalLocalMessage_Tool_EditFile;
      const toolInfo = JSON.parse(localMessage.text || "{}") as SayTool;
      if (originalMessage) {
        if (!isPersistedToolEditFileMessage(originalMessage)) {
          throw new Error(`先后数据类型不一致 ${localMessage.ts}:${localMessage.type} - ${originalMessage?.ts}:${originalMessage?.type}`);
        }
        modified = {
          ...localMessage,
          role: undefined,
          workingSetEffect: localMessage.partial
            ? {
                status: "init",
                languageId: "",
                path: "",
                diffContent: undefined,
              }
            : {
                status: originalMessage.workingSetEffect.status || "init",
                languageId: originalMessage.workingSetEffect.languageId || toolInfo.language || "",
                path: originalMessage.workingSetEffect.path || toolInfo.path || "",
                diffContent: originalMessage.workingSetEffect.diffContent || undefined,
              },
        };
      }
      else {
        modified = {
          ...localMessage,
          role: undefined,
          // 如果消息还没输出完成，path 等信息可能未确定
          workingSetEffect: localMessage.partial
            ? {
                status: "init",
                languageId: "",
                path: "",
                diffContent: undefined,
              }
            : {
                status: "init",
                languageId: toolInfo.language || "",
                path: toolInfo.path || "",
                diffContent: undefined,
              },
        };
      }
      return modified;
    }
    if (isTerminalMessage(localMessage)) {
      type StrictInternalLocalMessage_Terminal = Omit<InternalLocalMessage_AI, "autoRun" | "autoRunFailedReason">
        & Required<Pick<InternalLocalMessage_AI, "autoRun" | "autoRunFailedReason">>;
      if (originalMessage && "autoRun" in originalMessage && "autoRunFailedReason" in originalMessage) {
        return originalMessage;
      }
      if (localMessage.partial) {
        // 如果是 partial 的 terminal 消息，autoRun 和 autoRunFailedReason 可能
        // 还未确定，先将其设置为 undefined
        return {
          ...localMessage,
          role: undefined,
          autoRun: undefined,
          autoRunFailedReason: undefined,
        };
      }
      const terminalInfo = JSON.parse(localMessage.text || "{}") as TerminalTextStructure;
      const autoRunInfo: Pick<StrictInternalLocalMessage_Terminal, "autoRun" | "autoRunFailedReason">
      = configManager.get(Config.COMPOSER_ENABLE_AUTO_RUN)
        ? terminalInfo.requires_approval
          ? {
              autoRun: false,
              autoRunFailedReason: "agentRequiredApproval",
            }
          : configManager.get(Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE).some(dangerousCmd => terminalInfo.command.startsWith(dangerousCmd))
            ? {
                autoRun: false,
                autoRunFailedReason: "excluded",
              }
            : {
                autoRun: true,
                autoRunFailedReason: "",
              }
        : {
            autoRun: false,
            autoRunFailedReason: "off",
          };
      const modified: StrictInternalLocalMessage_Terminal = {
        ...localMessage,
        role: undefined,
        ...autoRunInfo,
      };
      return modified;
    }
    if (isMcpMessage(localMessage)) {
      type StrictInternalLocalMessage_Mcp = Omit<InternalLocalMessage_AI, "autoRun" | "autoRunFailedReason">
        & Required<Pick<InternalLocalMessage_AI, "autoRun" | "autoRunFailedReason">>;
      if (originalMessage && "autoRun" in originalMessage && "autoRunFailedReason" in originalMessage) {
        return originalMessage;
      }
      if (localMessage.partial) {
        // 如果是 partial 的 mcp 消息，autoRun 和 autoRunFailedReason 可能
        // 还未确定，先将其设置为 undefined
        return {
          ...localMessage,
          role: undefined,
          autoRun: undefined,
          autoRunFailedReason: undefined,
        };
      }
      const mcpInfo = JSON.parse(localMessage.text || "{}") as McpTextStructure;
      const autoRunInfo: Pick<StrictInternalLocalMessage_Mcp, "autoRun" | "autoRunFailedReason">
      = configManager.get(Config.COMPOSER_ENABLE_AUTO_RUN) && configManager.get(Config.COMPOSER_ENABLE_AUTO_RUN_MCP)
        ? mcpInfo.requires_approval
          ? {
              autoRun: false,
              autoRunFailedReason: "agentRequiredApproval",
            }
          : {
              autoRun: true,
              autoRunFailedReason: "",
            }
        : {
            autoRun: false,
            autoRunFailedReason: "off",
          };
      const modified: StrictInternalLocalMessage_Mcp = {
        ...localMessage,
        role: undefined,
        ...autoRunInfo,
      };
      return modified;
    }
    return {
      ...localMessage,
      role: undefined,
    };
  };
  return toInternalLocalMessage;
}
