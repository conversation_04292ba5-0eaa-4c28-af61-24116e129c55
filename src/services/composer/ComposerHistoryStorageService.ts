import { produce } from "immer";
import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { SqlLite } from "../sql-lite";
import AsyncLock from "async-lock";
import { PersistedComposerHistoryItem, PersistedComposerHistory } from "shared/lib/agent/storage";
import { MemoryCachedKwaipilotKV } from "../sql-lite/kwaipilotKVCache";
import { LoggerManager } from "../../base/logger";

const LOCK_KEY_UPDATE = "composerHistoryStorageService:update:common";

export class ComposerHistoryStorageService extends ServiceModule {
  lock = new AsyncLock();
  kwaipilotKV: MemoryCachedKwaipilotKV;
  constructor(ext: ContextManager) {
    super(ext);
    const sqlLiteService = this.getService(SqlLite);
    this.kwaipilotKV = new MemoryCachedKwaipilotKV(sqlLiteService, this.getBase(LoggerManager));
  }

  /**
   * 获取所有记录
   */
  async getComposerHistory(): Promise<PersistedComposerHistory> {
    const storageKey = this.composerHistoryStorageKey();
    return this.kwaipilotKV.getObject<PersistedComposerHistory>(storageKey, []);
  }

  async getComposerHistoryItem(sessionId: string) {
    const history = await this.getComposerHistory();
    return history.find(item => item.sessionId === sessionId);
  }

  async patchUpdateById(sessionId: string, data: Partial<Omit<PersistedComposerHistoryItem, "createdAt" | "lastUpdatedAt">>) {
    await this.lock.acquire(LOCK_KEY_UPDATE, async () => {
      const storageKey = this.composerHistoryStorageKey();
      const raw = await this.getComposerHistory();
      const modified = produce(raw, (draft) => {
        const targetIndex = draft.findIndex(item => item.sessionId === sessionId);
        if (targetIndex !== -1) {
          draft[targetIndex] = {
            ...draft[targetIndex],
            ...data,
            lastUpdatedAt: Date.now(),
          };
        }
      });
      this.kwaipilotKV.set(storageKey, modified);
    });
  }

  /**
   * 更新或插入一条记录
   * @param sessionId
   * @param data
   */
  async updateById(sessionId: string, data: Omit<PersistedComposerHistoryItem, "createdAt" | "lastUpdatedAt">) {
    await this.lock.acquire(LOCK_KEY_UPDATE, async () => {
      const storageKey = this.composerHistoryStorageKey();
      const raw = await this.getComposerHistory();
      const modified = produce(raw, (draft) => {
        const targetIndex = draft.findIndex(item => item.sessionId === sessionId);
        if (targetIndex !== -1) {
          draft[targetIndex] = {
            ...data,
            createdAt: draft[targetIndex].createdAt,
            lastUpdatedAt: Date.now(),
          };
        }
        else {
          draft.push({
            ...data,
            createdAt: Date.now(),
            lastUpdatedAt: Date.now(),
          });
        }
      });
      this.kwaipilotKV.set(storageKey, modified);
    });
  }

  async updateLastUpdatedAt(sessionId: string, lastUpdatedAt?: number) {
    await this.lock.acquire(LOCK_KEY_UPDATE, async () => {
      const storageKey = this.composerHistoryStorageKey();
      const raw = await this.getComposerHistory();
      const targetIndex = raw.findIndex(item => item.sessionId === sessionId);
      if (targetIndex === -1) {
        throw new Error(`sessionId ${sessionId} not found in composer history`);
      }
      const modified = produce(raw, (draft) => {
        draft[targetIndex].lastUpdatedAt = lastUpdatedAt || Date.now();
      });
      this.kwaipilotKV.set(storageKey, modified);
    });
  }

  async clear() {
    const storageKey = this.composerHistoryStorageKey();
    await this.kwaipilotKV.delete(storageKey);
  }

  async deleteHistoryItem(sessionId: string) {
    await this.lock.acquire(LOCK_KEY_UPDATE, async () => {
      const storageKey = this.composerHistoryStorageKey();
      const raw = await this.getComposerHistory();
      const modified = raw.filter(item => item.sessionId !== sessionId);
      this.kwaipilotKV.set(storageKey, modified);
    });
  }

  private composerHistoryStorageKey() {
    return "composerHistory";
  }
}
