import { ExtensionMCPShape } from "shared/lib/bridge/protocol";
import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import { LocalService } from "../../core/localService";
import { FromIdeProtocol } from "shared/lib/LocalService";
import { GlobalStateManager } from "../../base/state-manager";
import { GlobalState } from "shared/lib/state-manager/types";
import { DeleteMcpServerParams, FetchMcpDetailByMarketParams, InstallMcpParams, McpServerChangeEventDetail, RestartMcpServerParams, ToggleMcpServerParams } from "shared/lib/mcp/types";

export class MCPService extends ServiceModule implements ExtensionMCPShape {
  mcpServers = this.globalState.get(GlobalState.MCP_SERVERS, {
    code: 0,
    isError: false,
    message: "",
    mcpServers: [],
  });

  constructor(ext: ContextManager) {
    super(ext);
    this.$getAllMcpServers();
    this.registryLocalServiceListener();
    this.registerMcpUriHandler();
  }

  private get localService() {
    return this.ext.getCore(LocalService);
  }

  private registerMcpUriHandler() {
    vscode.window.registerUriHandler({
      handleUri(uri) {
        if (uri.path === "/mcp-management") {
          vscode.commands.executeCommand("kwaipilot.openMCPManagement", uri.query);
        }
      },
    });
  }

  private registryLocalServiceListener() {
    this.localService.onMessage("mcp/mcpServerChange", ({ data }) => {
      this.updateMcpServers(data);
      return undefined;
    });
  }

  async $passThruToLocalService<T extends keyof FromIdeProtocol>(messageType: T, data: FromIdeProtocol[T][0]): Promise<FromIdeProtocol[T][1]> {
    try {
      // 使用request方法发送请求并等待响应
      const result = await this.localService.request(messageType, data);
      return result;
    }
    catch (error: any) {
      vscode.window.showErrorMessage(`透传消息到local-service失败: ${error.message || error}`);
      throw error;
    }
  }

  async updateMcpServers(data: McpServerChangeEventDetail) {
    this.mcpServers = data;
    await this.globalState.update(GlobalState.MCP_SERVERS, this.mcpServers);
  }

  async $getSettingsPath() {
    return await this.$passThruToLocalService("mcp/getSettingsPath", undefined);
  }

  async $getAllMcpServers() {
    return await this.$passThruToLocalService("mcp/getAllMcpServers", undefined);
  }

  async $toggleMcpServer(params: ToggleMcpServerParams) {
    return await this.$passThruToLocalService("mcp/toggleMcpServer", params);
  }

  async $restartMcpServer(params: RestartMcpServerParams) {
    return await this.$passThruToLocalService("mcp/restartMcpServer", params);
  }

  async $deleteMcpServer(params: DeleteMcpServerParams) {
    return await this.$passThruToLocalService("mcp/deleteMcpServer", params);
  }

  async $installMcpServer(params: InstallMcpParams) {
    return await this.$passThruToLocalService("mcp/installMcp", params);
  }

  async $fetchMcpDetailByMarket(params: FetchMcpDetailByMarketParams) {
    return await this.$passThruToLocalService("mcp/fetchMcpDetailByMarket", params);
  }

  async $getMcpFeaturedServers() {
    return await this.$passThruToLocalService("mcp/fetchAvailableMcpListByMarket", undefined);
  }

  private get globalState() {
    return this.getBase(GlobalStateManager);
  }

  $getMcpServers() {
    return this.mcpServers;
  }
}
