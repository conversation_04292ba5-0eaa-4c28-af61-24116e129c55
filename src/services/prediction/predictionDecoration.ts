import * as vscode from "vscode";

import { Prediction } from "../../base/http-client/interface";
import { createDeferred, Deferred } from "../../utils/deferred";
import {
  newContentAfterDecorationType,
  selectContentDecorationType,
  tabTipDecorationType,
} from "./decorations";
import {
  BasicCompletionRequestParam,
  buildCodeCompletionLogParam,
  CodeCompletionLogParam,
} from "../../log/Model";
import { codeCompletionLog } from "../../log/LogService";
import { LoggerManager } from "../../base/logger";
import { Bridge } from "@bridge";
import { BlacklistManager } from "./blacklistManager";
import { editorConfigs } from "../../utils/getEditorConfig";

export class PredictionDecoration {
  private disposableEscape: vscode.Disposable | null = null;
  private disposableTab: vscode.Disposable | null = null;
  private disposableClick: vscode.Disposable | null = null;
  private readonly logger;
  private readonly loggerScope = "prediction";

  private editor: vscode.TextEditor | undefined;
  private blocks: Prediction[] = [];
  private sortCursorOffsets: number[] = [];
  private currentPrediction: Prediction | undefined;
  private _isRunning: boolean = false;
  private deferred: Deferred | undefined;
  private tabTipDeferred: Deferred | undefined;
  private awaitEnd: boolean = false;
  private cursorOffset: number = 0;
  private tabTipLine?: number;
  private tempDecorationTypes: vscode.TextEditorDecorationType[] = [];
  private logParam: CodeCompletionLogParam | undefined;
  private showTimeStamp: number = 0;
  private autoBlacklistTimeout: NodeJS.Timeout | undefined;
  private readonly AUTO_BLACKLIST_DELAY = 1000;

  constructor(
    logger: LoggerManager,
    private bridge: Bridge,
    private blacklistManager?: BlacklistManager,
  ) {
    this.logger = logger;
    this.bridge = bridge;
  }

  async run(
    editor: vscode.TextEditor,
    context: vscode.ExtensionContext,
    blocks: Prediction[],
    sortCursorOffsets: number[],
  ) {
    this.dispose();
    this.deferred = createDeferred();
    this.editor = editor;
    this.blocks = blocks;
    this.sortCursorOffsets = sortCursorOffsets;
    this.awaitEnd = false;
    this._isRunning = true;
    this.registerPredictionActions(context);
    this.recordCursorOffset();

    vscode.commands.executeCommand(
      "setContext",
      "kwaipilot.prediction.isVisible",
      true,
    );
    this.init();
    await this.deferred?.promise;
  }

  private recordCursorOffset() {
    const cursorPosition = this.editor!.selection.active;
    this.cursorOffset = this.editor!.document.offsetAt(cursorPosition);
  }

  private init() {
    this.clearDecoration();
    this.renderPredictionDecoration(this.getNextPrediction()!);
    // 设置自动加黑名单的定时器
    this.setupAutoBlacklist();
  }

  private setupAutoBlacklist() {
    // 清除之前的定时器
    if (this.autoBlacklistTimeout) {
      clearTimeout(this.autoBlacklistTimeout);
    }

    // 设置新的定时器
    this.autoBlacklistTimeout = setTimeout(() => {
      if (this.currentPrediction && this.blacklistManager && this._isRunning) {
        this.blacklistManager.add(this.currentPrediction);
        this.logger.info("auto add to blacklist", this.loggerScope, {
          value: this.currentPrediction,
        });
      }
    }, this.AUTO_BLACKLIST_DELAY);
  }

  private clearAutoBlacklist() {
    if (this.autoBlacklistTimeout) {
      clearTimeout(this.autoBlacklistTimeout);
      this.autoBlacklistTimeout = undefined;
    }
  }

  private handleEscape = async () => {
    if (!this.editor) return;
    if (this.tabTipDeferred) {
      this.resolveTabTip();
      return;
    }
    this.clearDecoration();
    this.clearAutoBlacklist();

    // 拒绝时加入黑名单
    if (this.currentPrediction && this.blacklistManager) {
      this.blacklistManager.add(this.currentPrediction);
    }

    this.dispose();
  };

  private handleTab = async () => {
    if (!this.editor) return;
    if (this.tabTipDeferred) {
      // 查看是否需要跳转到目标行
      if (this.tabTipLine !== undefined) {
        // 使用 revealRange 将行滚动到视图中
        this.editor.revealRange(
          new vscode.Range(this.tabTipLine, 0, this.tabTipLine, 0),
          vscode.TextEditorRevealType.InCenter, // 将行显示在视图中央
        );
        this.tabTipLine = undefined;
      }
      this.resolveTabTip();
      return;
    }
    this.clearAutoBlacklist();
    if (this.awaitEnd) {
      this.editor.revealRange(
        new vscode.Range(
          this.editor.document.positionAt(this.cursorOffset),
          this.editor.document.positionAt(this.cursorOffset),
        ),
        vscode.TextEditorRevealType.InCenter, // 将行显示在视图中央
      );
      this.editor.selection = new vscode.Selection(
        this.editor.document.positionAt(this.cursorOffset),
        this.editor.document.positionAt(this.cursorOffset),
      );
      this.dispose();
      return;
    }
    const {
      sourceStartOffset,
      sourceEndOffset,
      targetBlockContent,
      sourceBlockContent,
    } = this.currentPrediction!;

    this.clearDecoration();
    // 接受上报
    this.reportLog({
      requestId: this.currentPrediction?.diffBlockId ?? "unknown",
      accept: true,
    });

    this._isRunning = true;
    // 应用预测代码
    this.editor.edit((editBuilder) => {
      editBuilder.replace(
        new vscode.Range(
          this.editor!.document.positionAt(sourceStartOffset),
          this.editor!.document.positionAt(sourceEndOffset),
        ),
        targetBlockContent,
      );
    });
    // 只有当要更新的代码 endOffset在当前鼠标前面时，才需要在采纳后更新鼠标的位置
    if (this.currentPrediction && this.currentPrediction.sourceEndOffset < this.cursorOffset) {
      this.cursorOffset
        = this.cursorOffset
        + targetBlockContent.length
        - sourceBlockContent.length;
      const newCursorPosition = this.editor.document.positionAt(
        sourceStartOffset + targetBlockContent.length,
      );
      this.editor.selection = new vscode.Selection(
        newCursorPosition,
        newCursorPosition,
      );
    }

    // 找到下一个变更位置
    const nextPrediction = this.getNextPrediction();

    if (nextPrediction) {
      this.renderPredictionDecoration(nextPrediction);
    }
    else {
      this.awaitEnd = true;
      this.renderTabTipDecoration(this.cursorOffset);
    }
  };

  private registerPredictionActions(context: vscode.ExtensionContext) {
    this.disposableEscape = vscode.commands.registerCommand(
      "kwaipilot.handleEscape",
      this.handleEscape.bind(this),
    );
    this.disposableTab = vscode.commands.registerCommand(
      "kwaipilot.handleTab",
      this.handleTab.bind(this),
    );

    // 添加鼠标点击事件监听
    this.disposableClick = vscode.window.onDidChangeTextEditorSelection((e) => {
      if (!this.editor || !this._isRunning) return;

      // 只处理鼠标点击事件
      if (e.kind === vscode.TextEditorSelectionChangeKind.Mouse) {
        const currentPosition = this.editor.selection.active;
        const currentOffset = this.editor.document.offsetAt(currentPosition);

        // 如果位置发生改变，触发handleEscape
        if (currentOffset !== this.cursorOffset) {
          this.handleEscape();
        }
      }
    });

    context.subscriptions.push(
      this.disposableEscape,
      this.disposableTab,
      this.disposableClick,
    );
  }

  private getNextPrediction() {
    this.currentPrediction = this.blocks.find((prediction) => {
      const { sourceStartOffset } = prediction;
      return sourceStartOffset === this.sortCursorOffsets.shift(); // 找到下一个变更
    });
    return this.currentPrediction;
  }

  private async getOldLineDecoration(
    startLine: number,
    endLine: number,
    newContentEndLine: number,
    maxLineLength: number,
  ) {
    if (!this.editor || !this.currentPrediction) return [];

    // 应用装饰
    const decorations: vscode.DecorationOptions[] = [];
    const newEndLine = Math.max(endLine, newContentEndLine);

    // 一次性应用所有更改
    for (let i = startLine; i < newEndLine; i++) {
      // await this.replaceLineContent(i, newLines[j]);
      decorations.push({
        range: new vscode.Range(i, 0, i, maxLineLength),
      });
    }

    return decorations;
  }

  private async renderPredictionDecoration(prediction: Prediction) {
    if (!this.editor) return;

    this.showTimeStamp = Date.now();

    this.tabTipDeferred = createDeferred();
    this.renderTabTipDecoration(prediction.sourceStartOffset);
    await this.tabTipDeferred?.promise;
    const {
      sourceStartOffset,
      sourceBlockContent,
      sourceEndOffset,
      targetBlockContent,
    } = prediction;
    const startLine = this.editor.document.positionAt(sourceStartOffset).line;
    const endLine = this.editor.document.positionAt(sourceEndOffset).line;

    // 计算旧内容的最大长度
    let oldContentEnd = 0;
    for (let i = startLine; i <= endLine; i++) {
      const lineText = this.editor.document.lineAt(i).text;
      oldContentEnd = Math.max(oldContentEnd, lineText.length);
    }

    // 应用旧内容装饰
    const decoration = {
      range: new vscode.Range(
        this.editor.document.positionAt(sourceStartOffset),
        this.editor.document.positionAt(sourceEndOffset),
      ),
    };

    this.editor.setDecorations(selectContentDecorationType, [decoration]);
    this.tempDecorationTypes.push(selectContentDecorationType);
    let maxCommonBeforeBlackCount = Infinity;
    sourceBlockContent.split("\n").forEach((line, _index) => {
      const blackCount = line.length - line.trimStart().length;
      maxCommonBeforeBlackCount = Math.min(
        maxCommonBeforeBlackCount,
        blackCount,
      );
    });
    targetBlockContent.split("\n").forEach((line, _index) => {
      const blackCount = line.length - line.trimStart().length;
      maxCommonBeforeBlackCount = Math.min(
        maxCommonBeforeBlackCount,
        blackCount,
      );
    });
    const { dataUrl: b, backgroundColor } = await this.bridge.getPredictionImage({
      sourceBlockContent: sourceBlockContent
        .split("\n")
        .map(line => line.slice(maxCommonBeforeBlackCount))
        .join("\n"),
      targetBlockContent: targetBlockContent
        .split("\n")
        .map(line => line.slice(maxCommonBeforeBlackCount))
        .join("\n"),
      languageId: this.editor.document.languageId,
      editorConfig: editorConfigs,
    });
    if (!this._isRunning) {
      return;
    };
    const isLightTheme = vscode.window.activeColorTheme.kind === 1 || vscode.window.activeColorTheme.kind === 4;
    const backUpColor = isLightTheme ? "rgb(255, 255, 255)" : "rgb(30, 30, 30)";
    const borderColor = isLightTheme ? "#E7EEF7" : "#38444D";

    this.editor.setDecorations(newContentAfterDecorationType, [
      {
        range: new vscode.Range(
          startLine,
          oldContentEnd,
          startLine,
          oldContentEnd,
        ),
        renderOptions: {
          after: {
            contentIconPath: vscode.Uri.parse(b),
            margin: "0 0 0 3em",
            textDecoration: `
                font-style: normal;
                font-weight: normal;
                text-decoration: none;
                margin-left: 28ch;
                position: absolute;
                scale: 0.530;
                opacity: 1;
                border: 1px solid ${borderColor};
                border-radius: 8px;
                cursor: default;
                z-index: 10000;
                transform-origin: 0px 0px;
                background-color: ${backgroundColor || backUpColor};
                overflow: hidden;
              `,
          },
        },
      },
    ]);
    this.tempDecorationTypes.push(newContentAfterDecorationType);
  }

  private clearDecoration() {
    if (!this.editor) return;
    this.tempDecorationTypes.forEach((d) => {
      this.editor!.setDecorations(d, []);
    });
  }

  private renderTabTipDecoration(targetOffset: number) {
    if (!this.editor) {
      return;
    }
    const currentLine = this.editor.selection.active.line;
    const currentColumn = this.editor.document.lineAt(currentLine).text.length;
    const targetLine = this.editor.document.positionAt(targetOffset).line;
    const targetColumn = this.editor.document.lineAt(targetLine).text.length;

    if (currentLine === targetLine) {
      this.resolveTabTip();
      return;
    }

    const isVisible = this.isLineVisible(targetLine);
    if (isVisible) {
      // NOTE: 这里添加是否处于 awaitEnd阶段，从而展示回到初始编辑位置的行为
      this.resolveTabTip();
      return;
    }
    if (!isVisible) {
      this.tabTipLine = targetLine;
    }

    this.editor.setDecorations(tabTipDecorationType, [
      {
        range: new vscode.Range(
          isVisible ? targetLine : currentLine,
          isVisible ? targetColumn : currentColumn,
          isVisible ? targetLine : currentLine,
          isVisible ? targetColumn : currentColumn,
        ),
        renderOptions: {
          after: {
            contentText: `\u00A0按 Tab 跳转至预测编辑行 ${targetLine > currentLine ? "⬇" : "⬆"
            }\u00A0`,
          },
        },
      },
    ]);
    this.tempDecorationTypes.push(tabTipDecorationType);
  }

  private isLineVisible(line: number): boolean {
    const visibleRanges = this.editor?.visibleRanges ?? [];
    const targetRange = new vscode.Range(line, 0, line, 0);
    return visibleRanges.some(range => range.contains(targetRange));
  }

  private resolveTabTip = () => {
    this.tabTipDeferred?.resolve();
    this.editor?.setDecorations(tabTipDecorationType, []);
    this.tabTipDeferred = undefined;
  };

  dispose() {
    if (this.currentPrediction) {
      this.reportLog({
        requestId: this.currentPrediction.diffBlockId,
        accept: false,
      });
      this.currentPrediction = undefined;
    }
    this.clearAutoBlacklist();
    this.disposableEscape?.dispose();
    this.disposableTab?.dispose();
    this.disposableClick?.dispose();
    this._isRunning = false;
    this.deferred?.resolve();
    this.clearDecoration();
    vscode.commands.executeCommand(
      "setContext",
      "kwaipilot.prediction.isVisible",
      false,
    );
  }

  /**
   * 埋点上报
   */
  private reportLog(newParam: { requestId: string; accept: boolean }) {
    if (!this.logParam) {
      return;
    }
    const { requestId, accept } = newParam;
    if (accept) {
      this.logParam.applied = true;
      this.logParam.applyTimestamp = Date.now();
      this.logParam.rejected = false;
      this.logParam.rejectTimestamp = 0;
    }
    else {
      this.logParam.rejected = true;
      this.logParam.rejectTimestamp = Date.now();
      this.logParam.applied = false;
      this.logParam.applyTimestamp = 0;
    }
    this.logParam.requestId = requestId;
    this.logParam.completionCode = this.currentPrediction?.targetBlockContent;
    this.logParam.shown = true;
    this.logParam.showStartTimestamp = this.showTimeStamp;
    this.logParam.flag = "CodePrediction";

    this.logger.info(`report code prediction: ${accept ? "accept" : "reject"} ${requestId}`, this.loggerScope, {
      value: this.logParam,
    });
    codeCompletionLog(this.logParam);
  }

  initLogParam(param: {
    deviceId: string;
    fileName: string;
    languageId: string;
    textBeforeCursor: string;
    textAfterCursor: string;
    completionLogprod: number;
    username: string;
    startHandleTimestamp: number;
    requestTimestamp: number;
  }) {
    this.logParam = buildCodeCompletionLogParam(
      this.buildBasicPredictionParam(param),
    );
  }

  /** 补代码生成参数 */
  private buildBasicPredictionParam(param: {
    deviceId: string;
    fileName: string;
    languageId: string;
    textBeforeCursor: string;
    textAfterCursor: string;
    completionLogprod: number;
    username: string;
    startHandleTimestamp: number;
    requestTimestamp: number;
  }): BasicCompletionRequestParam {
    const {
      deviceId,
      fileName,
      languageId,
      textAfterCursor,
      textBeforeCursor,
      completionLogprod,
      username,
      startHandleTimestamp,
      requestTimestamp,
    } = param;
    const codeParam = new BasicCompletionRequestParam();
    codeParam.deviceId = deviceId;
    codeParam.fileName = fileName;
    codeParam.languageId = languageId;

    codeParam.codeBeforeCursor = textBeforeCursor;
    codeParam.codeAfterCursor = textAfterCursor;
    codeParam.completionCode = this.currentPrediction?.targetBlockContent;
    codeParam.completionLogprod = completionLogprod;
    codeParam.username = username;
    codeParam.requestTimestamp = requestTimestamp;
    codeParam.startHandleTimestamp = startHandleTimestamp;
    return codeParam;
  }

  get isRunning() {
    return this._isRunning;
  }
}
