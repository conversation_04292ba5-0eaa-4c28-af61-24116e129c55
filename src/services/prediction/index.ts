import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { Api } from "../../base/http-client";
import { WebloggerManager } from "../../base/weblogger";
import * as vscode from "vscode";
import { v4 as uuidv4 } from "uuid";
import { PredictionDecoration } from "./predictionDecoration";
import { HistoryManager } from "./historyManager";
import { ConfigManager, GlobalStateManager } from "../../base/state-manager";
import { Config, GlobalState } from "../../base/state-manager/types";
import { CompletionModule } from "../../core/completion";
import { LoggerManager } from "../../base/logger";
import { Bridge } from "@bridge";
import { BlacklistManager } from "./blacklistManager";
import { watchConfigChanges } from "../../utils/getEditorConfig";

export class PredictionService extends ServiceModule {
  private loggerScope = "prediction-service";
  private historyManager: HistoryManager;
  private blacklistManager: BlacklistManager;
  private abortController: AbortController | null = null;
  private predictionDecoration: PredictionDecoration;
  private editValidIntervalMs: number = 200;
  // 上次保存的时间为0，确保第一次运行保存
  private lastSaveTime: number = 0;
  private lastPredictTime: number = 0;

  constructor(ext: ContextManager) {
    super(ext);
    this.historyManager = new HistoryManager();
    this.blacklistManager = new BlacklistManager();
    watchConfigChanges(this.context);
    this.api.getPlatformConfig().then((data) => {
      if (!data?.codePredictionConfig) {
        return;
      }
      const { editHistorySize, editValidIntervalMs, blacklistMaxSize, blacklistExpireMs } = data.codePredictionConfig;
      this.editValidIntervalMs = editValidIntervalMs;
      this.historyManager.updateHistoryStep(editHistorySize);
      if (blacklistMaxSize && blacklistExpireMs) {
        this.blacklistManager.updateConfig(blacklistMaxSize, blacklistExpireMs);
      }
    });
    this.globState.update(GlobalState.ON_PREDICTION, false);
    this.predictionDecoration = new PredictionDecoration(this.logger, this.bridge, this.blacklistManager);
    this.registerListener();
  }

  private registerListener() {
    // 监听鼠标变动事件
    vscode.window.onDidChangeTextEditorSelection(async (e) => {
      if (Date.now() - this.lastSaveTime < this.editValidIntervalMs) {
        /** 更新频率太高，不处理 */
        return;
      }
      this.lastSaveTime = Date.now();
      if (e.kind !== 2 && e.textEditor.document.uri.scheme !== "output" && e.textEditor.document.uri.path !== "untitled") {
        this.historyManager.addHistory(e.textEditor.document.fileName, e.textEditor.document.getText());
      }
    });

    // 监听文件切换事件
    vscode.window.onDidChangeActiveTextEditor((e) => {
      if (!e?.document.uri.path) {
        return;
      }
      if (e.document.uri.path !== this.blacklistManager.filename) {
        // 清空黑名单
        this.blacklistManager.clear();
        this.blacklistManager.filename = e.document.uri.path;
        return;
      }
    });

    this.getCore(CompletionModule).on("code-prediction", () => {
      if (Date.now() - this.lastPredictTime < 75) {
        /** 更新频率太高，不处理 */
        return;
      }
      this.lastPredictTime = Date.now();
      this.showPredictedCode();
    });
    this.getCore(CompletionModule).on("dispose-prediction", () => {
      if (this.abortController) {
        this.abortController.abort();
      }
      this.predictionDecoration.dispose();
    });
  }

  private async getPredictCode(
    cursorOffset: number,
    history: string[],
    language: string,
    id: string,
  ) {
    if (this.abortController) {
      this.abortController.abort();
    }
    this.abortController = new AbortController();
    const data = await this.api.getPrediction(
      cursorOffset,
      history,
      language,
      id,
      this.abortController.signal,
    );

    return data?.diffBlocks;
  }

  private getPredictionParams(editor: vscode.TextEditor) {
    const cursorPosition = editor.selection.active;
    const cursorOffset = editor.document.offsetAt(cursorPosition);
    const id = uuidv4();
    const history = this.historyManager.getTargetHistory(editor.document.uri.path);
    const language = editor.document.languageId;
    const content = editor.document.getText();
    return { cursorOffset, id, history, language, content };
  }

  private async showPredictedCode() {
    if (!this.config.get(Config.PREDICTION_ENABLE) || this.predictionDecoration.isRunning) {
      return;
    }

    const editor = vscode.window.activeTextEditor;
    if (!editor) return;
    const requestTimestamp = Date.now();
    const { cursorOffset, id, history, language, content } = this.getPredictionParams(editor);

    const predictions = await this.getPredictCode(
      cursorOffset,
      [history, content],
      language,
      id,
    );
    if (!predictions) {
      // 需要在请求没数据时，销毁装饰器
      this.logger.info(`no prediction data: ${id}`, this.loggerScope, {
        value: {
          cursorOffset,
          history,
          language,
        },
      });
      this.predictionDecoration.dispose();
      return;
    };

    // 过滤掉黑名单中的预测
    const filteredPredictions = predictions.filter(prediction => !this.blacklistManager.isBlacklisted(prediction));

    if (filteredPredictions.length === 0) {
      this.logger.info(`filteredPredictions is empty: ${id}`, this.loggerScope, {
        value: {
          cursorOffset,
          history,
          language,
        },
      });
      this.predictionDecoration.dispose();
      return;
    }

    if (!this.checkSourceContent(editor, filteredPredictions[0].sourceStartOffset, filteredPredictions[0].sourceEndOffset, filteredPredictions[0].sourceBlockContent)) {
      // 校验 offset 圈出来的内容是否和当前内容一致
      this.logger.info(`checkSourceContent is false: ${id}`, this.loggerScope, {
        value: {
          cursorOffset,
          history,
          language,
        },
      });
      return;
    }
    // 对多个 prediction 进行排序
    const predictionCursorOffsets = this.customSort(
      filteredPredictions.map(p => p.sourceStartOffset),
      cursorOffset,
    );
    /**
     * 初始化曝光信息
     */
    const startHandleTimestamp = Date.now();
    this.predictionDecoration.initLogParam({
      deviceId: this.deviceId as string,
      fileName: editor.document.fileName,
      languageId: language,
      textBeforeCursor: history,
      textAfterCursor: content,
      completionLogprod: 0.0,
      username: this.globState.get(GlobalState.USER_INFO)?.name ?? "",
      startHandleTimestamp,
      requestTimestamp,
    });
    await this.predictionDecoration.run(editor, this.context, filteredPredictions, predictionCursorOffsets);
  }

  private customSort(arr: number[], benchmark: number) {
    const equal: number[] = [];
    const smaller: number[] = [];
    const larger: number[] = [];

    for (const num of arr) {
      if (num === benchmark) {
        equal.push(num);
      }
      else if (num < benchmark) {
        smaller.push(num);
      }
      else {
        larger.push(num);
      }
    }

    smaller.sort((a, b) => a - b);
    larger.sort((a, b) => a - b);

    return [...equal, ...smaller, ...larger];
  }

  private checkSourceContent(editor: vscode.TextEditor, startOffset: number, endOffset: number, sourceContentFromAI: string) {
    const sourceContent = editor.document.getText().slice(startOffset, endOffset);
    if (sourceContentFromAI === sourceContent) {
      return true;
    }
    return false;
  }

  private get weblogger() {
    return this.getBase(WebloggerManager);
  }

  private get api() {
    return this.getBase(Api);
  }

  private get config() {
    return this.getBase(ConfigManager);
  }

  private get globState() {
    return this.getBase(GlobalStateManager);
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get deviceId() {
    return this.globState.get(GlobalState.DEVICE_ID);
  }

  private get bridge() {
    return this.getBase(Bridge);
  }
}
