import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import { ConfigManager, GlobalStateManager } from "../../base/state-manager";
import { Config, GlobalState } from "../../base/state-manager/types";
import { LoggerManager } from "../../base/logger";
import { UploadFile, upload } from "../../api/upload";
import { LOG_PATH } from "../../common/const";
import { SettingPanelModule } from "../../core/setting-panel";
import { DefaultBaseUrl } from "../../const";

export class BrowserService extends ServiceModule {
  constructor(ext: ContextManager) {
    super(ext);
    this.registryListeners();
  }

  private registryListeners() {
    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.home", () => {
        this.logger.info("kwaipilot.home", "browser");
        this.openHome();
      }),
      vscode.commands.registerCommand("kwaipilot.feedback", () => {
        this.logger.info("kwaipilot.feedback", "browser");
        this.openFeedback();
      }),
      vscode.commands.registerCommand("kwaipilot.help", () => {
        this.logger.info("kwaipilot.help", "browser");
        this.openHelp();
      }),
      vscode.commands.registerCommand("kwaipilot.settings", () => {
        this.logger.info("kwaipilot.openBasicsManagement", "browser");
        vscode.commands.executeCommand(
          "kwaipilot.openBasicsManagement",
        );
      }),
    );
  }

  openHome() {
    const homeUrl = "https://kwaipilot.corp.kuaishou.com/chat";
    vscode.env.openExternal(vscode.Uri.parse(homeUrl));
  }

  openFeedback() {
    const logFiles = [
      vscode.Uri.file(`${LOG_PATH}/vscode/kwaipilot.log`),
      vscode.Uri.file(`${LOG_PATH}/core.log`),
      vscode.Uri.file(`${LOG_PATH}/error.log`),
    ];

    const uploadedFiles: UploadFile[] = [];
    let uploadCount = 0;

    const gotoFeedback = () => {
      const { name } = this.globalState.get(GlobalState.USER_INFO) ?? { name: "unauthenticated" };
      const query = new URLSearchParams();
      query.append("name", name);

      // 将所有日志文件的URL用 "|" 连接
      const logUrls = uploadedFiles
        .map(file => file.url)
        .filter(url => url) // 过滤掉undefined
        .join("|");
      query.append("log_url", logUrls);

      const feedbackUrl = `https://kwaipilot.corp.kuaishou.com/feedback?${query}`;
      vscode.env.openExternal(vscode.Uri.parse(feedbackUrl));
      this.logger.info(`feedback`, "browser", { value: feedbackUrl });
    };

    const onUploadComplete = (file: UploadFile) => {
      uploadedFiles.push(file);
      uploadCount++;

      // 当所有文件都上传完成时，跳转到反馈页面
      if (uploadCount === logFiles.length) {
        gotoFeedback();
      }
    };

    const onUploadError = (error: any) => {
      this.logger.error("feedback error", "browser", { err: error });
      uploadCount++;

      // 即使有文件上传失败，当所有文件都处理完时也跳转
      if (uploadCount === logFiles.length) {
        gotoFeedback();
      }
    };

    // 上传所有日志文件
    logFiles.forEach((fileUri) => {
      upload(this.getBase(ConfigManager).get(Config.PROXY_URL) || DefaultBaseUrl, {
        file: fileUri,
        onSuccess: onUploadComplete,
        onFailed: onUploadError,
      });
    });
  }

  openHelp() {
    const helpUrl
      = "https://docs.corp.kuaishou.com/k/home/<USER>/fcAAvma1OGznlywMIiYfgHz6w";
    vscode.env.openExternal(vscode.Uri.parse(helpUrl));
  }

  private get globalState() {
    return this.getBase(GlobalStateManager);
  }

  private get settingPanel() {
    return this.getCore(SettingPanelModule);
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }
}
