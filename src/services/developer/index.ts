import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { ExtensionDeveloperShape } from "shared/lib/bridge/protocol";
import * as vscode from "vscode";
import { GlobalStateManager } from "../../base/state-manager";
import { GlobalState } from "shared/lib/state-manager/types";

export class DeveloperService extends ServiceModule implements ExtensionDeveloperShape {
  constructor(ext: ContextManager) {
    super(ext);
    vscode.commands.registerCommand("kwaipilot.reloadWebview", () => {
      this.$reloadWebview();
    });
    vscode.commands.executeCommand("setContext", "kwaipilot.isDeveloperMode", this.$getIsDeveloperMode());
  }

  /**
   * 重新加载 webview 内容
   */
  public $reloadWebview() {
    vscode.commands.executeCommand(
      "workbench.action.webview.reloadWebviewAction",
    );
  }

  $getIsDeveloperMode(): boolean {
    return this.getBase(GlobalStateManager).get(GlobalState.IS_DEVELOPER_MODE, false);
  }

  $setIsDeveloperMode(isDeveloperMode: boolean): void {
    vscode.commands.executeCommand("setContext", "kwaipilot.isDeveloperMode", isDeveloperMode);
    this.getBase(GlobalStateManager).update(GlobalState.IS_DEVELOPER_MODE, isDeveloperMode);
  }
}
