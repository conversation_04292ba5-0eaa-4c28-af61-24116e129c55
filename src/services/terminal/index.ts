import * as vscode from "vscode";
import * as path from "path";
import { mergePromise, TerminalProcess, TerminalProcessResultPromise } from "./TerminalProcess";
import { TerminalInfo, TerminalRegistry } from "./TerminalRegistry";
import pWaitFor from "p-wait-for";

declare const process: { platform: string };

declare module "vscode" {
  interface Terminal {
    shellIntegration?: {
      cwd?: vscode.Uri;
      executeCommand?: (command: string) => {
        read: () => AsyncIterable<string>;
      };
    };
  }
  interface Window {
    onDidStartTerminalShellExecution?: (
      listener: (e: any) => any,
      thisArgs?: any,
      disposables?: vscode.Disposable[]
    ) => vscode.Disposable;
  }
}

export class TerminalManager {
  private terminalIds: Set<number> = new Set();
  private processes: Map<number, TerminalProcess> = new Map();
  private disposables: vscode.Disposable[] = [];

  constructor() {
    let disposable: vscode.Disposable | undefined;
    try {
      disposable = (vscode.window as vscode.Window).onDidStartTerminalShellExecution?.(async (e: any) => {
        // Creating a read stream here results in a more consistent output. This is most obvious when running the `date` command.
        e?.execution?.read();
      });
    }
    catch (error) {
      // console.error("Error setting up onDidEndTerminalShellExecution", error)
    }
    if (disposable) {
      this.disposables.push(disposable);
    }
  }

  runCommand(terminalInfo: TerminalInfo, command: string): TerminalProcessResultPromise {
    // 显示通知
    terminalInfo.busy = true;
    terminalInfo.lastCommand = command;
    const process = new TerminalProcess();
    this.processes.set(terminalInfo.id, process);

    process.once("completed", () => {
      terminalInfo.busy = false;
    });

    // if shell integration is not available, remove terminal so it does not get reused as it may be running a long-running process
    process.once("no_shell_integration", () => {
      console.log(`no_shell_integration received for terminal ${terminalInfo.id}`);
      // Remove the terminal so we can't reuse it (in case it's running a long-running process)
      TerminalRegistry.removeTerminal(terminalInfo.id);
      this.terminalIds.delete(terminalInfo.id);
      this.processes.delete(terminalInfo.id);
    });

    const promise = new Promise<void>((resolve, reject) => {
      process.once("continue", () => {
        resolve();
      });
      process.once("error", (error: Error) => {
        console.error(`Error in terminal ${terminalInfo.id}:`, error);
        reject(error);
      });
    });

    // if shell integration is already active, run the command immediately
    if (terminalInfo.terminal.shellIntegration) {
      process.waitForShellIntegration = false;
      process.run(terminalInfo.terminal, command);
    }
    else {
      // docs recommend waiting 3s for shell integration to activate
      pWaitFor(() => terminalInfo.terminal.shellIntegration !== undefined, { timeout: 8000 }).finally(() => {
        const existingProcess = this.processes.get(terminalInfo.id);
        if (existingProcess && existingProcess.waitForShellIntegration) {
          existingProcess.waitForShellIntegration = false;
          existingProcess.run(terminalInfo.terminal, command);
        }
      });
    }

    return mergePromise(process, promise);
  }

  async getOrCreateTerminal(cwd: string): Promise<TerminalInfo> {
    const terminals = TerminalRegistry.getAllTerminals();

    // Find available terminal from our pool first (created for this task)
    const matchingTerminal = terminals.find((t: any) => {
      if (t.busy) {
        return false;
      }
      const terminalCwd = t.terminal.shellIntegration?.cwd; // one of cline's commands could have changed the cwd of the terminal
      if (!terminalCwd) {
        return false;
      }
      return arePathsEqual(vscode.Uri.file(cwd).fsPath, terminalCwd.fsPath);
    });
    if (matchingTerminal) {
      this.terminalIds.add(matchingTerminal.id);
      return matchingTerminal;
    }

    // If no matching terminal exists, try to find any non-busy terminal
    const availableTerminal = terminals.find((t: any) => !t.busy);
    if (availableTerminal) {
      // Navigate back to the desired directory
      await this.runCommand(availableTerminal, `cd "${cwd}"`);
      this.terminalIds.add(availableTerminal.id);
      return availableTerminal;
    }

    // If all terminals are busy, create a new one
    const newTerminalInfo = TerminalRegistry.createTerminal(cwd);
    this.terminalIds.add(newTerminalInfo.id);
    return newTerminalInfo;
  }

  getTerminals(busy: boolean): { id: number; lastCommand: string }[] {
    return Array.from(this.terminalIds)
      .map(id => TerminalRegistry.getTerminal(id))
      .filter((t): t is TerminalInfo => t !== undefined && t.busy === busy)
      .map(t => ({ id: t.id, lastCommand: t.lastCommand }));
  }

  getUnretrievedOutput(terminalId: number): string {
    if (!this.terminalIds.has(terminalId)) {
      return "";
    }
    const process = this.processes.get(terminalId);
    return process ? process.getUnretrievedOutput() : "";
  }

  isProcessHot(terminalId: number): boolean {
    const process = this.processes.get(terminalId);
    return process ? process.isHot : false;
  }

  disposeAll() {
    this.terminalIds.clear();
    this.processes.clear();
    this.disposables.forEach(disposable => disposable.dispose());
    this.disposables = [];
  }

  toggleTerminal() {
    vscode.commands.executeCommand("workbench.action.terminal.toggleTerminal");
  }
}

function arePathsEqual(path1?: string, path2?: string): boolean {
  if (!path1 && !path2) {
    return true;
  }
  if (!path1 || !path2) {
    return false;
  }

  path1 = normalizePath(path1);
  path2 = normalizePath(path2);

  if (process.platform === "win32") {
    return path1.toLowerCase() === path2.toLowerCase();
  }
  return path1 === path2;
}

function normalizePath(p: string): string {
  // normalize resolve ./.. segments, removes duplicate slashes, and standardizes path separators
  let normalized = path.normalize(p);
  // however it doesn't remove trailing slashes
  // remove trailing slash, except for root paths
  if (normalized.length > 1 && (normalized.endsWith("/") || normalized.endsWith("\\"))) {
    normalized = normalized.slice(0, -1);
  }
  return normalized;
}
