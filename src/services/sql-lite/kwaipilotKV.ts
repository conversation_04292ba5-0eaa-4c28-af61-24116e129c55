import { DataTypes, Model, Sequelize } from "sequelize";

class KwaipilotKVModel extends Model {
  public key!: string;
  public value!: Buffer;
}
/**
 * @returns whether the provided parameter is undefined or null.
 */
export function isUndefinedOrNull(obj: unknown): obj is undefined | null {
  return (typeof obj === "undefined" || obj === null);
}

export type KVStorageValue = string | boolean | number | undefined | null | object;

export interface IKwaipilotKVAccessor {

  get(key: string, fallbackValue: string): Promise<string>;
  get(key: string, fallbackValue?: string): Promise<string | undefined>;

  set(key: string, value: KVStorageValue): Promise<void>;
  delete(key: string): Promise<void>;
}

export class KwaipilotKVHelper {
  constructor(readonly accessor: IKwaipilotKVAccessor) {
  }

  getBoolean(key: string, fallbackValue: boolean): Promise<boolean>;
  getBoolean(key: string, fallbackValue?: boolean): Promise<boolean | undefined>;
  async getBoolean(key: string, fallbackValue?: boolean): Promise<boolean | undefined> {
    const persistedValue = await this.accessor.get(key);
    if (isUndefinedOrNull(persistedValue)) {
      return fallbackValue;
    }
    return persistedValue === "true";
  }

  getNumber(key: string, fallbackValue: number): Promise<number>;
  getNumber(key: string, fallbackValue?: number): Promise<number | undefined>;
  async getNumber(key: string, fallbackValue?: number): Promise<number | undefined> {
    const persistedValue = await this.accessor.get(key);
    if (isUndefinedOrNull(persistedValue)) {
      return fallbackValue;
    }
    return Number(persistedValue);
  }

  getObject<T extends object>(key: string, fallbackValue: T): Promise<T>;
  getObject<T extends object>(key: string, fallbackValue?: T): Promise<T | undefined>;
  async getObject<T extends object>(key: string, fallbackValue?: T): Promise<T | undefined> {
    const persistedValue = await this.accessor.get(key);
    if (isUndefinedOrNull(persistedValue)) {
      return fallbackValue;
    }
    return JSON.parse(persistedValue) as T;
  }
}

export class KwaipilotKVAccessor implements IKwaipilotKVAccessor {
  get(key: string, fallbackValue: string): Promise<string>;
  get(key: string, fallbackValue?: string): Promise<string | undefined>;
  async get(key: unknown, fallbackValue?: string): Promise<string | undefined> {
    const v = await KwaipilotKVModel.findOne({
      where: {
        key,
      },
    });
    const persistedValue = v?.value.toString();
    if (isUndefinedOrNull(persistedValue)) {
      return fallbackValue;
    }

    return persistedValue;
  }

  async set(key: string, value: KVStorageValue): Promise<void> {
    await KwaipilotKVModel.upsert({
      key,
      value: Buffer.from(JSON.stringify(value)),
    });
  }

  async delete(key: string): Promise<void> {
    await KwaipilotKVModel.destroy({
      where: {
        key,
      },
    });
  }
}

export class KwaipilotKV extends KwaipilotKVHelper implements IKwaipilotKVAccessor {
  constructor(sequelize: Sequelize) {
    super(new KwaipilotKVAccessor());
    KwaipilotKVModel.init({
      key: {
        type: DataTypes.TEXT,
        primaryKey: true,
        allowNull: false,
      },
      value: {
        type: DataTypes.BLOB,
        allowNull: false,
      },
    }, {
      sequelize: sequelize,
      tableName: "KwaipilotKV",
      timestamps: false,
    });
  }

  get(key: string, fallbackValue: string): Promise<string>;
  get(key: string, fallbackValue?: string): Promise<string | undefined>;
  async get(key: string, fallbackValue?: string): Promise<string | undefined> {
    return this.accessor.get(key, fallbackValue);
  }

  async set(key: string, value: KVStorageValue): Promise<void> {
    await this.accessor.set(key, value);
  }

  async delete(key: string): Promise<void> {
    await this.accessor.delete(key);
  }
}
