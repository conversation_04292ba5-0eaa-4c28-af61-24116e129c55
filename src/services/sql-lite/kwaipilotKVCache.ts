import { SqlLite } from ".";
import { LoggerManager } from "../../base/logger";
import { IKwaipilotKVAccessor, isUndefinedOrNull, KVStorageValue, KwaipilotKV, KwaipilotKVHelper } from "./kwaipilotKV";

type KVStorageRawValue = string;

const LOGGER_SCOPE = "kwapilotKV";

class MemoryCachedKwaipilotKVAccessor implements IKwaipilotKVAccessor {
  private cache: Map<string, KVStorageRawValue> = new Map();

  constructor(protected readonly sqlite: SqlLite, protected readonly logger: LoggerManager) {

  }

  private async getPersistentAccessor(): Promise<KwaipilotKV | undefined> {
    // 等待 sqlite 初始化完成
    await this.sqlite.initiation;
    return this.sqlite.kwaipilotKV;
  }

  get(key: string, fallbackValue: string): Promise<string>;
  get(key: string, fallbackValue?: string): Promise<string | undefined>;
  async get(key: string, fallbackValue?: string): Promise<string | undefined> {
    const cached = this.cache.get(key);
    if (cached) {
      return cached;
    }
    try {
      const accessor = await this.getPersistentAccessor();
      if (!accessor) {
        // sqlite 初始化失败, 降级
        return fallbackValue;
      }
      const persistedValue = await accessor.get(key);
      if (isUndefinedOrNull(persistedValue)) {
        return fallbackValue;
      }
      this.cache.set(key, persistedValue);
      return persistedValue;
    }
    catch (e) {
      this.logger.error(`persistanceFailed:get:${key}`, LOGGER_SCOPE, {
        err: e,
      });
      return fallbackValue;
    }
  }

  async set(key: string, value: KVStorageValue): Promise<void> {
    this.cache.set(key, JSON.stringify(value));

    const accessor = await this.getPersistentAccessor();
    if (!accessor) {
      // sqlite 初始化失败, 降级
      return;
    }
    accessor.set(key, value).catch((e) => {
      this.logger.error(`persistanceFailed:set:${key}`, LOGGER_SCOPE, {
        err: e,
      });
    });
  }

  async delete(key: string): Promise<void> {
    this.cache.delete(key);
    try {
      const accessor = await this.getPersistentAccessor();
      if (!accessor) {
        // sqlite 初始化失败, 降级
        return;
      }
      await accessor.delete(key);
    }
    catch (e) {
      this.logger.error(`persistanceFailed:delete:${key}`, LOGGER_SCOPE, {
        err: e,
      });
    }
  }
}
/**
 * 1. 在存取数据之前，先缓存数据，避免频繁读取数据库
 * 2. 为了降级 sqlite ，缓存优先，在持久化失败时，不会抛错 而是写入日志记录
 */
export class MemoryCachedKwaipilotKV extends KwaipilotKVHelper implements IKwaipilotKVAccessor {
  constructor(protected readonly sqlite: SqlLite, protected readonly logger: LoggerManager) {
    super(new MemoryCachedKwaipilotKVAccessor(sqlite, logger));
  }

  get(key: string, fallbackValue: string): Promise<string>;
  get(key: string, fallbackValue?: string): Promise<string | undefined>;
  async get(key: string, fallbackValue?: string): Promise<string | undefined> {
    return this.accessor.get(key, fallbackValue);
  }

  async set(key: string, value: KVStorageValue): Promise<void> {
    await this.accessor.set(key, value);
  }

  async delete(key: string): Promise<void> {
    await this.accessor.delete(key);
  }
}
