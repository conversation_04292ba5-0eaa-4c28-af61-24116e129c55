CREATE TABLE IF NOT EXISTS message (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  session_id TEXT NOT NULL,
  chat_id TEXT NOT NULL,
  content TEXT NOT NULL,
  create_time BIGINT NOT NULL,
  update_time BIGINT NOT NULL,
  dataVersion TEXT NOT NULL DEFAULT 'v0',
  UNIQUE (session_id, chat_id),
  INDEX idx_session_id_message (session_id)
);

CREATE TABLE IF NOT EXISTS session (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  session_id TEXT NOT NULL,
  session_name TEXT NOT NULL,
  expired_index TEXT NOT NULL,
  clear_context_index TEXT NOT NULL,
  create_time BIGINT NOT NULL,
  update_time BIGINT NOT NULL,
  UNIQUE (session_id),
  INDEX idx_session_id_session (session_id)
);

CREATE TABLE IF NOT EXISTS KwaipilotKV (
  key TEXT UNIQUE ON CONFLICT REPLACE,
  value BLOB
);