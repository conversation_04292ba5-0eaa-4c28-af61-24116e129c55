type Params<R = any> = {
  args?: any[];
  task: (...args: any[]) => Promise<R>;
  times: number;
  timeGap: number;
  onResolve: (result: R, context: { times: number; stop: () => void }) => void;
  onReject: (result: R, context: { times: number; stop: () => void }) => void;
  isFirstSuccessStop?: boolean;
  isFirstFailStop?: boolean;
};

class TaskRunner<R> {
  private params: Params<R>;
  private successCount: number;
  private failCount: number;
  private stopped: boolean;

  constructor(params: Params<R>) {
    this.params = params;
    this.successCount = 0;
    this.failCount = 0;
    this.stopped = false;
  }

  async run() {
    let result: any;
    const context = { times: 0, stop: this.stop };

    while (true) {
      context.times++;

      if (this.stopped) {
        break;
      }

      try {
        result = await this.params.task(...(this.params.args || []));
        this.successCount++;
        this.params.onResolve(result, context);

        if (this.params.isFirstSuccessStop && this.successCount === 1) {
          break;
        }
      }
      catch (error: any) {
        this.failCount++;
        this.params.onReject(error, context);

        if (this.params.isFirstFailStop && this.failCount === 1) {
          break;
        }
      }

      if (context.times >= this.params.times) {
        break;
      }

      await new Promise(resolve => setTimeout(resolve, this.params.timeGap));
    }
  }

  stop = () => {
    this.stopped = true;
  };
}

export default TaskRunner;
