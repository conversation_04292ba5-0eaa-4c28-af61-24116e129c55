import * as vscode from "vscode";

export function checkLogin(context: vscode.ExtensionContext) {
  const userInfo = context.globalState.get<{ name: string }>("userSsoInfo");
  if (!userInfo) {
    vscode.window.showInformationMessage("Kwaipilot：请在登录后使用相关功能！", "登录")
      .then((selection) => {
        if (selection === "登录") {
          vscode.commands.executeCommand("kwaipilot.login");
        }
      });
    return null;
  }
  return userInfo;
}
