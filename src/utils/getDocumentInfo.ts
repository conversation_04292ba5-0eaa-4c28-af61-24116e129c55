import * as vscode from "vscode";
/**
 *
 * @param document 文本
 * @param startLine 起始行号，是行号，所以是 index + 1
 * @param endLine 终止行号
 * @returns prefix,sufix
 */

export const getPrefixAndSuffix = (document: vscode.TextDocument, startLine: number, endLine: number) => {
  // const lines = document.split('\n');
  // const prefix = lines.slice(0, startLine-1).join('\n');
  // const suffix = lines.slice(endLine-1).join('\n') + '\n';
  const startIndex = startLine - 1;
  const endIndex = endLine - 1;
  const prefix = document.getText(new vscode.Range(new vscode.Position(0, 0), new vscode.Position(startIndex, 0)));
  const suffix = document.getText(new vscode.Range(new vscode.Position(endLine, 0), new vscode.Position(document.lineCount, Infinity)));
  const content = document.getText(new vscode.Range(new vscode.Position(startIndex, 0), new vscode.Position(endIndex, Infinity)));
  return {
    prefix,
    suffix,
    content,
  };
};
