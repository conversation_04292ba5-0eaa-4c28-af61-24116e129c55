import { StateManager, StateSchema } from "../common/state";
import { getWorkspaceTreeAbsolute } from "./vscode";
import * as fs from "fs";

type StateKey = keyof StateSchema;

export class FileWorkspaceStateQueue {
  private key: StateKey;
  private maxLen: number;
  constructor(key: StateKey, maxLen = 20) {
    this.key = key;
    this.maxLen = maxLen;
  }

  get() {
    const stateManager = StateManager.getInstance();
    const files = Array.from(new Set(stateManager.get(this.key, [])));
    const newFile = files.filter(dir => fs.existsSync(getWorkspaceTreeAbsolute(dir)));
    if (newFile.length !== files.length) {
      stateManager.update(this.key, newFile);
    }
    return newFile;
  }

  set(filepath: string | string[]) {
    const stateManager = StateManager.getInstance();
    const history = stateManager.get(this.key, []);

    const newHistory = Array.from(new Set([...filepath, ...history]));

    stateManager.update(this.key, newHistory.slice(0, this.maxLen));
  }
}
