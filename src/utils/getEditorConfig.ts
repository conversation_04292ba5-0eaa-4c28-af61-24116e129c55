import * as vscode from "vscode";
import { EditorConfig } from "../shared/types";

const editorConfig = vscode.workspace.getConfiguration("editor");

const colorTheme = vscode.workspace.getConfiguration("workbench").get("colorTheme") as string;

// 获取具体配置项
export const editorConfigs: EditorConfig = {
  // 字体大小
  fontSize: (editorConfig.get("fontSize") ?? 14) as number,
  // 字体系列
  fontFamily: (editorConfig.get("fontFamily") ?? "Consolas, \"Courier New\", monospace") as string,
  // 行高
  // tab大小
  tabSize: (editorConfig.get("tabSize") ?? 4) as number,
  // 当前主题
  theme: colorTheme,
};

// 监听配置变化
export function watchConfigChanges(context: vscode.ExtensionContext) {
  // 监听编辑器配置变化
  context.subscriptions.push(
    vscode.workspace.onDidChangeConfiguration((e) => {
      // 检查是否是编辑器配置发生变化
      if (e.affectsConfiguration("editor")
        || e.affectsConfiguration("workbench.colorTheme")) {
        const editorConfig = vscode.workspace.getConfiguration("editor");
        const colorTheme = vscode.workspace.getConfiguration("workbench").get("colorTheme") as string;
        editorConfigs.fontSize = (editorConfig.get("fontSize") ?? 14) as number;
        editorConfigs.fontFamily = (editorConfig.get("fontFamily") ?? "Consolas, \"Courier New\", monospace") as string;
        editorConfigs.tabSize = (editorConfig.get("tabSize") ?? 4) as number;
        editorConfigs.theme = colorTheme;
      }
    }),
  );
}
