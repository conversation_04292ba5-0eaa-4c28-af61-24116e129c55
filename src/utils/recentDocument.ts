class CommonDocument {
  readonly fileName: string | undefined;
  readonly language: string | undefined;
  readonly content: string | undefined;
  readonly cursorOffset: number | undefined;

  constructor(fileName: string, language: string, content: string, cursorOffset: number) {
    this.fileName = fileName;
    this.language = language;
    this.content = content;
    this.cursorOffset = cursorOffset;
  }
}

export default CommonDocument;
