enum MessageType {
  Request = "request",
  Response = "response",
}

type Message = {
  type: MessageType;
  id: string;
  payload: RequestPayload;
};

export type RequestPayload = {
  action: string;
  data: any;
};

export class ChannelManager {
  /**
   * 保障运行性能
   * 有限数量的事件监听，采用 object 充当 map
   * 大量的请求次数, 并且有删除操作, 采用 Map 来记录
   * 参考链接1: https://v8.js.cn/blog/fast-properties/
   * 参考链接2: https://rehmat-sayany.medium.com/using-map-over-objects-in-javascript-a-performance-benchmark-ff2f351851ed
   */
  private handlers: { [key: string]: Array<(data: any) => any> } = {};
  private pendingRequests: Map<
    string,
    { resolve: (data: any) => void; reject: (data: any) => void }
  > = new Map();

  private interceptor?: (payload: any, id: string) => boolean;

  constructor(
    private postMessageFn: (message: Message) => void,
    private onMessageFn: (handler: (event: Message) => void) => void,
  ) {
    this.onMessageFn(event => this.handleMessage(event));
  }

  public request(action: string, data: any): Promise<any> {
    const message = this.pack(action, data);
    const promise = this.recordPendingRequest(message.id);
    this.postMessageFn(message);
    return promise;
  }

  public send(action: string, data: any) {
    const message = this.pack(action, data);
    this.postMessageFn(action ? message : data);
  }

  public addHandler(action: string, handler: (data: any) => any): void {
    if (!this.handlers[action]) {
      this.handlers[action] = [];
    }
    this.handlers[action].push(handler);
  }

  public removeHandler(action: string, handler: (data: any) => any): void {
    if (this.handlers[action]) {
      this.handlers[action] = this.handlers[action].filter(
        h => h !== handler,
      );
    }
  }

  /**
   * 设置检验函数, 如果检验函数不通过，则不执行消息处理
   * @param interceptor
   */
  public setInterceptor(interceptor: (payload: any, id: string) => boolean) {
    this.interceptor = interceptor;
  }

  private pack(action: string, data: any) {
    const id = this.generateUniqueId();
    const message: Message = {
      type: MessageType.Request,
      id,
      payload: { action, data },
    };
    return message;
  }

  private recordPendingRequest(key: string) {
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(key, { resolve, reject });
    });
  }

  private handleMessage(event: Message) {
    if (!event) {
      return;
    }
    const { type, id, payload } = event;
    // 如果 interceptor 存在并且检验不通过，则直接返回
    if (this.interceptor && !this.interceptor(payload, id)) {
      return;
    }
    if (type === MessageType.Request) {
      const { action, data } = payload as RequestPayload;
      const handlers = this.handlers[action] ?? [];
      for (const handler of handlers) {
        if (handler) {
          Promise.resolve(handler(data)).then((response) => {
            const responseMessage: Message = {
              type: MessageType.Response,
              id,
              payload: response,
            };
            this.postMessageFn(responseMessage);
          });
        }
      }
    }
    else if (type === MessageType.Response) {
      const request = this.pendingRequests.get(id);
      if (request) {
        request.resolve(payload);
        this.pendingRequests.delete(id);
      }
    }
  }

  /**
   * 生成唯一id
   * @todo 考虑使用(时间戳 + 随机) or (序号) 保证唯一性
   * @returns
   */
  private generateUniqueId(): string {
    return "xxxxxx".replace(/[x]/g, () => Math.random().toString(16).slice(-1));
  }
}
