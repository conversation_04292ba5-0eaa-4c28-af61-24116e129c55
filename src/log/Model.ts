import { v4 as uuidv4 } from "uuid";
import { PageEnum, ElementEnum } from "./CommonEnum";
import * as os from "os";
import CommonDocument from "../utils/recentDocument";
import { ProjectInfo, getProjectInfo } from "../utils/projectInfo";
import * as vscode from "vscode";
import pkg from "../../package.json";

export const PLUGIN_VERSION = pkg.version;
export const PLUGIN_PLATFORM = "kwaipilot-vscode";
const SERVICE_NAME = "Kwaipilot Plugin";
const GROUP_NAME = "kinsight";
export const KPN = "kinsight";
let incrId = 0;
const HOST_NAME = "https://kinsight.corp.kuaishou.com/";

export enum CompletionLogType {
  UNKNOWN = "UNKNOWN",
  COMPLETION_SHOW = "COMPLETION_SHOW",
  COMPLETION_REQUEST = "COMPLETION_REQUEST",
  COMPLETION_APPLY = "COMPLETION_APPLY",
}
export class CustomStatEvent {
  key?: string;
  value?: string;
}
export class LogCommonParam {
  identityPackage?: IdentityPackage;
  devicePackage?: DevicePackage;
  networkPackage?: NetworkPackage;
  locationPackage?: LocationPackage;
  appPackage?: AppPackage;
  serviceName?: string;
  globalAttr?: string;
}
export class ShowUrlPackage {
  page?: string;
  params?: string;
  identity?: string;
  page_type?: number;
}
export class LocationPackage {
  location?: string;
}
export class ElementPackageParam {
  action?: string;
  params?: string;
}
export class ShowEventPackage {
  action?: number;
  type?: number;
  sub_action?: number;
  status?: number;
  url_package?: ShowUrlPackage;
  content_package?: string;
}
export class IdentityPackage {
  deviceId?: string;
  userId?: string;
}
export class UrlPackageParam {
  page?: string;
  params?: string;
  identity?: string;
  pageType?: number;
}
export class EventPackage {
  showEvent?: ShowEventPackage;
  taskEvent?: TaskEventParam;
}

export class LogExtraParam {
  completionType?: string;
  sendType?: string;
  modelType?: string;
  question?: string;
  reply?: string;
  isLikeReply?: boolean;
  code?: string;
  path?: string;
  completionOffset?: number;
  completionLength?: number;
  choices?: string[];
  viewedDocuments?: CommonDocument[];
}
export class CompletionLogParam {
  completionLogType?: CompletionLogType;
  completionType?: string;
  path?: string;
  code?: string;
  completionOffset?: number;
  completionLength?: number;
  chioces?: string[];
  viewedDocuments?: CommonDocument[];
  projectInfo?: ProjectInfo;
  modelType?: string;
  platform?: string;
  pluginVersion?: string;
  deviceName?: string;
  deviceModel?: string;
  deviceOsVersion?: string;
  deviceId?: string;
}
// Code Compeltion Log - V2
export class CodeCompletionLogParam {
  // ---- 基础信息 ----
  deviceId?: string;
  platform?: string;
  pluginVersion?: string;
  deviceName?: string;
  deviceModel?: string;
  deviceOsVersion?: string;
  projectName?: string; // 项目名
  gitRepo?: boolean;
  gitRemote?: string; // 仓库地址
  currentBranchName?: string; // 当前分支名
  gitUsername?: string;
  gitUserEmail?: string;
  // ---- 补全信息 ----
  absolutePath?: string; // 绝对路径
  relativePath?: string; // 相对路径
  language?: string;
  viewedDocuments?: CommonDocument[]; // 最近浏览的同类型文件，最大限制为3

  codeBeforeCursor?: string; // 光标前代码
  codeAfterCursor?: string; // 光标后代码
  completionCode?: string; // 补全代码
  completionLogprod?: number; // 模型输出概率
  modelType?: string; // 模型类型
  modelVersion?: string; // 模型版本

  requestId?: string; // 对应的请求id
  requestTimestamp?: number; // 请求时间戳MS
  shown?: boolean; // 是否曝光
  showStartTimestamp?: number; // 曝光开始时间
  rejected?: boolean; // 是否拒绝
  rejectTimestamp?: number; // 拒绝时间戳
  applied?: boolean;
  applyTimestamp?: number; // Apply时间戳
  acceptLastCompletion?: boolean; // 是否接受上一次补全
  lastApplyIntervalMs?: number; // 上一次apply间隔(ms)，如果没有则设置为-1
  lastRejectIntervalMs?: number; // 上一次reject间隔（ms），如果没有则设置为-1
  editCharCount?: number; // 编辑字符数量
  editIntervalMs?: number; // 编辑间隔(ms)
  maxNewTokens?: number;
  username?: string; // SSO Name
  flag?: string;
}
export class TaskEventParam {
  type?: number;
  status?: number;
  elementType?: number;
  operationType?: number;
  operationDirection?: number;
  sessionId?: string;
  elementPackage?: ElementPackageParam;
  urlPackage?: UrlPackageParam;
  contentPackage?: string;
}
export class StatPackage {
  custom_stat_event?: CustomStatEvent;
}
export class DevicePackage {
  model?: string;
  osVersion?: string;
  ua?: string;
}
export class DeviceParam {
  deviceName?: string;
  deviceModel?: string;
  deviceOsVersion?: string;
}
export class NetworkPackage {
  type?: number;
}
export class AppPackage {
  productName?: string;
  container?: string;
  product?: number;
  platform?: number;
  versionName?: string;
  versionCode?: string;
}
export class LogDetail {
  clientTimestamp?: number;
  clientIncrementId?: number;
  sessionId?: string;
  timeZone?: string;
  eventPackage?: EventPackage;
  statPackage?: StatPackage;
  serverHostname?: string;
}
export class LogParam {
  common?: LogCommonParam;
  logs?: LogDetail[];
}

export type IFeedbackLog = {
  chatId?: string;
  modelType?: string;
  question?: string;
  reply?: string;
  docList?: any[];
  feedback?: string;
  username?: string;
  topK?: number;
  infraPlatform?: string;
  platform?: string;
  pluginVersion?: string;
  biz?: string;
  deviceId?: string;
  deviceName?: string;
};

/** 点赞反馈埋点参数 */
export class FeedbackLogParam {
  detail: IFeedbackLog;
  constructor(detail: IFeedbackLog) {
    this.detail = detail;
  }
}

/** 代码续写埋点数据 - Basic */
export class BasicCompletionRequestParam {
  deviceId?: string;
  fileName?: string;
  languageId?: string;
  viewedDocuments?: CommonDocument[];
  username?: string;

  codeBeforeCursor?: string;
  codeAfterCursor?: string;
  completionCode?: string;
  completionLogprod?: number;
  modelType?: string;
  modelVersion?: string;

  requestTimestamp?: number;
  shown?: boolean;
  showStartTimestamp?: number;
  rejected?: boolean;
  rejectTimestamp?: number;
  applied?: boolean;
  applyTimestamp?: number;
  flag?: "CodePrediction";

  acceptLastCompletion?: boolean;
  lastApplyIntervalMs?: number;
  lastRejectIntervalMs?: number;
  editCharCount?: number;
  editIntervalMs?: number;

  infoMd5?: string;
  existSelectCompletion?: boolean;

  requestId?: string;
  showIndex?: number;
  startHandleTimestamp?: number;
}

/** 代码续写埋点 - V2 - total */
export function buildCodeCompletionLogParam(codeRequestParam: BasicCompletionRequestParam): CodeCompletionLogParam {
  const logParam = new CodeCompletionLogParam();
  logParam.platform = PLUGIN_PLATFORM;
  logParam.pluginVersion = PLUGIN_VERSION;

  const device = buildDeviceLog();
  logParam.deviceName = device.deviceName;
  logParam.deviceModel = device.deviceModel;
  logParam.deviceOsVersion = device.deviceOsVersion;

  const projectInfos = getProjectInfo();
  if (projectInfos) {
    const project = projectInfos[0];
    logParam.projectName = project.name;
    logParam.gitRepo = project.gitRepo;
    logParam.gitRemote = project.gitRemote;
    logParam.currentBranchName = project.currentBranchName;
    logParam.gitUsername = project.username;
    logParam.gitUserEmail = project.userEmail;
  }

  logParam.deviceId = codeRequestParam.deviceId;
  logParam.absolutePath = codeRequestParam.fileName;
  if (codeRequestParam.fileName) {
    logParam.relativePath = vscode.workspace.asRelativePath(codeRequestParam.fileName);
  }
  logParam.language = codeRequestParam.languageId;
  logParam.viewedDocuments = codeRequestParam.viewedDocuments;

  logParam.codeBeforeCursor = codeRequestParam.codeBeforeCursor;
  logParam.codeAfterCursor = codeRequestParam.codeAfterCursor;
  logParam.completionCode = codeRequestParam.completionCode;
  logParam.completionLogprod = codeRequestParam.completionLogprod;
  logParam.modelType = codeRequestParam.modelType;
  logParam.modelVersion = codeRequestParam.modelVersion;

  logParam.requestTimestamp = codeRequestParam.requestTimestamp;
  logParam.shown = codeRequestParam.shown;
  logParam.showStartTimestamp = codeRequestParam.showStartTimestamp;
  logParam.rejected = codeRequestParam.rejected;
  logParam.rejectTimestamp = codeRequestParam.rejectTimestamp;
  logParam.applied = codeRequestParam.applied;
  logParam.applyTimestamp = codeRequestParam.applyTimestamp;
  logParam.acceptLastCompletion = codeRequestParam.acceptLastCompletion;
  logParam.lastApplyIntervalMs = codeRequestParam.lastApplyIntervalMs;
  logParam.lastRejectIntervalMs = codeRequestParam.lastRejectIntervalMs;
  logParam.editCharCount = codeRequestParam.editCharCount;
  logParam.editIntervalMs = codeRequestParam.editIntervalMs;
  logParam.username = codeRequestParam.username;
  return logParam;
}

export function buildTaskLogParam(eventType: number, elementType: number, operationType: number, page: PageEnum, element: ElementEnum, extraParam: LogExtraParam): LogParam {
  const pageType = 0; // unknown
  const status = 7; // success
  const operationDirection = 0; // unknown

  const p = new LogParam();
  p.common = buildCommonParam(extraParam);
  const logList = [];
  const logDetail = new LogDetail();
  logDetail.clientTimestamp = Date.now();
  logDetail.timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  logDetail.clientIncrementId = (incrId++);
  logDetail.eventPackage = buildEventPackage(eventType, elementType, status, operationType, operationDirection, pageType, PageEnum[page], ElementEnum[element], extraParam);
  logDetail.statPackage = buildStatPackage();
  logDetail.serverHostname = HOST_NAME;

  logList.push(logDetail);
  p.logs = logList;
  return p;
}

function buildCommonParam(extraParam: LogExtraParam): LogCommonParam {
  const common = new LogCommonParam();
  common.identityPackage = buildIdentity();
  common.devicePackage = buildDevice();
  common.networkPackage = buildNetwork();
  common.locationPackage = new LocationPackage();
  common.appPackage = buildAppPackage();
  common.serviceName = SERVICE_NAME;

  if (extraParam) {
    common.globalAttr = JSON.stringify(extraParam);
  }

  return common;
}

function buildIdentity(): IdentityPackage {
  const identityPackage = new IdentityPackage();
  // 标识信息
  identityPackage.deviceId = os.hostname();
  identityPackage.userId = os.hostname();
  return identityPackage;
}

function buildDevice(): DevicePackage {
  const devicePackage = new DevicePackage();
  devicePackage.model = process.platform;
  devicePackage.osVersion = process.arch;
  devicePackage.ua = "vscode";

  return devicePackage;
}

export function buildDeviceLog(): DeviceParam {
  const deviceParam = new DeviceParam();
  deviceParam.deviceName = os.hostname();
  deviceParam.deviceModel = os.platform();
  deviceParam.deviceOsVersion = os.release();
  return deviceParam;
}

function buildNetwork(): NetworkPackage {
  const devicePackage = new NetworkPackage();
  devicePackage.type = 2;
  return devicePackage;
}

function buildAppPackage(): AppPackage {
  const devicePackage = new AppPackage();
  devicePackage.productName = KPN;
  devicePackage.container = "IDEA/AS";
  devicePackage.platform = 10;
  devicePackage.versionName = "1.0-SNAPSHOT";
  devicePackage.versionCode = "1";
  return devicePackage;
}

function buildStatPackage(): StatPackage {
  const statPackage = new StatPackage();

  const customStatEvent = new CustomStatEvent();
  customStatEvent.key = "";
  customStatEvent.value = "";
  statPackage.custom_stat_event = customStatEvent;
  return statPackage;
}

function buildEventPackage(eventType: number, elementType: number, status: number, operationType: number, operationDirection: number, pageType: number, page: string, element: string, extraParam: LogExtraParam): EventPackage {
  const eventPackage = new EventPackage();

  const taskEvent = new TaskEventParam();
  taskEvent.type = eventType;
  taskEvent.elementType = elementType;
  taskEvent.status = status;
  taskEvent.operationType = operationType;
  taskEvent.operationDirection = operationDirection;
  taskEvent.sessionId = GROUP_NAME + "_" + uuidv4().toLowerCase();
  taskEvent.contentPackage = "{}";

  const elementPacakge = new ElementPackageParam();
  elementPacakge.action = element;
  if (extraParam) {
    elementPacakge.params = JSON.stringify(extraParam);
  }
  taskEvent.elementPackage = elementPacakge;

  const urlPackage = new UrlPackageParam();
  urlPackage.page = page;
  if (extraParam) {
    urlPackage.params = JSON.stringify(extraParam);
  }
  urlPackage.identity = GROUP_NAME + "_" + uuidv4().toLowerCase();
  urlPackage.pageType = pageType;
  taskEvent.urlPackage = urlPackage;

  eventPackage.taskEvent = taskEvent;
  return eventPackage;
}

/** 反馈埋点 */
export function buildFeedbackLogParam(param: {
  chatId?: string;
  modelType?: string;
  question?: string;
  reply?: string;
  docList?: any[];
  feedback?: string;
  topK?: number;
  infraPlatform?: string;
  deviceId?: string;
  username: string;
}): FeedbackLogParam {
  const {
    chatId,
    modelType,
    question,
    reply,
    docList,
    feedback,
    username,
    topK,
    infraPlatform,
    deviceId,
  } = param;
  const feedbackParam = new FeedbackLogParam({
    chatId,
    modelType,
    question,
    reply,
    docList,
    feedback,
    username,
    topK,
    infraPlatform,
    platform: PLUGIN_PLATFORM,
    pluginVersion: PLUGIN_VERSION,
    biz: PLUGIN_PLATFORM,
    deviceId,
    deviceName: buildDeviceLog().deviceName,
  });
  return feedbackParam;
}
