import * as vscode from "vscode";
import * as child_process from "child_process";

interface ProjectGitInfo {
  /** 当前分支 */
  currentBranch: string;
  /** GIT 仓库地址 */
  url: string;
  /** 最新提交commit */
  latestCommit: string;
  /** 是否时快手的Git仓库地址 */
  isKwaiGitRepo: boolean;
}

export class GitUtils {
  constructor() {
  }

  async getProjectGitInfo(): Promise<ProjectGitInfo> {
    const workspace = vscode.workspace.workspaceFolders?.[0].uri.fsPath;
    if (!workspace) {
      return {
        currentBranch: "",
        url: "",
        latestCommit: "",
        isKwaiGitRepo: false,
      };
    }
    try {
      // 获取当前分支名称
      const currentBranch = child_process.execSync(`git -C ${workspace} rev-parse --abbrev-ref HEAD`)
        .toString().trim();

      // 获取最新的提交哈希
      const latestCommit = child_process.execSync(`git -C ${workspace} log -1 --format=%H`)
        .toString().trim();

      // 获取远程 URL
      const remoteUrl = child_process.execSync(`git -C ${workspace} remote get-url origin`)
        .toString().trim();
      return {
        currentBranch,
        url: remoteUrl,
        latestCommit,
        isKwaiGitRepo: remoteUrl?.startsWith("*************************") || remoteUrl?.startsWith("https://git.corp.kuaishou.com") || false,
      };
    }
    catch (err) {
      console.error("Error fetching git info:", err);
      return {
        currentBranch: "",
        url: "",
        latestCommit: "",
        isKwaiGitRepo: false,
      };
    }
  }
}
