import * as vscode from "vscode";
import { CoreModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { BasicCompletionRequestParam, buildCodeCompletionLogParam, buildDeviceLog, PLUGIN_VERSION, PLUGIN_PLATFORM } from "../../log/Model";
import CommonDocument from "../../utils/recentDocument";
import { ConfigManager, GlobalStateManager } from "../../base/state-manager";
import { Config, GlobalState } from "../../base/state-manager/types";
import { InlineCompletionItem } from "vscode";
import { Sleep, generateMD5 } from "../../utils";
import { WebloggerManager } from "../../base/weblogger";
import { DocumentManager } from "../document";
import { codeCompletionLog } from "../../log/LogService";
import { LoggerManager } from "../../base/logger";
import { DefaultBaseUrl } from "../../const";
import { Api } from "../../base/http-client";
import { Change, diffWords } from "diff";
import { RequestController } from "./requestController";

type DiffPartType = "+" | "-" | "=";

const MAX_SHOW_TIP_COUNT = 5;

export class CompletionModule extends CoreModule {
  private readonly loggerScope = "debugger-completion";

  private lastLog: BasicCompletionRequestParam | undefined;
  private lastHash: string | undefined;
  private showStartTimestamp: number = 0;
  private currentCompletion: string | undefined;
  private showAcceptTipCount: number = 0;
  private requestController: RequestController;
  private mabeHasResult = false;

  constructor(ext: ContextManager) {
    super(ext);
    this.showAcceptTipCount = this.globalState.get(GlobalState.SHOW_ACCEPT_TIP_COUNT, 0);
    this.initListeners();
    this.requestController = new RequestController(this.logger, this.config, this.globalState, this.getCore(DocumentManager), this.weblogger, this.api);
  }

  private get shouldShowAcceptTip(): boolean {
    return this.showAcceptTipCount < MAX_SHOW_TIP_COUNT;
  }

  private addOneCount() {
    this.showAcceptTipCount++;
    this.globalState.update(GlobalState.SHOW_ACCEPT_TIP_COUNT, this.showAcceptTipCount);
  }

  private readonly acceptLineDecorationType = vscode.window.createTextEditorDecorationType({
    after: {
      contentText: `⌘ + ↓ 采纳本行`,
      color: "rgba(153,153,153,0.35)",
      // 移除左边距
      margin: "0 0 0 1rem",
    },
  });

  /**
   * 显示接受行装饰器（仅在多行补全且未达到显示次数上限时）
   */
  private showAcceptLineDecorations(range: vscode.Range[]) {
    const activeEditor = vscode.window.activeTextEditor;
    if (!activeEditor || range.length === 0) {
      return;
    }

    // 只有满足显示条件且当前补全未显示过装饰器时才显示
    if (this.shouldShowAcceptTip) {
      activeEditor.setDecorations(this.acceptLineDecorationType, range);
      this.addOneCount(); // 仅在实际显示装饰器时增加计数
    }
  }

  /**
   * 清理接受行装饰器
   */
  private clearAcceptLineDecorations() {
    const activeEditor = vscode.window.activeTextEditor;
    if (activeEditor) {
      activeEditor.setDecorations(this.acceptLineDecorationType, []);
    }
  }

  /**
   * 重置当前补全的装饰器状态
   */
  private resetDecoratorState() {
    this.clearAcceptLineDecorations();
  }

  private initListeners() {
    vscode.languages.registerInlineCompletionItemProvider(
      { pattern: "**" },
      {
        provideInlineCompletionItems:
          this.provideInlineCompletionItems.bind(this),
      },
    );
    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaiPilot.showInlineCompletion", () => {
        // 触发内联代码提示
        vscode.commands.executeCommand("editor.action.inlineSuggest.trigger");
      }),
    );
    this.context.subscriptions.push(
      vscode.commands.registerCommand(
        "kwaiPilot.applyCompletion",
        this.applyCompletion.bind(this),
      ),
    );
    // 注册badcase上报命令
    this.context.subscriptions.push(
      vscode.commands.registerCommand(
        "kwaiPilot.reportBadCase",
        this.reportBadCase.bind(this),
      ),
    );
    this.context.subscriptions.push(vscode.commands.registerCommand("kwaipilot.lineAccept", async () => {
      this.getBase(WebloggerManager).$reportUserAction({ key: "accept_line_shortcut" });
      await vscode.commands.executeCommand("editor.action.inlineSuggest.acceptNextLine");
      this.clearAcceptLineDecorations();
      // 用户主动接受后，设置为最大值以不再显示提示
      this.showAcceptTipCount = MAX_SHOW_TIP_COUNT;
      this.globalState.update(GlobalState.SHOW_ACCEPT_TIP_COUNT, MAX_SHOW_TIP_COUNT);
    }));
  }

  /** 提供内联代码补全 */
  private async provideInlineCompletionItems(
    document: vscode.TextDocument,
    position: vscode.Position,
    context: vscode.InlineCompletionContext,
    token: vscode.CancellationToken,
  ): Promise<
    | vscode.InlineCompletionItem[]
    | vscode.InlineCompletionList
    | null
    | undefined
    > {
    const username = this.globalState.get(GlobalState.USER_INFO)?.name || "";

    // 清理之前的装饰器并重置状态
    this.resetDecoratorState();
    // 判断是否登录, 未登录则不触发补全
    if (!username) {
      return;
    }
    const delay = this.config.get(Config.CODE_COMPLETION_DELAY);
    await Sleep(delay);
    this.emit("dispose-prediction");
    // 判断是否开启代码补全, 如果未开启, 则不触发补全。但提供代码预测
    if (!this.config.get(Config.ENABLE)) {
      this.emit("code-prediction");
      return;
    }
    const languageId = document.languageId;
    const lineInfo = document.lineAt(position.line).text;

    /**
     * 判断是否开启注释补全,
     * 如果未开启, 并且光标在注释区域内, 则不触发补全
     */
    if (this.checkCommentCompletion(languageId, lineInfo)) {
      return;
    }

    const textBeforeCursor = document.getText(
      new vscode.Range(new vscode.Position(0, 0), position),
    );
    const textAfterCursor = document.getText(
      new vscode.Range(
        position,
        document.lineAt(document.lineCount - 1).range.end,
      ),
    );
    const fileName = document.fileName;
    if (token.isCancellationRequested || fileName.startsWith("git")) {
      return Promise.resolve([] as InlineCompletionItem[]);
    }
    const curMd5 = generateMD5(
      fileName,
      languageId,
      textBeforeCursor,
      textAfterCursor,
    );

    // 2. 判断上次是否Apply - 存在SelectCompletion的情况下，不执行 - 此处只发送Reject埋点
    if (
      this.lastLog
      && !this.lastLog?.existSelectCompletion
      && this.lastLog?.applied != true
      && this.lastHash
    ) {
      if (this.lastHash != curMd5) {
        // console.log(" --- Send Reject --- ");
        const rejectTimestamp = Date.now();
        this.lastLog.rejectTimestamp = rejectTimestamp;
        this.lastLog.rejected = true;
        // 埋点上传
        codeCompletionLog(buildCodeCompletionLogParam(this.lastLog));
        this.lastHash = curMd5;
        this.currentCompletion = undefined; // 清除当前续写内容
      }
    }
    this.lastLog = undefined;
    const existSelectCompletion = !!context.selectedCompletionInfo;
    const items: any[] = []; // 补全列表
    let requestTimestamp = 0;
    // 基础续写指标
    let codeBasicLogParam: BasicCompletionRequestParam | undefined;

    // @todo 获取最近的文件信息 -> 获取光标位置的关联信息
    const viewedDocuments = this.recentlyOpenedDoc
      .filter(doc => doc.language == languageId && doc.fileName != fileName)
      .reverse()
      .slice(0, 3);
    try {
      // 记录请求时间
      requestTimestamp = Date.now();
      this.weblogger.sendNodeClick("VS_REQUEST_CODE_COMPLETION");
      this.weblogger.sendNodePV("VS_HOME", {
        source: "node",
      });
      const proxyUrl = this.config.get(Config.PROXY_URL) || DefaultBaseUrl;
      const deviceId = this.getBase(GlobalStateManager).get(GlobalState.DEVICE_ID);
      const maxNewTokensForCodeCompletion
        = this.config.get(Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION) || 32;
      const modelType = this.config.get(Config.MODEL_TYPE) || "";

      this.logger.info("requestCodeCompletion", this.loggerScope, {
        value: {
          proxyUrl,
          textBeforeCursor,
          textAfterCursor,
          languageId,
          fileName,
          position,
          deviceId,
          maxNewTokensForCodeCompletion,
          modelType,
        },
      });

      // 4. 获取代码补全
      let answers: any;
      if (this.mabeHasResult) {
        this.mabeHasResult = false;
        const result = await this.requestController.getLastCompletionResult();
        if (result) {
          answers = result;
        }
      }
      else {
        answers = await this.requestController.requestCodeCompletion(document, position);
      }
      this.weblogger.sendNodeClick("VS_REQUEST_CODE_COMPLETION_END", {
        duration: Date.now() - requestTimestamp,
      });
      if (!answers) {
        // 代码补全无返回时触发代码预测
        this.emit("code-prediction");
        this.logger.info("没有续写结果", this.loggerScope);
        return;
      }
      this.emit("dispose-prediction");
      this.logger.info("code completion item", this.loggerScope, {
        value: answers,
      });
      const selectedCompletionInfo = context.selectedCompletionInfo;
      answers.forEach(
        (item: {
          completionLogprod: any;
          code: string | any[];
          modelType: any;
          modelVersion: any;
        }) => {
          if (item.code?.length == 0) {
            return Promise.resolve([] as InlineCompletionItem[]);
          }
          const code = item.code.toString();
          this.currentCompletion = code; // 记录当前展示的续写内容

          const startPos = selectedCompletionInfo?.range.start ?? position;
          let range = new vscode.Range(startPos, startPos);
          let completionText = code;
          const isSingleLineCompletion = code.split("\n").length <= 1;

          if (isSingleLineCompletion) {
            const lastLineOfCompletionText = completionText.split("\n").pop();
            const currentText = document
              .lineAt(startPos)
              .text.substring(startPos.character);
            const diffs = diffWords(
              currentText,
              lastLineOfCompletionText ?? "",
            );

            if (this.diffPatternMatches(diffs, ["+"])) { /* empty */ }
            else if (
              this.diffPatternMatches(diffs, ["+", "="])
              || this.diffPatternMatches(diffs, ["+", "=", "+"])
            ) {
              range = new vscode.Range(
                startPos,
                document.lineAt(startPos).range.end,
              );
            }
            else if (
              this.diffPatternMatches(diffs, ["+", "-"])
              || this.diffPatternMatches(diffs, ["-", "+"])
            ) { /* empty */ }
            else {
              if (diffs[0]?.added) {
                completionText = diffs[0].value;
              }
              else {
                return undefined;
              }
            }
          }
          else {
            this.showAcceptLineDecorations([range]); // 使用新的显示方法
            range = new vscode.Range(startPos, document.lineAt(startPos).range.end);
          }
          const completionLogprod = item.completionLogprod;
          const modelType = item.modelType || undefined;
          const modelVersion = item.modelVersion || undefined;
          // 补充埋点属性
          codeBasicLogParam = this.buildBasicCompletionParam(
            deviceId,
            fileName,
            languageId,
            textBeforeCursor,
            textAfterCursor,
            code,
            completionLogprod,
            modelType,
            modelVersion,
            viewedDocuments,
            username,
            this.requestController.requestId,
            this.requestController.showIndex,
          );
          items.push({
            insertText: code,
            range: range,
            command: {
              command: "kwaiPilot.applyCompletion",
              arguments: [codeBasicLogParam, requestTimestamp],
            },
          });
        },
      );
    }
    catch (err: any) {
      this.logger.error("completion error", this.loggerScope, {
        err,
      });
      const icon = "$(error)";
      vscode.window.setStatusBarMessage(
        `${icon} Kwaipilot: Loading results error...`,
      );
    }

    if (!items || items.length == 0 || token.isCancellationRequested) {
      return Promise.resolve([] as InlineCompletionItem[]);
    }

    this.showStartTimestamp = Date.now();
    // 5. 更新曝光
    if (codeBasicLogParam) {
      this.lastLog = this.buildCompletionRequestParam(
        codeBasicLogParam,
        requestTimestamp,
        true,
        this.showStartTimestamp,
        false,
        0,
        false,
        0,
        existSelectCompletion,
      );
      // console.log("计划曝光 rejct时是否发送卖点", !existSelectCompletion);
      this.lastHash = this.lastLog.infoMd5;
    }

    return { items };
  }

  /**
   * 接受代码补全, 上报相关埋点
   */
  private async applyCompletion(
    codeBasicLogParam: any,
    requestTimestamp: number,
  ) {
    // 清理装饰器
    this.clearAcceptLineDecorations();
    this.mabeHasResult = true;

    const currentApplyTime = Date.now();
    const {
      deviceId,
      fileName,
      languageId,
      codeBeforeCursor,
      codeAfterCursor,
      completionCode,
      completionLogprod,
      modelType,
      modelVersion,
      existSelectCompletion,
      viewedDocuments,
      username,
      requestId,
      showIndex,
    } = codeBasicLogParam;
    const codeParam = this.buildBasicCompletionParam(
      deviceId,
      fileName,
      languageId,
      codeBeforeCursor,
      codeAfterCursor,
      completionCode,
      completionLogprod,
      modelType,
      modelVersion,
      viewedDocuments,
      username,
      requestId,
      showIndex,
    );

    // 埋点上传
    try {
      this.lastLog = this.buildCompletionRequestParam(
        codeParam,
        requestTimestamp,
        true,
        this.showStartTimestamp,
        false,
        0,
        true,
        currentApplyTime,
        existSelectCompletion,
      );
      this.logger.info(`上报埋点的数据, ${this.lastLog.requestId}-${this.lastLog.showIndex}-${this.lastLog.requestTimestamp} `, this.loggerScope, { value: this.lastLog });
      codeCompletionLog(buildCodeCompletionLogParam(this.lastLog));
      this.lastHash = this.lastLog.infoMd5;
      this.currentCompletion = undefined; // 清除当前续写内容
    }
    catch (e) {
      console.log(e);
    }
  }

  /** 补充代码生成参数 */
  private buildBasicCompletionParam(
    deviceId: string | undefined,
    fileName: string,
    languageId: string,
    textBeforeCursor: string,
    textAfterCursor: string,
    code: string,
    completionLogprod: number,
    modelType: string | undefined,
    modelVersion: string | undefined,
    viewedDocuments: CommonDocument[],
    username: string | undefined,
    requestId: string,
    showIndex: number | undefined,
  ): BasicCompletionRequestParam {
    const codeParam = new BasicCompletionRequestParam();
    codeParam.deviceId = deviceId;
    codeParam.fileName = fileName;
    codeParam.languageId = languageId;

    codeParam.codeBeforeCursor = textBeforeCursor;
    codeParam.codeAfterCursor = textAfterCursor;
    codeParam.completionCode = code;
    codeParam.completionLogprod = completionLogprod;
    codeParam.modelType = modelType;
    codeParam.modelVersion = modelVersion;
    codeParam.viewedDocuments = viewedDocuments;
    codeParam.username = username;
    codeParam.requestId = requestId;
    codeParam.showIndex = showIndex;

    codeParam.infoMd5 = generateMD5(
      fileName,
      languageId,
      textBeforeCursor,
      textAfterCursor,
    );
    return codeParam;
  }

  /** 构建续写埋点参数 */
  private buildCompletionRequestParam(
    param: BasicCompletionRequestParam,
    requestTimestamp: number | undefined,
    shown: boolean | undefined,
    showStartTimestamp: number | undefined,
    rejected: boolean,
    rejectTimestamp: number,
    applied: boolean,
    applyTimestamp: number,
    existSelectCompletion: boolean,
  ): BasicCompletionRequestParam {
    const logParam = new BasicCompletionRequestParam();
    logParam.deviceId = param.deviceId;
    logParam.fileName = param.fileName;
    logParam.languageId = param.languageId;
    logParam.viewedDocuments = param.viewedDocuments;
    logParam.username = param.username;

    logParam.codeBeforeCursor = param.codeBeforeCursor;
    logParam.codeAfterCursor = param.codeAfterCursor;
    logParam.completionCode = param.completionCode;
    logParam.completionLogprod = param.completionLogprod;
    logParam.modelType = param.modelType;
    logParam.modelVersion = param.modelVersion;

    logParam.requestTimestamp = requestTimestamp;
    logParam.shown = shown;
    logParam.showStartTimestamp = showStartTimestamp;
    logParam.rejected = rejected;
    logParam.rejectTimestamp = rejectTimestamp;
    logParam.applied = applied;
    logParam.applyTimestamp = applyTimestamp;

    logParam.infoMd5 = param.infoMd5;
    logParam.existSelectCompletion = existSelectCompletion;

    logParam.requestId = param.requestId;
    logParam.showIndex = param.showIndex;
    return logParam;
  }

  /**
   * 检查未开启注释补全，且当前行是注释行
   * @todo 未严格判断，临时方案待优化
   * @param languageId 语言类型
   * @param lineInfo 行信息
   * @returns
   */
  private checkCommentCompletion(languageId: string, lineInfo: string) {
    const text = lineInfo.trim();
    return (
      !this.config.get(Config.COMMENT_COMPLETION_ENABLE)
      && languageId != null
      && (text.startsWith("//")
        || text.startsWith("/*")
        || text.startsWith("*")
        || text.startsWith("#"))
    );
  }

  private diffPatternMatches(
    diffs: Change[],
    pattern: DiffPartType[],
  ): boolean {
    if (diffs.length !== pattern.length) {
      return false;
    }

    for (let i = 0; i < diffs.length; i++) {
      const diff = diffs[i];
      const diffPartType: DiffPartType
        = !diff.added && !diff.removed ? "=" : diff.added ? "+" : "-";

      if (diffPartType !== pattern[i]) {
        return false;
      }
    }

    return true;
  }

  /** 上报badcase */
  private async reportBadCase() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return;
    }

    if (!this.currentCompletion) {
      vscode.window.showInformationMessage("请在有续写内容时上报badcase");
      return;
    }

    const document = editor.document;
    const position = editor.selection.active;
    const fileName = document.fileName;
    const languageId = document.languageId;
    const textBeforeCursor = document.getText(
      new vscode.Range(new vscode.Position(0, 0), position),
    );
    const textAfterCursor = document.getText(
      new vscode.Range(
        position,
        document.lineAt(document.lineCount - 1).range.end,
      ),
    );
    const username = this.globalState.get(GlobalState.USER_INFO)?.name || "";
    const proxyUrl = this.config.get(Config.PROXY_URL) || DefaultBaseUrl;
    const deviceId = this.globalState.get(GlobalState.DEVICE_ID);
    const maxNewTokensForCodeCompletion
      = this.config.get(Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION) || 32;
    const modelType = this.config.get(Config.MODEL_TYPE) || "";

    try {
      this.weblogger.sendNodeClick("VS_REPORT_BAD_CASE");
      // 获取最近浏览的文件
      const viewedDocuments = this.recentlyOpenedDoc
        .filter(doc => doc.language == languageId && doc.fileName != fileName)
        .reverse()
        .slice(0, 3);

      const deviceInfo = buildDeviceLog();

      // 构建请求参数
      const requestData = {
        projectName: undefined,
        gitRepo: undefined,
        gitRemote: undefined,
        currentBranchName: undefined,
        gitUsername: undefined,
        gitUserEmail: undefined,
        username: username,

        absolutePath: fileName,
        relativePath: vscode.workspace.asRelativePath(fileName),
        language: languageId,

        codeBeforeCursor: textBeforeCursor,
        codeAfterCursor: textAfterCursor,
        cursorOffset: position.character,
        completionCode: this.currentCompletion, // 使用当前展示的续写内容

        count: 3,
        maxNewTokens: maxNewTokensForCodeCompletion,
        modelType: modelType,
        deviceId: deviceId,
        platform: PLUGIN_PLATFORM,
        pluginVersion: PLUGIN_VERSION,
        deviceName: deviceInfo.deviceName,
        deviceModel: deviceInfo.deviceModel,
        deviceOsVersion: deviceInfo.deviceOsVersion,
        viewedDocuments: viewedDocuments,
        stream: false,
      };

      // 发送badcase上报请求
      await this.api.post(`${proxyUrl}/eapi/kwaipilot/code/completions/case`, JSON.stringify(requestData));
      vscode.window.showInformationMessage("已成功上报badcase");
      this.weblogger.sendNodeClick("VS_REPORT_BAD_CASE_SUCCESS");
      this.logger.info("report bad case success", this.loggerScope, {
        value: requestData,
      });
    }
    catch (err: any) {
      this.logger.error("report bad case error", this.loggerScope, {
        err,
      });
      vscode.window.showErrorMessage("上报badcase失败");
      this.weblogger.sendNodeClick("VS_REPORT_BAD_CASE_ERROR");
    }
  }

  private get globalState() {
    return this.getBase(GlobalStateManager);
  }

  private get config() {
    return this.getBase(ConfigManager);
  }

  private get weblogger() {
    return this.getBase(WebloggerManager);
  }

  private get recentlyOpenedDoc() {
    try {
      return this.getCore(DocumentManager).getRecentlyDocs(10);
    }
    catch (error) {
      return [];
    }
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get api() {
    return this.getBase(Api);
  }
}
