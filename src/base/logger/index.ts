import Transport from "winston-transport";
import { createLogger, transports, format } from "winston";
import "winston-daily-rotate-file";

import { BaseModule } from "..";
import { ContextManager } from "../context-manager";
import { OutputChannel } from "../../utils/log";

import { LOG_PATH } from "../../common/const";
import { LoggerSupplementaryField } from "../../shared/types/logger";

import { type Logger } from "winston";
import { ExtensionMode, version } from "vscode";
import pkg from "../../../package.json";

class VscodeTransport extends Transport {
  constructor(opts?: Transport.TransportStreamOptions) {
    super(opts);
  }

  private formatConsoleMessage(info: any): string {
    let message = "";

    // 添加scope标签如果存在
    if (info.tags?.scope) {
      message += ` [${info.tags.scope}]`;
    }

    // 添加主要消息
    message += `: ${info.message}`;

    // 如果是错误，添加错误信息
    if (info.err) {
      message += `\n  Error: ${info.err.message}`;
      if (info.err.stack) {
        message += `\n  Stack: ${info.err.stack}`;
      }
    }

    return message;
  }

  public log(info: any, next: () => void) {
    setImmediate(() => {
      this.emit("logged", info);
    });

    const allowedLevels = ["error", "warn", "info"];
    if (allowedLevels.includes(info.level)) {
      switch (info.level) {
        case "error":
          OutputChannel.error(this.formatConsoleMessage(info));
          break;
        case "warn":
          OutputChannel.warn(this.formatConsoleMessage(info));
          break;
        case "info":
          OutputChannel.info(this.formatConsoleMessage(info));
          break;
      }
    }

    next();
  }
}
export class LoggerManager extends BaseModule {
  private readonly logger: Logger;
  constructor(ext: ContextManager) {
    super(ext);
    const isDev = ext.context.extensionMode === ExtensionMode.Development;
    this.logger = createLogger({
      format: format.combine(
        format.timestamp(),
        format.errors({ stack: true }),
        format.printf(({ timestamp, level, message, err, tags: meta = {} }) => {
          function formatDateString(dateString: string): string {
            const beijingOffset = 8 * 60;
            const date = new Date(new Date(dateString).getTime() + beijingOffset * 60 * 1000);
            const yy = String(date.getUTCFullYear()).slice(-2);
            const mm = String(date.getUTCMonth() + 1).padStart(2, "0");
            const dd = String(date.getUTCDate()).padStart(2, "0");
            const hh = String(date.getUTCHours()).padStart(2, "0");
            const min = String(date.getUTCMinutes()).padStart(2, "0");
            const ss = String(date.getUTCSeconds()).padStart(2, "0");

            return `${yy}/${mm}/${dd} ${hh}:${min}:${ss}`;
          }
          const localtime = formatDateString(timestamp);

          const data = {
            level,
            localtime,
            content: message,
            ...meta,
          };
          if (err) {
            data.err = err;
          }
          return `[${level}]: ${isDev ? "" : "[file log]"} [${JSON.stringify(data)}]`;
        }),
      ),
      transports: isDev
        ? [
            new VscodeTransport(),
            new transports.DailyRotateFile({
              level: "info",
              filename: `kwaipilot_%DATE%.log`,
              dirname: LOG_PATH,
              datePattern: "YYYY-MM-DD",
              zippedArchive: false,
              maxSize: "50m",
              maxFiles: "3d",
              createSymlink: true,
              symlinkName: "kwaipilot.log",

            }),
          ]
        : [
            new transports.DailyRotateFile({
              level: "info",
              filename: `kwaipilot_%DATE%.log`,
              dirname: LOG_PATH,
              datePattern: "YYYY-MM-DD",
              zippedArchive: false,
              maxSize: "50m",
              maxFiles: "3d",
              createSymlink: true,
              symlinkName: "kwaipilot.log",

            }),
            new VscodeTransport(),
          ],
    });
  }

  public debug(message: string, scope: string, tags?: LoggerSupplementaryField) {
    this.logger.debug({ message, tags: { ...tags, scope, ideVersion: version, pluginVersion: pkg.version } });
  }

  public info(message: string, scope: string, tags?: LoggerSupplementaryField) {
    this.logger.info({ message, tags: { ...tags, scope, ideVersion: version, pluginVersion: pkg.version } });
  }

  public error(message: string, scope: string, tags?: LoggerSupplementaryField) {
    if (tags?.err instanceof Error) {
      const err = tags.err;
      delete tags.err;
      this.logger.error({ message, err, tags, ideVersion: version, pluginVersion: pkg.version });
    }
    else {
      this.logger.error({ message, tags: { ...tags, scope, ideVersion: version, pluginVersion: pkg.version } });
    }
  }

  public warn(message: string, scope: string, tags?: LoggerSupplementaryField) {
    this.logger.warn({ message, tags: { ...tags, scope, ideVersion: version, pluginVersion: pkg.version } });
  }
}
