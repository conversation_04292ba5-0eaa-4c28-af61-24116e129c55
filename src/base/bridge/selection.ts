import { SelectionOrFileContext, toRangeData } from "shared";
import * as vscode from "vscode";

/**
 * Gets the context items for the current selection in the active editor, or the entire file if there is no selection.
 *
 * The context items include the file URI, content, source (selection or file), range, and token count.
 * If the file is ignored, an empty array is returned.
 *
 * @returns An array of context items for the current selection or file.
 */
export async function getSelectionOrFileContext(): Promise<SelectionOrFileContext | null> {
  // const editor = getEditor()?.active;
  const editor = vscode.window.activeTextEditor;
  const document = editor?.document;
  const selection = editor?.selection;

  if (!document || !selection /* || (await shouldIgnore(document.uri)) */) {
    return null;
  }

  // If the selection is empty, use the entire file content
  const range = selection.start.isEqual(selection.end) ? undefined : selection;
  const content = editor.document.getText(range);

  return {
    uri: document.uri.toString(),
    relativePath: vscode.workspace.asRelativePath(document.uri),
    content,
    // repoName: await getFirstRepoNameContainingUri(document.uri),
    range: range && toRangeData(range),
  };
}
