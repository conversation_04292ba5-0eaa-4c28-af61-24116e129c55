// NOTE: ide 专用文件，插件请勿修改
import * as vscode from "vscode";
import { WEBVIEW_BRIDGE_EVENT_NAME, WebviewBridgeParams } from "../../shared/types/bridge";
import { BaseBridge } from "./BaseBridge";

/**
 * VSCode Native Bridge类，使用"vscodeNativeBridge"作为日志作用域
 * 重写了callHandler方法，移除了webview ready检查
 */
export class Bridge extends BaseBridge {
  protected loggerScope = "vscodeNativeBridge";

  /**
   * 重写callHandler方法，移除webview ready检查
   * 插件调用视图的handler
   */
  callHandler<T extends WEBVIEW_BRIDGE_EVENT_NAME>(
    webview: vscode.Webview,
    handlerName: T,
    data: WebviewBridgeParams[T],
    callback?: (data: any) => void,
  ) {
    const callbackId = "native_" + String(this.callbackId++);
    if (callback) {
      this.callbacks.set(callbackId, callback);
    }

    webview.postMessage({
      protocol: "callHandler",
      name: handlerName,
      callbackId,
      data: this.formateResponse(data),
    });
  }
}
