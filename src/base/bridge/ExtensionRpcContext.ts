import { IRPCProtocol, Proxied, ProxyIdentifier } from "shared/lib/bridge/proxyIdentifier";
import { RPCProtocol } from "shared/lib/bridge/rpcProtocol";
import { LoggerManager } from "../logger";
import { IMessagePassingProtocol } from "shared/lib/bridge/ipc";

const LOGGER_SCOPE = "ExtensionRpcContext";

export function createExtensionRpcContext({ logger, protocol }: {
  logger: () => LoggerManager;
  protocol: IMessagePassingProtocol;
}): IRPCProtocol {
  const rpcProtocol = new RPCProtocol(protocol,
    {
      logIncoming(req, initiator, str, data) {
        logger().info(`[rpcIncoming][initiator:${initiator}][req:${req}][${str}]`, LOGGER_SCOPE, {
          value: data,
        });
      },
      logOutgoing(req, initiator, str, data) {
        logger().info(`[rpcOutgoing][initiator:${initiator}][req:${req}][${str}]`, LOGGER_SCOPE, {
          value: data,
        });
      },
    });
  return {
    getProxy: <T>(identifier: ProxyIdentifier<T>): Proxied<T> => rpcProtocol.getProxy(identifier),
    set: <T, R extends T>(identifier: ProxyIdentifier<T>, instance: R): R => rpcProtocol.set(identifier, instance),
    dispose: (): void => rpcProtocol.dispose(),
    assertRegistered: (identifiers: ProxyIdentifier<any>[]): void => rpcProtocol.assertRegistered(identifiers),
    drain: (): Promise<void> => rpcProtocol.drain(),

  };
}
