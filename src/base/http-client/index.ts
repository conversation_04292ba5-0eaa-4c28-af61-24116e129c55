import { BaseModule } from "..";
import { ContextManager } from "../context-manager";
import { ConfigManager, GlobalStateManager } from "../state-manager";
import { Config, GlobalState } from "../state-manager/types";
import { merge } from "lodash";
import { CodeSearchCheckoutRequest,
  CodeSearchGeneratePromptRequest,
  GetCodeSearchCheckoutResponse,
  GetCodeSearchPromptResponse, GetPredictionResponse, HttpClientResponse,
  PlatformConfigResponse, AgentGrayResponse,
  ReportEditFile } from "./interface";
import { LoggerManager } from "../logger";
import { handleGitUrl } from "../../shared/utils/git";
import { fetchEventSource, FetchEventSourceInit } from "./fetch";
import { Doc, Model } from "../../shared/types/business";
import fetch from "node-fetch";
import { PLUGIN_PLATFORM } from "../../log/Model";
import { getProjectInfo } from "../../utils/projectInfo";
import { WebloggerManager } from "../weblogger";

interface HttpClientOptions {
  defaultBaseUrl: string;
  config?: fetch.RequestInit;
  requestInterceptors?: ((config: fetch.RequestInit) => fetch.RequestInit)[];
  responseInterceptors?: ((response: Response) => Response)[];
}

export class HttpClient extends BaseModule {
  private _baseUrl: string = "";
  private defaultBaseUrl = "";
  private _config: fetch.RequestInit = {};
  private requestInterceptors: ((config: fetch.RequestInit) => fetch.RequestInit)[] = [];
  private responseInterceptors: ((response: any) => any)[] = [];

  constructor(ext: ContextManager, options: HttpClientOptions) {
    super(ext);
    this.defaultBaseUrl = options.defaultBaseUrl;
    this._config = options.config || {};
    this.requestInterceptors = options.requestInterceptors || [];
    this.responseInterceptors = options.responseInterceptors || [];
    this.getBase(ConfigManager).watch(
      Config.PROXY_URL,
      (nv?: string) => {
        this._baseUrl = nv || this.defaultBaseUrl;
      },
      { immediate: true },
    );
  }

  async request<R>(url: string, requestInit?: fetch.RequestInit) {
    const response = await this.rawRequest(url, requestInit);

    let responseData = await response.json() as R;

    this.responseInterceptors.forEach((interceptor) => {
      responseData = interceptor(responseData);
    });

    this.logger.debug("api response after interceptor", "http-client", {
      value: responseData,
    });

    return responseData;
  }

  async rawRequest(url: string, requestInit?: fetch.RequestInit) {
    let config = this.mergeConfig(this._config, requestInit);
    config.headers = {
      "Content-Type": "application/json",
      ...config.headers,
    };
    config.method = config.method || "GET";
    let response;
    const u = new URL(url, this._baseUrl);

    this.logger.debug(`api request url: ${u.toString()}`, "http-client", {
      value: {
        url: u.toString(),
        config,
      },
    });
    this.requestInterceptors.forEach((interceptor) => {
      config = interceptor(config);
    });

    this.logger.debug("api request after interceptor", "http-client", {
      value: {
        url: u.toString(),
        config,
      },
    });
    try {
      response = await fetch(u, config);
    }
    catch (error: any) {
      if (error.type === "aborted") {
        throw "user abort";
      }
      this.logger.error(`api request error: ${config.method} ${u.pathname}`, "http-client", {
        value: {
          url: u.toString(),
          config,
        },
        err: error,
      });
      throw new Error(error.toString());
    }

    if (!response.ok) {
      this.logger.error(`api request error: ${config.method} ${u.pathname} ${response.status}`, "http-client", {
        value: {
          url: u.toString(),
          config,
          status: response.status,
          statusText: response.statusText,
        },
        reason: response.timeout ? "timeout" : "an error occurred",
      });
      throw new Error("An error occurred");
    }
    this.logger.info(`api request success: ${config.method} ${u.pathname} ${response.status}`, "http-client", {
      value: {
        url: u.toString(),
        config,
        response,
      },
    });
    return response;
  }

  async get<T, R = HttpClientResponse<T>>(url: string, params: Record<string, string> = {}, config: fetch.RequestInit = {}) {
    const p = this.formatUrl(url, params);
    config.method = "get";
    return this.request<R>(p, config);
  }

  async post<T, R = HttpClientResponse<T>, D extends fetch.BodyInit = any>(url: string, body?: D, config: fetch.RequestInit = {}) {
    config.method = "post";
    config.body = body;
    return this.request<R>(url, config);
  }

  rawFetchEventSource(url: string, options: FetchEventSourceInit) {
    let u = null;
    if (typeof url === "string") {
      u = new URL(url, this._baseUrl);
    }
    this.logger.debug("fetch event source", "http-client", {
      value: options.body,
    });

    return fetchEventSource(u ? u.toString() : url, options);
  }

  private mergeConfig(originConfig: fetch.RequestInit = {}, config: fetch.RequestInit = {}) {
    return merge({}, originConfig, config);
  }

  private formatUrl(url: string, params: Record<string, string>) {
    let isUrl = true;
    const urlObj = new URL(url, "http://example.com");

    try {
      new URL(url);
    }
    catch (error) {
      isUrl = false;
    }

    // 获取现有的搜索参数
    const searchParams = urlObj.searchParams;

    // 遍历新的参数对象，并添加到搜索参数中
    for (const [key, value] of Object.entries(params)) {
      searchParams.append(key, value);
    }

    if (!isUrl) {
      return `${urlObj.pathname}${urlObj.search}`;
    }

    return urlObj.toString();
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  get globalState() {
    return this.getBase(GlobalStateManager);
  }
}

export class Api extends HttpClient {
  constructor(ctx: ContextManager, config: HttpClientOptions) {
    super(ctx, config);
  }

  async getPlatformConfig() {
    const { data } = await this.get<PlatformConfigResponse>("/eapi/kwaipilot/plugin/config");
    return data;
  }

  async getCodeSearchPrompt(body: CodeSearchGeneratePromptRequest) {
    body.repoName = handleGitUrl(body.repoName);
    const { data } = await this.post<GetCodeSearchPromptResponse>("/eapi/kwaipilot/plugin/code/search/generate_prompt", JSON.stringify(body));

    return data;
  }

  async getCodeSearchCheckout(body: CodeSearchCheckoutRequest, signal: AbortSignal) {
    body.repoName = handleGitUrl(body.repoName);
    const { data } = await this.post<GetCodeSearchCheckoutResponse>("/eapi/kwaipilot/plugin/code/search/checkout", JSON.stringify(body), { signal });

    return data;
  }

  async getModelList(username: string) {
    const { data } = await this.get<Model[]>(`/eapi/kwaipilot/plugin/chat_model?username=${username}`);
    return data;
  }

  async getDocList() {
    const { data } = await this.get<Doc[]>("/eapi/kwaipilot/plugin/kwai_knowledge_repo_list");

    return data;
  }

  async getPrediction(cursorOffset: number, history: string[], language: string, id: string, signal: AbortSignal) {
    const body = {
      cursorOffset,
      history,
      language,
      id,
      platform: PLUGIN_PLATFORM,
      username: this.globalState.get(GlobalState.USER_INFO)?.name,
    };
    const { data } = await this.post<GetPredictionResponse | null>("/eapi/kwaipilot/chat/edit/predict", JSON.stringify(body), { signal });
    return data;
    // const mockData: GetPredictionResponse = getMockData();
    // return mockData
  }

  /** 根据用户问题生成对话标题 */
  async summaryConversation(question: string) {
    const body = {
      platform: PLUGIN_PLATFORM,
      userQuestion: question,
      username: this.globalState.get(GlobalState.USER_INFO)?.name,
    };
    return await this.rawRequest("/eapi/kwaipilot/plugin/tool/summaryConversation", {
      body: JSON.stringify(body),
      method: "POST",

    });
  }

  /** 获取推荐问题 */
  async getSuggestQuestion(params: { question: string; answer: string }) {
    const body = {
      ...params,
      username: this.globalState.get(GlobalState.USER_INFO)?.name,
      platform: PLUGIN_PLATFORM,
    };
    const { data } = await this.post<string[]>("/eapi/kwaipilot/plugin/tool/get_suggest_question", JSON.stringify(body));
    return data;
  }

  async reportCost(data: {
    beginTimestamp: number;
    namespace: string;
    stage: string;
    extra1?: string;
    extra2?: string;
  }) {
    const body = {
      beginTimestamp: data.beginTimestamp,
      duration: Date.now() - data.beginTimestamp,
      endTimestamp: Date.now(),
      namespace: data.namespace,
      platform: PLUGIN_PLATFORM,
      stage: data.stage,
      username: this.globalState.get(GlobalState.USER_INFO)?.name,
      extra1: data.extra1,
      extra2: data.extra2,
    };
    return await this.post("/eapi/kwaipilot/log/time", JSON.stringify(body));
  }

  async reportEditFile(
    data: ReportEditFile,
  ) {
    const projectInfo = getProjectInfo();
    const body = {
      platform: PLUGIN_PLATFORM,
      projectName: projectInfo ? projectInfo[0].name : "unknodw project",
      username: this.globalState.get(GlobalState.USER_INFO)?.name,
      ...data,
    };
    // fixme: 本来是通过接口上报的，现在改成weblogger
    this.getBase(WebloggerManager).$reportUserAction({
      key: "code_storage",
      content: JSON.stringify(body),
    });
  }

  fetchEventSource(url: string, options: FetchEventSourceInit): Promise<void> {
    return this.rawFetchEventSource(url, options);
  }

  async checkAgentGray(username: string) {
    return this.get<AgentGrayResponse>("/eapi/kwaipilot/plugin/agent_gray", { username });
  }
}
