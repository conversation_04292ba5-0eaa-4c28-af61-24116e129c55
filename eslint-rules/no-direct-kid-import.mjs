export default {
  meta: {
    type: "problem",
    docs: {
      description:
				"【编码提示】请从 Union/kid 引入 Icon 组件，以确保统一的图标处理可以在ide正确渲染",
      category: "Best Practices",
      recommended: true,
    },
    fixable: "code",
    schema: [],
    messages: {
      noDirectIconifyImport:
				"【编码提示】请从 Union/kid 引入 Icon 组件，以确保统一的图标处理可以在ide正确渲染",
    },
  },
  create(context) {
    return {
      ImportDeclaration(node) {
        // 只检查 webview-ui 下的文件，排除 Union 文件夹
        const filePath = context.getFilename();
        if (!filePath.includes("webview-ui") || filePath.includes("Union")) {
          return;
        }

        // 检查是否从 "@kid/enterprise-icon/icon/output/Icon" 导入
        if (node.source.value === "@kid/enterprise-icon/icon/output/Icon") {
          node.specifiers.forEach((specifier) => {
            if (
              specifier.type === "ImportSpecifier"
              && specifier.imported.name === "Icon"
            ) {
            }
            {
              // 检查导入的组件
              context.report({
                node: specifier,
                messageId: "noDirectIconifyImport",
                fix(fixer) {
                  // 获取所有导入的组件
                  const imports = node.specifiers
                    .filter(s => s.type === "ImportSpecifier")
                    .map(s => s.imported.name);

                  // 分离 Icon 和其他组件
                  const otherComponents = imports.filter(
                    name => name !== "KidIcon",
                  );

                  const fixes = [];

                  // 添加从 Union 导入的 Icon 语句
                  fixes.push(
                    fixer.insertTextBefore(
                      node,
                      `import KidIcon from '@/components/Union/kid';\n`,
                    ),
                  );

                  // 如果还有其他组件，保留原来的导入语句，但移除 Icon
                  if (otherComponents.length > 0) {
                    fixes.push(
                      fixer.replaceText(
                        node,
                        `import { ${otherComponents.join(", ")} } from '@kid/enterprise-icon/icon/output/Icon';\n`,
                      ),
                    );
                  }
                  else {
                    // 如果没有其他组件，删除整个导入语句
                    fixes.push(fixer.remove(node));
                  }

                  return fixes;
                },
              });
            }
          });
        }
      },
    };
  },
};
