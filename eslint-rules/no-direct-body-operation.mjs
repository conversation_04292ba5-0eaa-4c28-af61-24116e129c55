export default {
  meta: {
    type: "problem",
    docs: {
      description: "【编码提示】禁止直接对 document.body 进行操作，因为你的宿主环境可能是IDE内部的主渲染进程",
      category: "Best Practices",
      recommended: true,
    },
    fixable: null,
    schema: [],
    messages: {
      noDirectBodyOperation: "【编码提示】不要直接对 body 进行操作，请使用 getRootContainerId() 对应的元素进行操作。宿主环境可能是IDE内部的主渲染进程，请不要污染他导致IDE发生崩溃",
    },
  },
  create(context) {
    // DOM 操作方法
    const domMethods = [
      // 添加节点
      "append",
      "appendChild",
      "prepend",
      "insertBefore",
      "insertAdjacentElement",
      "insertAdjacentHTML",
      "insertAdjacentText",
      // 删除节点
      "remove",
      "removeChild",
      "replaceChild",
      // 属性操作
      "setAttribute",
      "setAttributeNS",
      "removeAttribute",
      "removeAttributeNS",
      // 内容操作
      "replaceWith",
      "replaceChildren",
      // 克隆操作
      "cloneNode",
    ];

    // classList 操作
    const classListMethods = ["add", "remove", "toggle", "replace", "contains"];

    // style 操作
    const styleMethods = [
      "setProperty",
      "removeProperty",
      "getPropertyValue",
      "getPropertyPriority",
    ];

    // 需要忽略的框架相关代码
    const ignorePatterns = ["react-dom", "react", "next", "vite", "webpack"];

    return {
      // 检查直接方法调用
      MemberExpression(node) {
        // 只检查 webview-ui 下的文件
        const filePath = context.getFilename();
        if (!filePath.includes("webview-ui")) {
          return;
        }

        // 检查是否是 document.body 的调用
        if (
          node.object.type === "MemberExpression"
          && node.object.object.name === "document"
          && node.object.property.name === "body"
        ) {
          // 检查是否是 classList 操作
          if (node.property.name === "classList") {
            // 获取父节点，检查是否是对 classList 的方法调用
            const parent = node.parent;
            if (
              parent
              && parent.type === "MemberExpression"
              && parent.object === node
            ) {
              const methodName = parent.property.name;
              if (classListMethods.includes(methodName)) {
                // 检查是否在需要忽略的框架代码中
                const filename = context.getFilename();
                const shouldIgnore = ignorePatterns.some(pattern =>
                  filename.includes(pattern),
                );

                if (!shouldIgnore) {
                  context.report({
                    node: parent,
                    messageId: "noDirectBodyOperation",
                  });
                }
              }
            }
          }
          // 检查是否是 style 操作
          else if (node.property.name === "style") {
            // 获取父节点，检查是否是对 style 的方法调用
            const parent = node.parent;
            if (
              parent
              && parent.type === "MemberExpression"
              && parent.object === node
            ) {
              const methodName = parent.property.name;
              if (styleMethods.includes(methodName)) {
                // 检查是否在需要忽略的框架代码中
                const filename = context.getFilename();
                const shouldIgnore = ignorePatterns.some(pattern =>
                  filename.includes(pattern),
                );

                if (!shouldIgnore) {
                  context.report({
                    node: parent,
                    messageId: "noDirectBodyOperation",
                  });
                }
              }
            }
          }
          // 检查其他 DOM 方法调用
          else {
            const methodName = node.property.name;
            if (domMethods.includes(methodName)) {
              // 检查是否在需要忽略的框架代码中
              const filename = context.getFilename();
              const shouldIgnore = ignorePatterns.some(pattern =>
                filename.includes(pattern),
              );

              if (!shouldIgnore) {
                context.report({
                  node,
                  messageId: "noDirectBodyOperation",
                });
              }
            }
          }
        }
      },
    };
  },
};
