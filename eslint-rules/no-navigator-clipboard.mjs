export default {
  meta: {
    type: "problem",
    docs: {
      description: "【编码提示】禁止使用 navigator.clipboard.writeText，因为它在某些环境可能不可用或受到浏览器权限限制",
      category: "Best Practices",
      recommended: true,
    },
    fixable: null,
    schema: [],
    messages: {
      noNavigatorClipboard: "【编码提示】请不要直接使用 navigator.clipboard.writeText，应使用 kwaiPilotBridgeAPI.copyToClipboard",
    },
  },
  create(context) {
    /**
     * 检查是否是 navigator.clipboard.writeText
     * @param {Object} node - AST 节点
     * @returns {boolean} 是否匹配模式
     */
    function isNavigatorClipboardWriteText(node) {
      return (
        node
        && node.type === "MemberExpression"
        && node.property
        && node.property.type === "Identifier"
        && node.property.name === "writeText"
        && node.object
        && node.object.type === "MemberExpression"
        && node.object.property
        && node.object.property.type === "Identifier"
        && node.object.property.name === "clipboard"
        && node.object.object
        && node.object.object.type === "Identifier"
        && node.object.object.name === "navigator"
      );
    }

    return {
      // 检查普通的成员表达式（如直接调用 navigator.clipboard.writeText）
      MemberExpression(node) {
        if (isNavigatorClipboardWriteText(node)) {
          context.report({
            node,
            messageId: "noNavigatorClipboard",
          });
        }
      },

      // 检查在回调函数中的使用（如 onClick 事件处理）
      "ArrowFunctionExpression, FunctionExpression"(node) {
        if (node.body && node.body.type === "BlockStatement") {
          // 遍历函数体内的语句
          node.body.body.forEach((statement) => {
            if (
              statement.type === "ExpressionStatement"
              && statement.expression
              && statement.expression.type === "CallExpression"
              && isNavigatorClipboardWriteText(statement.expression.callee)
            ) {
              context.report({
                node: statement.expression.callee,
                messageId: "noNavigatorClipboard",
              });
            }
          });
        }
        else if (
          node.body
          && node.body.type === "CallExpression"
          && isNavigatorClipboardWriteText(node.body.callee)
        ) {
          // 处理箭头函数的简写形式 () => navigator.clipboard.writeText()
          context.report({
            node: node.body.callee,
            messageId: "noNavigatorClipboard",
          });
        }
      },

      // 检查方法调用（如 navigator.clipboard.writeText()）
      CallExpression(node) {
        if (isNavigatorClipboardWriteText(node.callee)) {
          context.report({
            node: node.callee,
            messageId: "noNavigatorClipboard",
          });
        }
      },
    };
  },
};
