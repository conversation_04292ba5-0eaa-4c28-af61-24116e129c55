export default {
  meta: {
    type: "problem",
    docs: {
      description: "【编码提示】禁止直接对 document 对象进行操作，应使用 DOM 工具类中的方法替代",
      category: "Best Practices",
      recommended: true,
    },
    fixable: null,
    schema: [],
    messages: {
      noDirectDocumentOperation: "【编码提示】不要直接对 document 进行操作，请使用 DOM 下的方法进行替代。宿主环境可能是IDE内部的主渲染进程，请使用安全封装的方法避免污染环境",
    },
  },
  create(context) {
    // 需要忽略的框架相关代码和路径
    const ignorePatterns = ["react-dom", "react", "next", "vite", "webpack", "node_modules", "Union"];

    // 允许例外的情况（在 DOM 工具类中必须允许使用 document）
    const allowedFilePatterns = ["utils/dom.ts"];

    return {
      // 捕获所有对 document 的引用
      Identifier(node) {
        // 只有当标识符是 'document' 时才进行处理
        if (node.name !== "document") {
          return;
        }

        const filePath = context.getFilename();

        // 过滤非 webview-ui 目录的文件
        if (!filePath.includes("webview-ui")) {
          return;
        }

        // 检查是否是在 DOM 工具类文件中，这些文件允许使用 document
        const isInAllowedFile = allowedFilePatterns.some(pattern =>
          filePath.includes(pattern),
        );
        if (isInAllowedFile) {
          return;
        }

        // 检查是否在需要忽略的框架代码中
        const shouldIgnore = ignorePatterns.some(pattern =>
          filePath.includes(pattern),
        );
        if (shouldIgnore) {
          return;
        }

        // 检查是否是对 document 的引用，而不是其他上下文（如变量声明、参数等）
        const parent = node.parent;

        // 如果是 MemberExpression 且 document 是对象（如 document.body）
        if (parent && parent.type === "MemberExpression" && parent.object === node) {
          context.report({
            node,
            messageId: "noDirectDocumentOperation",
          });
          return;
        }

        // 如果是直接调用 document 的情况（如 document()）
        if (parent && parent.type === "CallExpression" && parent.callee === node) {
          context.report({
            node,
            messageId: "noDirectDocumentOperation",
          });
          return;
        }

        // 其他可能的使用场景，如 window.document
        if (
          parent
          && parent.type === "MemberExpression"
          && parent.property === node
          && parent.object.type === "Identifier"
          && (parent.object.name === "window" || parent.object.name === "globalThis")
        ) {
          context.report({
            node,
            messageId: "noDirectDocumentOperation",
          });
          return;
        }
      },
    };
  },
};
