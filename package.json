{"name": "kwai<PERSON>lot", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "description": "一款为快手程序员量身打造的、更懂快手代码的代码大模型，集合前沿技术，为您的编程工作提供支持，成为您编程路上的得力助手！", "version": "9.4.7", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "icon": "assets/images/kwaipilot-logo1.png", "homepage": "https://kwaipilot.corp.kuaishou.com/", "repository": "https://kwaipilot.corp.kuaishou.com/", "keywords": ["code-suggestion", "copilot", "code-inference"], "engines": {"vscode": "^1.77.0"}, "bugs": {}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://kwaipilot.corp.kuaishou.com/"}, "contributors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "license": "<PERSON><PERSON><PERSON><PERSON>", "categories": ["Other"], "activationEvents": ["onStartupFinished", "onUri"], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "kwaipilot_sidebar", "title": "<PERSON><PERSON><PERSON><PERSON>", "icon": "assets/images/logo-dark.svg"}]}, "views": {"kwaipilot_sidebar": [{"type": "webview", "id": "kwaiPilotChatWebView", "name": ""}]}, "commands": [{"command": "kwaipilot.showInlineCompletion", "title": "Kwaipilot：触发内联补全"}, {"command": "kwaipilot.newComposer", "title": "打开代码智能体"}, {"command": "kwaipilot.inlineChat", "title": "Kwaipilot：对话式生成代码", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.addToComposerContext", "title": "将选中代码行添加至\"知识\"", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.generateCommentMessage", "title": "Kwaipilot：生成代码注释", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.generateUnitTest", "title": "Kwaipilot：生成单元测试", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.explain", "title": "Kwaipilot：解释代码", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.home", "title": "前往问答引擎", "category": "kwai<PERSON>lot", "icon": {"dark": "assets/images/home-dark.svg", "light": "assets/images/home-light.svg"}}, {"command": "kwaipilot.Open Kwaipilot Panel", "title": "Kwaipilot：智能编程助手", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.feedback", "title": "用户反馈", "category": "kwai<PERSON>lot", "icon": {"dark": "assets/images/zmn-dark.svg", "light": "assets/images/zmn-light.svg"}}, {"command": "kwaipilot.help", "title": "帮助文档", "category": "kwai<PERSON>lot", "icon": {"dark": "assets/images/help-dark.svg", "light": "assets/images/help-light.svg"}}, {"command": "kwaipilot.settings", "title": "设置", "category": "kwai<PERSON>lot", "icon": {"dark": "assets/images/settings-dark.svg", "light": "assets/images/settings-light.svg"}}, {"command": "kwaipilot.login", "title": "登录", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.logout", "title": "登出", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.reloadWebview", "title": "刷新 webview", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.quickAsk", "title": "Kwaipilot：快速问答", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.showDiff", "title": "Kwaipilot：显示代码块", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.fileDiff", "title": "Kwaipilot：单文件中显示文件差异", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.addDataAnnotation", "title": "Kwaipilot：数据增强标注", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.handleEscape", "title": "Kwaipilot：处理Esc键"}, {"command": "kwaipilot.handleTab", "title": "Kwaipilot：处理Tab键"}, {"command": "kwaipilot.openCodeIndexManagement", "title": "Kwaipilot：代码索引管理", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.openRulesManagement", "title": "Kwaipilot：规则管理", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.openBasicsManagement", "title": "Kwaipilot：基础配置", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.openFunctionManagement", "title": "Kwaipilot：功能配置", "category": "kwai<PERSON>lot"}, {"command": "kwaiPilot.reportBadCase", "title": "Kwaipilot：上报badcase", "category": "kwai<PERSON>lot"}, {"command": "kwaipilot.acceptDiff", "title": "接受更改", "category": "kwai<PERSON>lot", "icon": "$(check)"}, {"command": "kwaipilot.rejectDiff", "title": "拒绝更改", "category": "kwai<PERSON>lot", "icon": "$(close)"}, {"command": "kwaipilot.nextDiffFile", "title": "下一个差异文件", "category": "kwai<PERSON>lot", "icon": "$(arrow-right)"}, {"command": "kwaipilot.previousDiffFile", "title": "上一个差异文件", "category": "kwai<PERSON>lot", "icon": "$(arrow-left)"}, {"command": "kwaipilot.lineAccept", "title": "接受续写当前行", "category": "kwai<PERSON>lot"}], "submenus": [{"id": "kwaipilot_submenus", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "kwaipilot_submenus_files", "label": "<PERSON><PERSON><PERSON><PERSON>"}], "menus": {"editor/context": [{"submenu": "kwaipilot_submenus", "group": "navigation"}], "explorer/context": [{"submenu": "kwaipilot_submenus_files", "group": "navigation"}], "kwaipilot_submenus_files": [{"command": "kwaipilot.quickAsk", "group": "group1@1"}], "kwaipilot_submenus": [{"command": "kwaipilot.addToComposerContext", "group": "group1@1"}, {"command": "kwaipilot.inlineChat", "group": "group1@2"}, {"command": "kwaipilot.generateCommentMessage", "group": "group2@1", "when": "editorHasSelection"}, {"command": "kwaipilot.generateUnitTest", "group": "group2@2", "when": "editorHasSelection"}, {"command": "kwaipilot.addDataAnnotation", "when": "kwaipilot.canDataAnnotation", "group": "group2@3"}], "view/title": [{"command": "kwaipilot.feedback", "when": "view == kwaiPilotChatWebView", "group": "navigation@2"}, {"command": "kwaipilot.help", "when": "view == kwaiPilotChatWebView", "group": "navigation@3"}, {"command": "kwaipilot.settings", "when": "view == kwaiPilotChatWebView", "group": "navigation@4"}, {"command": "kwaipilot.login", "when": "view == kwaiPilotChatWebView && kwaipilot.isLogin !== true", "group": "navigation@5"}, {"command": "kwaipilot.logout", "when": "view == kwaiPilotChatWebView && kwaipilot.isLogin === true", "group": "navigation@6"}, {"command": "kwaipilot.reloadWebview", "when": "view == kwaiPilotChatWebView && kwaipilot.isDeveloperMode === true", "group": "navigation@7"}], "editor/title": [{"command": "kwaipilot.previousDiffFile", "group": "navigation", "when": "kwaipilot.inDiffMode && kwaipilot.hasMutipleDiffFiles"}, {"command": "kwaipilot.nextDiffFile", "group": "navigation", "when": "kwaipilot.inDiffMode && kwaipilot.hasMutipleDiffFiles"}, {"command": "kwaipilot.acceptDiff", "group": "navigation@1", "when": "kwaipilot.inDiffMode"}, {"command": "kwaipilot.rejectDiff", "group": "navigation@1", "when": "kwaipilot.inDiffMode"}]}, "keybindings": [{"command": "kwaipilot.handleEscape", "key": "escape", "when": "editorTextFocus && kwaipilot.prediction.isVisible"}, {"command": "kwaipilot.handleTab", "key": "tab", "when": "editorTextFocus && kwaipilot.prediction.isVisible"}, {"command": "kwaipilot.showInlineCompletion", "key": "alt+p", "mac": "alt+p", "when": "editorTextFocus"}, {"command": "kwaipilot.inlineChat", "key": "cmd+i", "mac": "cmd+i", "when": "editorTextFocus"}, {"command": "kwaipilot.addToComposerContext", "key": "cmd+l", "mac": "cmd+l", "when": "editorTextFocus"}, {"command": "kwaipilot.acceptDiff", "mac": "shift+cmd+enter", "key": "shift+ctrl+enter"}, {"command": "kwaipilot.rejectDiff", "mac": "cmd+z", "key": "ctrl+z", "when": "kwaipilot.diffVisible"}, {"command": "kwaipilot.lineAccept", "key": "ctrl+down", "mac": "cmd+down", "when": "editorTextFocus && inlineSuggestionVisible"}, {"command": "kwaipilot.rejectDiff", "mac": "shift+cmd+backspace", "key": "shift+ctrl+backspace"}, {"command": "kwaipilot.acceptVerticalDiffBlock", "mac": "cmd+y", "key": "ctrl+y", "when": "kwaipilot.diffVisible"}, {"command": "kwaipilot.rejectVerticalDiffBlock", "mac": "cmd+n", "key": "ctrl+n", "when": "kwaipilot.diffVisible"}, {"command": "kwaipilot.generateCommentMessage", "key": "alt+ctrl+m", "mac": "ctrl+cmd+m", "when": "editor<PERSON><PERSON><PERSON>"}, {"command": "kwaipilot.generateUnitTest", "key": "alt+ctrl+t", "mac": "ctrl+cmd+t", "when": "editor<PERSON><PERSON><PERSON>"}, {"command": "kwaipilot.addDataAnnotation", "key": "ctrl+m", "mac": "cmd+m", "when": "editor<PERSON><PERSON><PERSON>"}, {"command": "kwaipilot.Open Kwaipilot Panel", "key": "alt+ctrl+k", "mac": "ctrl+cmd+k"}, {"command": "kwaiPilot.reportBadCase", "key": "ctrl+u", "mac": "cmd+u", "when": "editor<PERSON><PERSON><PERSON>"}]}, "scripts": {"webview:dev": "pnpm -r --parallel --filter webview-ui --filter setting-ui dev --force", "install:all": "node scripts/install.js", "build:webview": "pnpm --filter webview-ui build && pnpm --filter setting-ui build", "vscode:prepublish": "pnpm run compile", "compile": "tsc --build && tsup", "watch-tsc": "tsc --build --watch", "watch-tsup": "tsup --watch --sourcemap true", "type-check": "tsc --build && tsc --noEmit", "webview:type-check": "pnpm --filter webview-ui type-check && pnpm --filter setting-ui type-check", "pretest": "pnpm run compile && pnpm run lint", "lint": "pnpm eslint src webview-ui/src --quiet && pnpm --filter webview-ui lint:style", "build": "pnpm run lint && node scripts/build.js", "build:pre": "node scripts/build.js --pre-release", "compile:export": "tsc --build && tsup --config tsup-export.config.js", "compile:export:watch": "tsc --build && tsup --config tsup-export.config.js --watch", "build:export": "node scripts/build-export.js", "build:export:pre": "node scripts/build-export.js --pre-release", "publish": "node scripts/publish.js", "publish:pre": "node scripts/publish.js --pre-release", "pkgls": "vsce ls --no-dependencies", "transform:default-theme": "node scripts/transform-default-theme.js"}, "devDependencies": {"@eslint/compat": "^1.2.4", "@eslint/js": "^9.17.0", "@stylistic/eslint-plugin": "^2.12.1", "@types/crypto-js": "^4.2.2", "@types/diff": "^5.2.1", "@types/glob": "^7.2.0", "@types/lodash": "^4.17.7", "@types/minimist": "^1.2.5", "@types/node": "^18.19.80", "@types/node-fetch": "^2.6.11", "@types/react": "^18.2.41", "@types/uuid": "^9.0.8", "@types/vscode": "1.77.0", "delay": "^6.0.0", "diff": "^7.0.0", "eslint": "^9.17.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0", "glob": "^7.2.3", "globals": "^15.13.0", "globby": "^14.1.0", "immer": "^10.1.1", "isbinaryfile": "^5.0.4", "lexical": "^0.17.1", "mammoth": "^1.9.0", "minimist": "^1.2.8", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "prettier": "^3.4.2", "rxjs": "^7.8.1", "tsup": "^8.2.4", "typescript": "^5.5.4", "typescript-eslint": "^8.18.1", "vsce": "^2.15.0", "vscode-test": "^1.6.1"}, "dependencies": {"shared": "workspace:*", "@fortaine/fetch-event-source": "^3.0.6", "@ks/weblogger": "^3.10.41", "@types/async-lock": "^1.4.2", "@vue/compiler-sfc": "^3.4.38", "async-lock": "^1.4.1", "axios": "^1.7.7", "bindings": "^1.5.0", "chokidar": "^3.6.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "diff": "^7.0.0", "fast-deep-equal": "^3.1.3", "fastest-levenshtein": "^1.0.16", "file-type": "^16.5.4", "formdata-node": "^6.0.3", "formdata-polyfill": "^4.0.10", "lodash": "^4.17.21", "lru-cache": "9.1.2", "node-fetch": "^2.7.0", "p-wait-for": "^5.0.2", "pg-hstore": "^2.3.4", "sequelize": "^6.37.4", "sqlite3": "^5.1.7", "uuid": "^9.0.1", "web-streams-polyfill": "^4.0.0", "winston": "^3.14.2", "winston-daily-rotate-file": "^5.0.0", "winston-transport": "^4.7.1"}, "pnpm": {"overrides": {"mdast-util-gfm-table": "^2.0.0", "mdast-util-gfm-strikethrough": "^2.0.0", "mdast-util-gfm-footnote": "~2.0.0", "mdast-util-from-markdown": "^2.0.0", "mdast-util-to-markdown": "^2.0.0", "mdast-util-gfm-task-list-item": "^2.0.0", "micromark-extension-gfm-footnote": "^2.0.0", "micromark-extension-gfm-strikethrough": "^2.0.0", "micromark-extension-gfm-table": "^2.0.0", "micromark-extension-gfm-task-list-item": "^2.0.0", "micromark-util-combine-extensions": "^2.0.0", "remark-math": "^6.0.0"}, "patchedDependencies": {"micromark-util-combine-extensions": "webview-ui/patches/micromark-util-combine-extensions.patch", "bindings": "patches/bindings.patch", "nunjucks": "patches/nunjucks.patch"}}}