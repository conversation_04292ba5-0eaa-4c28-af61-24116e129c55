{"name": "shared", "version": "1.0.0", "description": "", "main": "lib/index.js", "types": "lib/index.d.ts", "type": "module", "files": ["lib"], "exports": {".": {"import": "./lib/index.js", "types": "./lib/index.d.ts"}, "./lib/*": {"import": ["./lib/*.js", "./lib/*/index.js"], "types": ["./lib/*.d.ts", "./lib/*/index.d.ts"]}, "./lib/business": {"import": "./lib/business/index.js", "types": "./lib/business/index.d.ts"}, "./lib/MentionNode": {"import": "./lib/MentionNode/index.js", "types": "./lib/MentionNode/index.d.ts"}, "./lib/agent": {"import": "./lib/agent/index.js", "types": "./lib/agent/index.d.ts"}, "./lib/CustomVariable": {"import": "./lib/CustomVariable/index.js", "types": "./lib/CustomVariable/index.d.ts"}, "./lib/util": {"import": "./lib/util/index.js", "types": "./lib/util/index.d.ts"}, "./lib/const": {"import": "./lib/const/index.js", "types": "./lib/const/index.d.ts"}, "./lib/bridge": {"import": "./lib/bridge/index.js", "types": "./lib/bridge/index.d.ts"}}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc -p tsconfig.lib.json --composite false"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@tsconfig/recommended": "^1.0.8", "@types/lodash-es": "^4.17.12", "@types/node": "^12.20.55", "fast-deep-equal": "^3.1.3", "lodash-es": "^4.17.21", "rxjs": "^7.8.1", "typescript": "^5.5.4", "vitest": "^3.0.2"}, "dependencies": {"vscode-uri": "^3.1.0"}}