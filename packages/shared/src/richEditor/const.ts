import { CommandType, SharpCommand, SlashCommand } from "../misc";
import { CustomPromptData } from "../CustomVariable";

export enum SpecialMenu {
  DIR_AND_FILE = "DIR_AND_FILE",
}

export const SpecialRichMenu: Record<SpecialMenu, RichEditorBoxPanelData> = {
  [SpecialMenu.DIR_AND_FILE]: {
    title: "目录/文件",
    key: SpecialMenu.DIR_AND_FILE,
    description: "",
    type: "normal",
    data: SpecialMenu.DIR_AND_FILE,
    commandType: CommandType.SHARP,
    uri: "",
  },
};

export type RichEditorBoxPanelData = RichEditorBoxPanelDataCommon | RichEditorBoxPanelDataSubmenu | RichEditorBoxPanelDataCustomPrompt;
export interface RichEditorBoxPanelDataCommon {
  title: string;
  /** 自定义渲染 title 后面的内容 */
  titleSuffix?: string | JSX.Element;
  /** 这个用标识是什么 文件目录还是
   *
   * 2025.1.22 补充了类型, 看起来是为了区分每个菜单项的类别(文件\目录\特殊菜单), 注意对于父菜单项(例如[文件]的入口), 也会使用这个字段
   */
  key: SharpCommand | SlashCommand | SpecialMenu;
  /** 文件/目录的完整路径 */
  description: string;
  type: "normal";
  data: string;
  uri: string;
  commandType: CommandType;
  search?: string[];
}

/**
 * <AUTHOR>
 * @date 2025-01-22
 * 子菜单的类型
 * 将不同的子菜单的配置写到 RichEditorBoxPanelData 配置中, 进而将子菜单切换\加载\搜索等逻辑收敛到 NewPanel 组件内部
 */
export enum RichEditorMenuType {
  /**
   * 文件列表
   */
  FILE = "FILE",
  /** 目录列表 */
  FOLDER = "FOLDER",
  /** 自定义提示词列表 */
  CUSTOM_PROMPT = "CUSTOM_PROMPT",
  /** # 指令一级菜单 */
  SHARP_COMMAND = "SHARP_COMMAND",
  /** / 指令一级菜单 */
  SLASH_COMMAND = "SLASH_COMMAND",
  /** 规则列表 */
  RULES = "RULES",
}

export interface RichEditorBoxPanelDataSubmenu extends Omit<RichEditorBoxPanelDataCommon, "type"> {
  type: "submenu";
  submenu: RichEditorMenuType;
}

export type RichEditorBoxPanelDataCustomPrompt = Omit<RichEditorBoxPanelDataCommon, "type"> & {
  type: "customPrompt";
  raw: CustomPromptData;
};
