import { type SerializedLexicalNode, type Spread } from "lexical";
import { URI } from "vscode-uri";
import {
  type CustomVariableItem,
  type CustomVariableItemFile,
  type CustomVariableItemRepository,
  type CustomVariableItemSource,
  type CustomVariableItemSymbol,
  CustomVariableItemSelection,
  CustomVariableItemTree,
  CustomVariableUserDefined,
  CustomVariableItemLanguage,
} from "./messages";
import { displayPathBasename } from "./cody-shared/displayPath";
import {
  displayLineRange,
  doRangesIntersect,
  isRangeContained,
  isRangeProperSubset,
  mergeRanges,
} from "./cody-shared/range";
import _ from "lodash";

export const CONTEXT_ITEM_MENTION_NODE_TYPE = "customVariable";
export const TEMPLATE_INPUT_NODE_TYPE = "templateInput";

/**
 * The subset of {@link CustomVariableItem} fields that we need to store to identify and display context
 * item mentions.
 */
export type SerializedCustomVariableItem = {
  uri: string;
  content?: string | null;
  source?: CustomVariableItemSource;
} & (
  | Omit<CustomVariableItemFile, "uri" | "content" | "source">
  | Omit<CustomVariableItemRepository, "uri" | "content" | "source">
  | Omit<CustomVariableItemTree, "uri" | "content" | "source">
  | Omit<CustomVariableItemSymbol, "uri" | "content" | "source">
  | Omit<CustomVariableItemSelection, "uri" | "content" | "source">
  | Omit<CustomVariableItemLanguage, "uri" | "content" | "source">
  | Omit<CustomVariableUserDefined, "uri" | "content" | "source">
);

export type SerializedTemplateInput = {
  // TODO should these be PromptStrings?
  placeholder: string;
};

export type SerializedCustomVariableNode = Spread<
  {
    type: typeof CONTEXT_ITEM_MENTION_NODE_TYPE;
    contextItem: SerializedCustomVariableItem;
    isFromInitialContext: boolean;
    text: string;
  },
  SerializedLexicalNode
>;

export type SerializedTemplateInputNode = Spread<
  {
    type: typeof TEMPLATE_INPUT_NODE_TYPE;
    templateInput: SerializedTemplateInput;
  },
  SerializedLexicalNode
>;

export function serializeCustomVariableNode(
  contextItem: CustomVariableItem | SerializedCustomVariableItem,
): SerializedCustomVariableItem {
  const uri = contextItem.uri;
  const uriString
        = uri instanceof Object
          ? URI.from({
              scheme: uri.scheme,
              authority: uri.authority,
              path: uri.path,
              query: uri.query,
              fragment: uri.fragment,
            }).toString()
          : uri;

  // Make sure we only bring over the fields on the context item that we need, or else we
  // could accidentally include tons of data (including the entire contents of files).
  return {
    ...contextItem,
    uri: uriString,

    // Don't include the `content` (if it's present) because it's quite large, and we don't need
    // to serialize it here. It can be hydrated on demand.
    // content: undefined,
    // 0219：Cody中contextItem 传递比较复杂，这里简化一下，直接保存 content
    content: contextItem.content,
  };
}

export function deserializeContextItem(contextItem: SerializedCustomVariableItem): CustomVariableItem {
  return { ...contextItem, uri: URI.parse(contextItem.uri) } as CustomVariableItem;
}

export function isSerializedContextItemMentionNode(
  node: SerializedLexicalNode | null | undefined,
): node is SerializedCustomVariableNode {
  return Boolean(node && node.type === CONTEXT_ITEM_MENTION_NODE_TYPE);
}

export function isSerializedTemplateInputNode(
  node: SerializedLexicalNode | null | undefined,
): node is SerializedTemplateInputNode {
  return Boolean(node && node.type === TEMPLATE_INPUT_NODE_TYPE);
}

type Operation = "modify" | "create" | "delete" | "keep";

interface OperationResult {
  item: SerializedCustomVariableItem;
  operation: Operation;
  update?: SerializedCustomVariableItem;
}

interface Operations {
  modify: Map<SerializedCustomVariableItem, SerializedCustomVariableItem>;
  delete: Set<SerializedCustomVariableItem>;
  create: SerializedCustomVariableItem[];
}

export function getMentionOperations(
  existing: SerializedCustomVariableItem[],
  toAdd: SerializedCustomVariableItem[],
): Operations {
  const groups = Array.from(
    new Set([
      ...existing.map(e => `${e.uri}|${e.source}`),
      ...toAdd.map(a => `${a.uri}|${a.source}`),
    ]),
  );

  // Process each URI+source separately
  const results = groups.flatMap((group) => {
    const [uri, source] = group.split("|");
    const existingForGroup = existing.filter(e => e.uri === uri && e.source === source);
    const toAddForGroup = toAdd.filter(e => e.uri === uri && e.source === source);

    return processGroupedMentions(existingForGroup, toAddForGroup);
  });

  return {
    modify: results.reduce((m, r) => {
      if (r.operation === "modify" && r.update) {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        m.set(r.item, r.update!);
      }
      return m;
    }, new Map()),
    delete: new Set(_(results).filter({ operation: "delete" }).map("item").value()),
    create: _(results).filter({ operation: "create" }).map("item").value(),
  };
}

// Given a set of existing and new mentions for the same document, determine the set
// of operations to update the state (e.g. add new mentions, or delete or modify existing ones)
function processGroupedMentions(
  existing: SerializedCustomVariableItem[],
  toAdd: SerializedCustomVariableItem[],
): OperationResult[] {
  // If existing document has full coverage, keep it and skip
  const existingFullDocumentCoverage = existing.find(item => !item.range);
  if (existingFullDocumentCoverage) {
    return [];
  }
  // Check if any new item covers the entire document
  const fullDocumentCoverage = toAdd.find(item => !item.range);
  if (fullDocumentCoverage) {
    // Mark all existing items for deletion and create one new item
    return [
      ...existing.map(item => ({ item, operation: "delete" as Operation })),
      { item: fullDocumentCoverage, operation: "create" as Operation },
    ];
  }

  return processExistingMentions(existing, toAdd).concat(processNewMentions(existing, toAdd));
}

// Given a set of existing and new mentions for the same document, determine the set
// of operations to update the existing mentions (e.g. deleting or modifying the range)
function processExistingMentions(
  existing: SerializedCustomVariableItem[],
  toAdd: SerializedCustomVariableItem[],
): OperationResult[] {
  return existing.map((existingItem) => {
    for (const newItem of toAdd) {
      // Just to satisfy the compiler, this was already checked in the caller
      if (!existingItem.range || !newItem.range) continue;

      if (isRangeProperSubset(existingItem.range, newItem.range)) {
        return { item: existingItem, operation: "delete" };
      }

      // If the new item has a meaningful intersection with the existing item
      // (meaning it is not fully contained in the existing item), merge the two
      if (
        doRangesIntersect(existingItem.range, newItem.range)
        && !isRangeContained(newItem.range, existingItem.range)
      ) {
        return {
          operation: "modify",
          item: existingItem,
          update: mergeContextItems(existingItem, newItem),
        };
      }
    }

    // If no new item overlaps with the existing item, keep it
    return {
      item: existingItem,
      operation: "keep",
    };
  });
}

// Given a set of existing and new mentions for the same document, determine which
// new mentions should be added due to non-overlapping ranges
function processNewMentions(
  existing: SerializedCustomVariableItem[],
  toAdd: SerializedCustomVariableItem[],
): OperationResult[] {
  return toAdd
    .filter(newItem =>
      existing.every(
        existingItem =>
        // These are just to satisfy the compiler, they were already checked in the caller
          existingItem.range
          && newItem.range
          // if the new item is partially contained in an existing item,
          // we won't need to add it as it will have been handled by a modify operation
          // but if it completely subsumes an existing item, we need to add it
          // as the existing item will be deleted
          && (!doRangesIntersect(newItem.range, existingItem.range)
            || isRangeProperSubset(existingItem.range, newItem.range)),
      ),
    )
    .map(item => ({
      item,
      operation: "create",
    }));
}

function mergeContextItems(a: SerializedCustomVariableItem, b: SerializedCustomVariableItem): SerializedCustomVariableItem {
  // If one of the ranges is undefined, then it references the entire item
  // so we can just return that item
  if (!a.range || !b.range) {
    return a.range ? b : a;
  }

  return {
    ...a,
    range: mergeRanges(a.range, b.range),
  };
}

export function customVariableItemMentionNodeDisplayText(contextItem: SerializedCustomVariableItem): string {
  // A displayed range of `foo.txt:2-4` means "include all of lines 2, 3, and 4", which means the
  // range needs to go to the start (0th character) of line 5. Also, `RangeData` is 0-indexed but
  // display ranges are 1-indexed.
  const rangeText = contextItem.range?.start ? `:${displayLineRange(contextItem.range)}` : "";
  switch (contextItem.type) {
    case "file":
      return `${decodeURIComponent(displayPathBasename(URI.parse(contextItem.uri)))}` || "(empty)";
    case "selection":
      return `${decodeURIComponent(displayPathBasename(URI.parse(contextItem.uri)))}${rangeText}` || "(empty)";

    case "repository":
      return trimCommonRepoNamePrefixes(contextItem.repoName) || "unknown repository";

    case "tree":
      return contextItem.uri || "(empty)";

    case "symbol":
      return contextItem.symbolName;
    case "language":
      return contextItem.language;
    case "userDefined":
      return contextItem.variableValue || contextItem.variableName;
    default: {
      const neverType = contextItem;
      console.warn("neverType", neverType);
      return "(empty)";
    }
  }
}

export function templateInputNodeDisplayText(templateInput: SerializedTemplateInputNode): string {
  return templateInput.templateInput.placeholder;
}

function trimCommonRepoNamePrefixes(repoName: string): string {
  return repoName.replace(/^(github|gitlab)\.com\//, "");
}

export function assemblePlainTextForGpt(node: SerializedCustomVariableNode): string {
  const transformers: {
    [key in SerializedCustomVariableItem["type"]]: (node: Extract<SerializedCustomVariableItem, { type: key }>) => string;
  } = {
    file: node => `\`\`\`file:${node.uri}\n${node.content}\n\`\`\``,
    selection: node => `\`\`\`selection:${customVariableItemMentionNodeDisplayText(node)}\n${node.content}\n\`\`\``,
    userDefined: node => node.variableValue || node.variableName,
    symbol: node => node.symbolName,
    repository: node => node.repoName,
    tree: node => node.uri,
    language: node => node.language,
  };

  return transformers[node.contextItem.type](node.contextItem as any);
}
