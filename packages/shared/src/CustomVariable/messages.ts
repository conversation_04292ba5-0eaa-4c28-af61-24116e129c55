import { type URI } from "vscode-uri";

import { RangeData } from "./cody-shared/range";

export type ContextFileType = "file" | "symbol";

/**
 * Fields that are common to any context item included in chat messages.
 */
interface CustomVariableItemCommon {
  // TODO: URI 有必要吗
  /**
     * The URI of the document (such as a file) where this context resides.
     */
  uri: URI;

  /**
     * If only a subset of a file is included as context, the range of that subset.
     */
  range?: RangeData;

  /**
     * The content, either the entire document or the range subset.
     */
  content?: string | null;

  repoName?: string;
  revision?: string;

  /**
     * The description of the context item used to display in mentions menu.
     */
  description?: string;

  /**
     * Whether the item is excluded by <PERSON>re.
     */
  isIgnored?: boolean;

  /**
     * Optional metadata about where this context item came from or how it was scored, which
     * can help a user or dev working on Cody understand why this item is appearing in context.
     */
  metadata?: string[];

  /**
     * Optional badge to display with the context item.
     */
  badge?: string;
}

/**
 * The source of this context.
 */
export enum CustomVariableItemSource {
  /** Explicitly @-mentioned by the user in chat */
  User = "user",

  /** From the current editor state and open tabs/documents */
  Editor = "editor",

  /** From symf search */
  Search = "search",

  /** In initial context */
  Initial = "initial",

  /** Query-based context that is not added by user */
  Priority = "priority",

  /** Remote search */
  Unified = "unified",

  /** Selected code from the current editor */
  Selection = "selection",

  /** Output from the terminal */
  Terminal = "terminal",

  /** From source control history */
  History = "history",

  /** Agentic context */
  Agentic = "agentic",
}

/**
 * An item (such as a file or symbol) that is included as context in a chat message.
 */
export type CustomVariableItem =
  | CustomVariableItemFile
  | CustomVariableItemRepository
  | CustomVariableItemTree
  | CustomVariableItemSymbol
  | CustomVariableItemSelection
  | CustomVariableItemLanguage
  | CustomVariableUserDefined;

export type BuiltInVariableType = Exclude<CustomVariableItem, CustomVariableUserDefined>["type"];

/**
 * Context items to show by default in the chat input, or as suggestions in the chat UI.
 */
export interface DefaultContext {
  initialContext: CustomVariableItem[];
  corpusContext: CustomVariableItem[];
}

/**
 * A context item that represents a repository.
 */
export interface CustomVariableItemRepository extends CustomVariableItemCommon {
  type: "repository";
  repoName: string;
  repoID: string;
  content: null;
}

/**
 * A context item that represents a tree (directory).
 */
export interface CustomVariableItemTree extends CustomVariableItemCommon {
  type: "tree";

  /** Only workspace root trees are supported right now. */
  isWorkspaceRoot: true;

  content: null;
}

/**
 * A file (or a subset of a file given by a range) that is included as context in a chat message.
 */
export interface CustomVariableItemFile extends CustomVariableItemCommon {
  type: "file";
}

export interface CustomVariableItemSelection extends CustomVariableItemCommon {
  type: "selection";
}

export interface CustomVariableItemLanguage extends CustomVariableItemCommon {
  type: "language";
  language: string;
}

export interface CustomVariableUserDefined extends CustomVariableItemCommon {
  type: "userDefined";
  variableName: string;
  variableValue: string;
}

/**
 * A symbol (which is a range within a file) that is included as context in a chat message.
 */
export interface CustomVariableItemSymbol extends CustomVariableItemCommon {
  type: "symbol";

  /** The name of the symbol, used for presentation only (not semantically meaningful). */
  symbolName: string;

  /** The kind of symbol, used for presentation only (not semantically meaningful). */
  kind: SymbolKind;

  /**
     * Name of remote repository, this is how mention resolve logic checks
     * that we need to resolve this context item mention via remote search file
     */
  remoteRepositoryName?: string;
}

/** The valid kinds of a symbol. */
export type SymbolKind = "class" | "function" | "method";

/** {@link CustomVariableItem} with the `content` field set to the content. */
export type CustomVariableItemWithContent = CustomVariableItem & { content: string };

export const GENERAL_HELP_LABEL = "Search for a file to include, or type # for symbols...";
export const NO_SYMBOL_MATCHES_HELP_LABEL = " (language extensions may be loading)";
export const FILE_RANGE_TOOLTIP_LABEL = "Type a line range to include, e.g. 5-10...";
export const LARGE_FILE_WARNING_LABEL
    = "File too large. Add line range with : or use @# to choose a symbol";
export const IGNORED_FILE_WARNING_LABEL = "File ignored by an admin setting.";
