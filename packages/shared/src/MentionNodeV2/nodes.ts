import { Spread, SerializedLexicalNode } from "lexical";
import { displayLineRange, RangeData } from "../CustomVariable/cody-shared/range";
import { URI } from "vscode-uri";
import { displayPathBasename } from "../CustomVariable/cody-shared/displayPath";
import { Doc } from "../business";
import { SlashCommand } from "../misc";
import { UploadFile } from "../misc/file";

export const MENTION_NODE_V2_TYPE = "mentionNodeV2";

export type MentionNodeV2Structure =
  | MentionNodeV2Structure_File
  | MentionNodeV2Structure_RemoteFile
  | MentionNodeV2Structure_RemoteImage
  | MentionNodeV2Structure_Selection
  | MentionNodeV2Structure_Tree
  | MentionNodeV2Structure_Rule
  | MentionNodeV2Structure_Web
  | MentionNodeV2Structure_Codebase
  | MentionNodeV2Structure_SlashCommand
  | MentionNodeV2Structure_Knowledge;

export interface MentionNodeV2Structure_Common {
  /**
   * 文件的 uri
   */
  uri: string;

  /**
  * 相对于 workspace 的路径，便于展示
  */
  relativePath: string;
}

export interface MentionNodeV2Structure_File extends MentionNodeV2Structure_Common {
  type: "file";

}

/**
 * 对话模式下, 用户通过本地上传到服务端的文件, 在接口层使用服务端的信息传递, 展示时使用本地文件的数据
 *
 * 这一模式与现在智能体模式的上下文文件执行逻辑差异比较大, 因此单独使用一结构体表示
 *
 * uri relativePath 都是本地文件的路径
 *
 * uploadInfo 是上传到服务端的文件信息
 */
export interface MentionNodeV2Structure_RemoteFile extends MentionNodeV2Structure_Common {
  type: "remoteFile";
  uploadInfo: UploadFile;
}
export interface MentionNodeV2Structure_RemoteImage extends MentionNodeV2Structure_Common {
  type: "remoteImage";
  uploadInfo: UploadFile;
}

export interface MentionNodeV2Structure_Rule extends MentionNodeV2Structure_Common {
  type: "rule";
}

export interface MentionNodeV2Structure_Web extends MentionNodeV2Structure_Common {
  uri: "web://";
  relativePath: "";
  type: "web";
}

/**
 * 鉴于 web 节点没有 uri 和 relativePath
 * 这里使用一个默认的结构体
 */
export const placeholderMentionNodeV2Structure_Web: MentionNodeV2Structure_Web = {
  type: "web",
  uri: "web://",
  relativePath: "",
};

/**
 * 对话模式的代码库codebase
 */
export interface MentionNodeV2Structure_Codebase extends MentionNodeV2Structure_Common {
  type: "codebase";
  uri: "codebase://";
  relativePath: "";
}

/**
 * 对话模式的代码库codebase 的占位符
 */
export const placeholderMentionNodeV2Structure_Codebase: MentionNodeV2Structure_Codebase = {
  type: "codebase",
  uri: "codebase://",
  relativePath: "",
};

/**
 * 对话模式的知识库, uri 格式为 `knowledge://<docId>`
 */
export interface MentionNodeV2Structure_Knowledge extends MentionNodeV2Structure_Common {
  type: "knowledge";
  // 对应的知识库详情
  doc: Doc;
  /**
   * 对话模式的知识库, uri 格式为 `knowledge://<docId>`
   */
  uri: `knowledge://${string}`;
}

export interface MentionNodeV2Structure_SlashCommand extends MentionNodeV2Structure_Common {
  type: "slashCommand";
  uri: `slashCommand://${SlashCommand}`;
  command: SlashCommand;
  label: string;
  template: string;
  /**
   * 指令可以绑定一个上下文项 例如 解释代码 your-file.ts:2-4
   */
  contextItem?: MentionNodeV2Structure_File | MentionNodeV2Structure_Selection;
}

export interface MentionNodeV2Structure_Selection extends MentionNodeV2Structure_Common {
  type: "selection";

  range: RangeData;

  content: string;
}

export interface MentionNodeV2Structure_Tree extends MentionNodeV2Structure_Common {
  type: "tree";

}

/**
 * lexical 的 序列化节点
 */
export type SerializedMentionNodeV2 = Spread<
  {
    type: typeof MENTION_NODE_V2_TYPE;
    structure: MentionNodeV2Structure;
  },
  SerializedLexicalNode
>;

export function mentionNodeV2DisplayText(structureData: MentionNodeV2Structure): string {
  switch (structureData.type) {
    case "file":
    case "remoteFile":
    case "remoteImage":
      return `${decodeURIComponent(displayPathBasename(URI.parse(structureData.uri)))}` || "(empty)";
    case "rule":
      return `${decodeURIComponent(displayPathBasename(URI.parse(structureData.uri)))}` || "(empty)";
    case "selection":{
      // A displayed range of `foo.txt:2-4` means "include all of lines 2, 3, and 4", which means the
      // range needs to go to the start (0th character) of line 5. Also, `RangeData` is 0-indexed but
      // display ranges are 1-indexed.
      const rangeText = structureData.range?.start ? `:${displayLineRange(structureData.range)}` : "";
      return `${decodeURIComponent(displayPathBasename(URI.parse(structureData.uri)))}${rangeText}` || "(empty)";
    }
    case "tree":
      return `${decodeURIComponent(displayPathBasename(URI.parse(structureData.uri)))}` || "(empty)";
    case "codebase":
      return "代码库";
    case "knowledge":
      return structureData.doc.name;
    case "slashCommand":
      return structureData.label;
    case "web":
      return "联网";
    default: {
      const neverType: never = structureData;
      return "unknown" + neverType;
    }
  }
}

export function isMentionNodeV2(node: SerializedLexicalNode): node is SerializedMentionNodeV2 {
  return Boolean(typeof node === "object" && node && "type" in node && node.type === MENTION_NODE_V2_TYPE);
}

export const slashCommandSetRequiringContextItem: Set<SlashCommand> = new Set([
  SlashCommand.CODE_EXPLAIN,
  SlashCommand.CODE_REFACTOR,
  SlashCommand.FUNC_COMMENT,
  SlashCommand.FUNC_SPLIT,
  SlashCommand.LINE_CODE_COMMENT,
  SlashCommand.UNIT_TEST,
]);
