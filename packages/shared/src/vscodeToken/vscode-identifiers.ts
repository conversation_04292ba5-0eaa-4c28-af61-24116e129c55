export const actionBarToggledBackground = "--vscode-actionBar-toggledBackground";

export const activityBarActiveBackground = "--vscode-activityBar-activeBackground";

export const activityBarActiveBorder = "--vscode-activityBar-activeBorder";

export const activityBarActiveFocusBorder = "--vscode-activityBar-activeFocusBorder";

export const activityBarBackground = "--vscode-activityBar-background";

export const activityBarBorder = "--vscode-activityBar-border";

export const activityBarDropBorder = "--vscode-activityBar-dropBorder";

export const activityBarForeground = "--vscode-activityBar-foreground";

export const activityBarInactiveForeground = "--vscode-activityBar-inactiveForeground";

export const activityBarBadgeBackground = "--vscode-activityBarBadge-background";

export const activityBarBadgeForeground = "--vscode-activityBarBadge-foreground";

export const activityBarTopActiveBackground = "--vscode-activityBarTop-activeBackground";

export const activityBarTopActiveBorder = "--vscode-activityBarTop-activeBorder";

export const activityBarTopBackground = "--vscode-activityBarTop-background";

export const activityBarTopDropBorder = "--vscode-activityBarTop-dropBorder";

export const activityBarTopForeground = "--vscode-activityBarTop-foreground";

export const activityBarTopInactiveForeground = "--vscode-activityBarTop-inactiveForeground";

export const activityErrorBadgeBackground = "--vscode-activityErrorBadge-background";

export const activityErrorBadgeForeground = "--vscode-activityErrorBadge-foreground";

export const activityWarningBadgeBackground = "--vscode-activityWarningBadge-background";

export const activityWarningBadgeForeground = "--vscode-activityWarningBadge-foreground";

export const badgeBackground = "--vscode-badge-background";

export const badgeForeground = "--vscode-badge-foreground";

export const bannerBackground = "--vscode-banner-background";

export const bannerForeground = "--vscode-banner-foreground";

export const bannerIconForeground = "--vscode-banner-iconForeground";

export const breadcrumbActiveSelectionForeground = "--vscode-breadcrumb-activeSelectionForeground";

export const breadcrumbBackground = "--vscode-breadcrumb-background";

export const breadcrumbFocusForeground = "--vscode-breadcrumb-focusForeground";

export const breadcrumbForeground = "--vscode-breadcrumb-foreground";

export const breadcrumbPickerBackground = "--vscode-breadcrumbPicker-background";

export const buttonBackground = "--vscode-button-background";

export const buttonBorder = "--vscode-button-border";

export const buttonForeground = "--vscode-button-foreground";

export const buttonHoverBackground = "--vscode-button-hoverBackground";

export const buttonSecondaryBackground = "--vscode-button-secondaryBackground";

export const buttonSecondaryForeground = "--vscode-button-secondaryForeground";

export const buttonSecondaryHoverBackground = "--vscode-button-secondaryHoverBackground";

export const buttonSeparator = "--vscode-button-separator";

export const chartAxis = "--vscode-chart-axis";

export const chartGuide = "--vscode-chart-guide";

export const chartLine = "--vscode-chart-line";

export const chartsBlue = "--vscode-charts-blue";

export const chartsForeground = "--vscode-charts-foreground";

export const chartsGreen = "--vscode-charts-green";

export const chartsLines = "--vscode-charts-lines";

export const chartsOrange = "--vscode-charts-orange";

export const chartsPurple = "--vscode-charts-purple";

export const chartsRed = "--vscode-charts-red";

export const chartsYellow = "--vscode-charts-yellow";

export const chatAvatarBackground = "--vscode-chat-avatarBackground";

export const chatAvatarForeground = "--vscode-chat-avatarForeground";

export const chatEditedFileForeground = "--vscode-chat-editedFileForeground";

export const chatRequestBackground = "--vscode-chat-requestBackground";

export const chatRequestBorder = "--vscode-chat-requestBorder";

export const chatSlashCommandBackground = "--vscode-chat-slashCommandBackground";

export const chatSlashCommandForeground = "--vscode-chat-slashCommandForeground";

export const checkboxBackground = "--vscode-checkbox-background";

export const checkboxBorder = "--vscode-checkbox-border";

export const checkboxForeground = "--vscode-checkbox-foreground";

export const checkboxSelectBackground = "--vscode-checkbox-selectBackground";

export const checkboxSelectBorder = "--vscode-checkbox-selectBorder";

export const commandCenterActiveBackground = "--vscode-commandCenter-activeBackground";

export const commandCenterActiveBorder = "--vscode-commandCenter-activeBorder";

export const commandCenterActiveForeground = "--vscode-commandCenter-activeForeground";

export const commandCenterBackground = "--vscode-commandCenter-background";

export const commandCenterBorder = "--vscode-commandCenter-border";

export const commandCenterDebuggingBackground = "--vscode-commandCenter-debuggingBackground";

export const commandCenterForeground = "--vscode-commandCenter-foreground";

export const commandCenterInactiveBorder = "--vscode-commandCenter-inactiveBorder";

export const commandCenterInactiveForeground = "--vscode-commandCenter-inactiveForeground";

export const commentsViewResolvedIcon = "--vscode-commentsView-resolvedIcon";

export const commentsViewUnresolvedIcon = "--vscode-commentsView-unresolvedIcon";

export const contrastActiveBorder = "--vscode-contrastActiveBorder";

export const contrastBorder = "--vscode-contrastBorder";

export const debugConsoleErrorForeground = "--vscode-debugConsole-errorForeground";

export const debugConsoleInfoForeground = "--vscode-debugConsole-infoForeground";

export const debugConsoleSourceForeground = "--vscode-debugConsole-sourceForeground";

export const debugConsoleWarningForeground = "--vscode-debugConsole-warningForeground";

export const debugConsoleInputIconForeground = "--vscode-debugConsoleInputIcon-foreground";

export const debugExceptionWidgetBackground = "--vscode-debugExceptionWidget-background";

export const debugExceptionWidgetBorder = "--vscode-debugExceptionWidget-border";

export const debugIconBreakpointCurrentStackframeForeground = "--vscode-debugIcon-breakpointCurrentStackframeForeground";

export const debugIconBreakpointDisabledForeground = "--vscode-debugIcon-breakpointDisabledForeground";

export const debugIconBreakpointForeground = "--vscode-debugIcon-breakpointForeground";

export const debugIconBreakpointStackframeForeground = "--vscode-debugIcon-breakpointStackframeForeground";

export const debugIconBreakpointUnverifiedForeground = "--vscode-debugIcon-breakpointUnverifiedForeground";

export const debugIconContinueForeground = "--vscode-debugIcon-continueForeground";

export const debugIconDisconnectForeground = "--vscode-debugIcon-disconnectForeground";

export const debugIconPauseForeground = "--vscode-debugIcon-pauseForeground";

export const debugIconRestartForeground = "--vscode-debugIcon-restartForeground";

export const debugIconStartForeground = "--vscode-debugIcon-startForeground";

export const debugIconStepBackForeground = "--vscode-debugIcon-stepBackForeground";

export const debugIconStepIntoForeground = "--vscode-debugIcon-stepIntoForeground";

export const debugIconStepOutForeground = "--vscode-debugIcon-stepOutForeground";

export const debugIconStepOverForeground = "--vscode-debugIcon-stepOverForeground";

export const debugIconStopForeground = "--vscode-debugIcon-stopForeground";

export const debugTokenExpressionBoolean = "--vscode-debugTokenExpression-boolean";

export const debugTokenExpressionError = "--vscode-debugTokenExpression-error";

export const debugTokenExpressionName = "--vscode-debugTokenExpression-name";

export const debugTokenExpressionNumber = "--vscode-debugTokenExpression-number";

export const debugTokenExpressionString = "--vscode-debugTokenExpression-string";

export const debugTokenExpressionType = "--vscode-debugTokenExpression-type";

export const debugTokenExpressionValue = "--vscode-debugTokenExpression-value";

export const debugToolBarBackground = "--vscode-debugToolBar-background";

export const debugToolBarBorder = "--vscode-debugToolBar-border";

export const debugViewExceptionLabelBackground = "--vscode-debugView-exceptionLabelBackground";

export const debugViewExceptionLabelForeground = "--vscode-debugView-exceptionLabelForeground";

export const debugViewStateLabelBackground = "--vscode-debugView-stateLabelBackground";

export const debugViewStateLabelForeground = "--vscode-debugView-stateLabelForeground";

export const debugViewValueChangedHighlight = "--vscode-debugView-valueChangedHighlight";

export const descriptionForeground = "--vscode-descriptionForeground";

export const diffEditorBorder = "--vscode-diffEditor-border";

export const diffEditorDiagonalFill = "--vscode-diffEditor-diagonalFill";

export const diffEditorInsertedLineBackground = "--vscode-diffEditor-insertedLineBackground";

export const diffEditorInsertedTextBackground = "--vscode-diffEditor-insertedTextBackground";

export const diffEditorInsertedTextBorder = "--vscode-diffEditor-insertedTextBorder";

export const diffEditorMoveBorder = "--vscode-diffEditor-move-border";

export const diffEditorMoveActiveBorder = "--vscode-diffEditor-moveActive-border";

export const diffEditorRemovedLineBackground = "--vscode-diffEditor-removedLineBackground";

export const diffEditorRemovedTextBackground = "--vscode-diffEditor-removedTextBackground";

export const diffEditorRemovedTextBorder = "--vscode-diffEditor-removedTextBorder";

export const diffEditorUnchangedCodeBackground = "--vscode-diffEditor-unchangedCodeBackground";

export const diffEditorUnchangedRegionBackground = "--vscode-diffEditor-unchangedRegionBackground";

export const diffEditorUnchangedRegionForeground = "--vscode-diffEditor-unchangedRegionForeground";

export const diffEditorUnchangedRegionShadow = "--vscode-diffEditor-unchangedRegionShadow";

export const diffEditorGutterInsertedLineBackground = "--vscode-diffEditorGutter-insertedLineBackground";

export const diffEditorGutterRemovedLineBackground = "--vscode-diffEditorGutter-removedLineBackground";

export const diffEditorOverviewInsertedForeground = "--vscode-diffEditorOverview-insertedForeground";

export const diffEditorOverviewRemovedForeground = "--vscode-diffEditorOverview-removedForeground";

export const disabledForeground = "--vscode-disabledForeground";

export const dropdownBackground = "--vscode-dropdown-background";

export const dropdownBorder = "--vscode-dropdown-border";

export const dropdownForeground = "--vscode-dropdown-foreground";

export const dropdownListBackground = "--vscode-dropdown-listBackground";

export const editorBackground = "--vscode-editor-background";

export const editorCompositionBorder = "--vscode-editor-compositionBorder";

export const editorFindMatchBackground = "--vscode-editor-findMatchBackground";

export const editorFindMatchBorder = "--vscode-editor-findMatchBorder";

export const editorFindMatchForeground = "--vscode-editor-findMatchForeground";

export const editorFindMatchHighlightBackground = "--vscode-editor-findMatchHighlightBackground";

export const editorFindMatchHighlightBorder = "--vscode-editor-findMatchHighlightBorder";

export const editorFindMatchHighlightForeground = "--vscode-editor-findMatchHighlightForeground";

export const editorFindRangeHighlightBackground = "--vscode-editor-findRangeHighlightBackground";

export const editorFindRangeHighlightBorder = "--vscode-editor-findRangeHighlightBorder";

export const editorFocusedStackFrameHighlightBackground = "--vscode-editor-focusedStackFrameHighlightBackground";

export const editorFoldBackground = "--vscode-editor-foldBackground";

export const editorFoldPlaceholderForeground = "--vscode-editor-foldPlaceholderForeground";

export const editorForeground = "--vscode-editor-foreground";

export const editorHoverHighlightBackground = "--vscode-editor-hoverHighlightBackground";

export const editorInactiveSelectionBackground = "--vscode-editor-inactiveSelectionBackground";

export const editorInlineValuesBackground = "--vscode-editor-inlineValuesBackground";

export const editorInlineValuesForeground = "--vscode-editor-inlineValuesForeground";

export const editorLineHighlightBackground = "--vscode-editor-lineHighlightBackground";

export const editorLineHighlightBorder = "--vscode-editor-lineHighlightBorder";

export const editorLinkedEditingBackground = "--vscode-editor-linkedEditingBackground";

export const editorPlaceholderForeground = "--vscode-editor-placeholder-foreground";

export const editorRangeHighlightBackground = "--vscode-editor-rangeHighlightBackground";

export const editorRangeHighlightBorder = "--vscode-editor-rangeHighlightBorder";

export const editorSelectionBackground = "--vscode-editor-selectionBackground";

export const editorSelectionForeground = "--vscode-editor-selectionForeground";

export const editorSelectionHighlightBackground = "--vscode-editor-selectionHighlightBackground";

export const editorSelectionHighlightBorder = "--vscode-editor-selectionHighlightBorder";

export const editorSnippetFinalTabstopHighlightBackground = "--vscode-editor-snippetFinalTabstopHighlightBackground";

export const editorSnippetFinalTabstopHighlightBorder = "--vscode-editor-snippetFinalTabstopHighlightBorder";

export const editorSnippetTabstopHighlightBackground = "--vscode-editor-snippetTabstopHighlightBackground";

export const editorSnippetTabstopHighlightBorder = "--vscode-editor-snippetTabstopHighlightBorder";

export const editorStackFrameHighlightBackground = "--vscode-editor-stackFrameHighlightBackground";

export const editorSymbolHighlightBackground = "--vscode-editor-symbolHighlightBackground";

export const editorSymbolHighlightBorder = "--vscode-editor-symbolHighlightBorder";

export const editorWordHighlightBackground = "--vscode-editor-wordHighlightBackground";

export const editorWordHighlightBorder = "--vscode-editor-wordHighlightBorder";

export const editorWordHighlightStrongBackground = "--vscode-editor-wordHighlightStrongBackground";

export const editorWordHighlightStrongBorder = "--vscode-editor-wordHighlightStrongBorder";

export const editorWordHighlightTextBackground = "--vscode-editor-wordHighlightTextBackground";

export const editorWordHighlightTextBorder = "--vscode-editor-wordHighlightTextBorder";

export const editorActionListBackground = "--vscode-editorActionList-background";

export const editorActionListFocusBackground = "--vscode-editorActionList-focusBackground";

export const editorActionListFocusForeground = "--vscode-editorActionList-focusForeground";

export const editorActionListForeground = "--vscode-editorActionList-foreground";

export const editorActiveLineNumberForeground = "--vscode-editorActiveLineNumber-foreground";

export const editorBracketHighlightForeground1 = "--vscode-editorBracketHighlight-foreground1";

export const editorBracketHighlightForeground2 = "--vscode-editorBracketHighlight-foreground2";

export const editorBracketHighlightForeground3 = "--vscode-editorBracketHighlight-foreground3";

export const editorBracketHighlightForeground4 = "--vscode-editorBracketHighlight-foreground4";

export const editorBracketHighlightForeground5 = "--vscode-editorBracketHighlight-foreground5";

export const editorBracketHighlightForeground6 = "--vscode-editorBracketHighlight-foreground6";

export const editorBracketHighlightUnexpectedBracketForeground = "--vscode-editorBracketHighlight-unexpectedBracket-foreground";

export const editorBracketMatchBackground = "--vscode-editorBracketMatch-background";

export const editorBracketMatchBorder = "--vscode-editorBracketMatch-border";

export const editorBracketPairGuideActiveBackground1 = "--vscode-editorBracketPairGuide-activeBackground1";

export const editorBracketPairGuideActiveBackground2 = "--vscode-editorBracketPairGuide-activeBackground2";

export const editorBracketPairGuideActiveBackground3 = "--vscode-editorBracketPairGuide-activeBackground3";

export const editorBracketPairGuideActiveBackground4 = "--vscode-editorBracketPairGuide-activeBackground4";

export const editorBracketPairGuideActiveBackground5 = "--vscode-editorBracketPairGuide-activeBackground5";

export const editorBracketPairGuideActiveBackground6 = "--vscode-editorBracketPairGuide-activeBackground6";

export const editorBracketPairGuideBackground1 = "--vscode-editorBracketPairGuide-background1";

export const editorBracketPairGuideBackground2 = "--vscode-editorBracketPairGuide-background2";

export const editorBracketPairGuideBackground3 = "--vscode-editorBracketPairGuide-background3";

export const editorBracketPairGuideBackground4 = "--vscode-editorBracketPairGuide-background4";

export const editorBracketPairGuideBackground5 = "--vscode-editorBracketPairGuide-background5";

export const editorBracketPairGuideBackground6 = "--vscode-editorBracketPairGuide-background6";

export const editorCodeLensForeground = "--vscode-editorCodeLens-foreground";

export const editorCommentsWidgetRangeActiveBackground = "--vscode-editorCommentsWidget-rangeActiveBackground";

export const editorCommentsWidgetRangeBackground = "--vscode-editorCommentsWidget-rangeBackground";

export const editorCommentsWidgetReplyInputBackground = "--vscode-editorCommentsWidget-replyInputBackground";

export const editorCommentsWidgetResolvedBorder = "--vscode-editorCommentsWidget-resolvedBorder";

export const editorCommentsWidgetUnresolvedBorder = "--vscode-editorCommentsWidget-unresolvedBorder";

export const editorCursorBackground = "--vscode-editorCursor-background";

export const editorCursorForeground = "--vscode-editorCursor-foreground";

export const editorErrorBackground = "--vscode-editorError-background";

export const editorErrorBorder = "--vscode-editorError-border";

export const editorErrorForeground = "--vscode-editorError-foreground";

export const editorGhostTextBackground = "--vscode-editorGhostText-background";

export const editorGhostTextBorder = "--vscode-editorGhostText-border";

export const editorGhostTextForeground = "--vscode-editorGhostText-foreground";

export const editorGroupBorder = "--vscode-editorGroup-border";

export const editorGroupDropBackground = "--vscode-editorGroup-dropBackground";

export const editorGroupDropIntoPromptBackground = "--vscode-editorGroup-dropIntoPromptBackground";

export const editorGroupDropIntoPromptBorder = "--vscode-editorGroup-dropIntoPromptBorder";

export const editorGroupDropIntoPromptForeground = "--vscode-editorGroup-dropIntoPromptForeground";

export const editorGroupEmptyBackground = "--vscode-editorGroup-emptyBackground";

export const editorGroupFocusedEmptyBorder = "--vscode-editorGroup-focusedEmptyBorder";

export const editorGroupHeaderBorder = "--vscode-editorGroupHeader-border";

export const editorGroupHeaderNoTabsBackground = "--vscode-editorGroupHeader-noTabsBackground";

export const editorGroupHeaderTabsBackground = "--vscode-editorGroupHeader-tabsBackground";

export const editorGroupHeaderTabsBorder = "--vscode-editorGroupHeader-tabsBorder";

export const editorGutterAddedBackground = "--vscode-editorGutter-addedBackground";

export const editorGutterBackground = "--vscode-editorGutter-background";

export const editorGutterCommentGlyphForeground = "--vscode-editorGutter-commentGlyphForeground";

export const editorGutterCommentRangeForeground = "--vscode-editorGutter-commentRangeForeground";

export const editorGutterCommentUnresolvedGlyphForeground = "--vscode-editorGutter-commentUnresolvedGlyphForeground";

export const editorGutterDeletedBackground = "--vscode-editorGutter-deletedBackground";

export const editorGutterFoldingControlForeground = "--vscode-editorGutter-foldingControlForeground";

export const editorGutterItemBackground = "--vscode-editorGutter-itemBackground";

export const editorGutterItemGlyphForeground = "--vscode-editorGutter-itemGlyphForeground";

export const editorGutterModifiedBackground = "--vscode-editorGutter-modifiedBackground";

export const editorHintBorder = "--vscode-editorHint-border";

export const editorHintForeground = "--vscode-editorHint-foreground";

export const editorHoverWidgetBackground = "--vscode-editorHoverWidget-background";

export const editorHoverWidgetBorder = "--vscode-editorHoverWidget-border";

export const editorHoverWidgetForeground = "--vscode-editorHoverWidget-foreground";

export const editorHoverWidgetHighlightForeground = "--vscode-editorHoverWidget-highlightForeground";

export const editorHoverWidgetStatusBarBackground = "--vscode-editorHoverWidget-statusBarBackground";

export const editorIndentGuideActiveBackground = "--vscode-editorIndentGuide-activeBackground";

export const editorIndentGuideActiveBackground1 = "--vscode-editorIndentGuide-activeBackground1";

export const editorIndentGuideActiveBackground2 = "--vscode-editorIndentGuide-activeBackground2";

export const editorIndentGuideActiveBackground3 = "--vscode-editorIndentGuide-activeBackground3";

export const editorIndentGuideActiveBackground4 = "--vscode-editorIndentGuide-activeBackground4";

export const editorIndentGuideActiveBackground5 = "--vscode-editorIndentGuide-activeBackground5";

export const editorIndentGuideActiveBackground6 = "--vscode-editorIndentGuide-activeBackground6";

export const editorIndentGuideBackground = "--vscode-editorIndentGuide-background";

export const editorIndentGuideBackground1 = "--vscode-editorIndentGuide-background1";

export const editorIndentGuideBackground2 = "--vscode-editorIndentGuide-background2";

export const editorIndentGuideBackground3 = "--vscode-editorIndentGuide-background3";

export const editorIndentGuideBackground4 = "--vscode-editorIndentGuide-background4";

export const editorIndentGuideBackground5 = "--vscode-editorIndentGuide-background5";

export const editorIndentGuideBackground6 = "--vscode-editorIndentGuide-background6";

export const editorInfoBackground = "--vscode-editorInfo-background";

export const editorInfoBorder = "--vscode-editorInfo-border";

export const editorInfoForeground = "--vscode-editorInfo-foreground";

export const editorInlayHintBackground = "--vscode-editorInlayHint-background";

export const editorInlayHintForeground = "--vscode-editorInlayHint-foreground";

export const editorInlayHintParameterBackground = "--vscode-editorInlayHint-parameterBackground";

export const editorInlayHintParameterForeground = "--vscode-editorInlayHint-parameterForeground";

export const editorInlayHintTypeBackground = "--vscode-editorInlayHint-typeBackground";

export const editorInlayHintTypeForeground = "--vscode-editorInlayHint-typeForeground";

export const editorLightBulbForeground = "--vscode-editorLightBulb-foreground";

export const editorLightBulbAiForeground = "--vscode-editorLightBulbAi-foreground";

export const editorLightBulbAutoFixForeground = "--vscode-editorLightBulbAutoFix-foreground";

export const editorLineNumberActiveForeground = "--vscode-editorLineNumber-activeForeground";

export const editorLineNumberDimmedForeground = "--vscode-editorLineNumber-dimmedForeground";

export const editorLineNumberForeground = "--vscode-editorLineNumber-foreground";

export const editorLinkActiveForeground = "--vscode-editorLink-activeForeground";

export const editorMarkerNavigationBackground = "--vscode-editorMarkerNavigation-background";

export const editorMarkerNavigationErrorBackground = "--vscode-editorMarkerNavigationError-background";

export const editorMarkerNavigationErrorHeaderBackground = "--vscode-editorMarkerNavigationError-headerBackground";

export const editorMarkerNavigationInfoBackground = "--vscode-editorMarkerNavigationInfo-background";

export const editorMarkerNavigationInfoHeaderBackground = "--vscode-editorMarkerNavigationInfo-headerBackground";

export const editorMarkerNavigationWarningBackground = "--vscode-editorMarkerNavigationWarning-background";

export const editorMarkerNavigationWarningHeaderBackground = "--vscode-editorMarkerNavigationWarning-headerBackground";

export const editorMinimapInlineChatInserted = "--vscode-editorMinimap-inlineChatInserted";

export const editorMultiCursorPrimaryBackground = "--vscode-editorMultiCursor-primary-background";

export const editorMultiCursorPrimaryForeground = "--vscode-editorMultiCursor-primary-foreground";

export const editorMultiCursorSecondaryBackground = "--vscode-editorMultiCursor-secondary-background";

export const editorMultiCursorSecondaryForeground = "--vscode-editorMultiCursor-secondary-foreground";

export const editorOverviewRulerAddedForeground = "--vscode-editorOverviewRuler-addedForeground";

export const editorOverviewRulerBackground = "--vscode-editorOverviewRuler-background";

export const editorOverviewRulerBorder = "--vscode-editorOverviewRuler-border";

export const editorOverviewRulerBracketMatchForeground = "--vscode-editorOverviewRuler-bracketMatchForeground";

export const editorOverviewRulerCommentForeground = "--vscode-editorOverviewRuler-commentForeground";

export const editorOverviewRulerCommentUnresolvedForeground = "--vscode-editorOverviewRuler-commentUnresolvedForeground";

export const editorOverviewRulerCommonContentForeground = "--vscode-editorOverviewRuler-commonContentForeground";

export const editorOverviewRulerCurrentContentForeground = "--vscode-editorOverviewRuler-currentContentForeground";

export const editorOverviewRulerDeletedForeground = "--vscode-editorOverviewRuler-deletedForeground";

export const editorOverviewRulerErrorForeground = "--vscode-editorOverviewRuler-errorForeground";

export const editorOverviewRulerFindMatchForeground = "--vscode-editorOverviewRuler-findMatchForeground";

export const editorOverviewRulerIncomingContentForeground = "--vscode-editorOverviewRuler-incomingContentForeground";

export const editorOverviewRulerInfoForeground = "--vscode-editorOverviewRuler-infoForeground";

export const editorOverviewRulerInlineChatInserted = "--vscode-editorOverviewRuler-inlineChatInserted";

export const editorOverviewRulerInlineChatRemoved = "--vscode-editorOverviewRuler-inlineChatRemoved";

export const editorOverviewRulerModifiedForeground = "--vscode-editorOverviewRuler-modifiedForeground";

export const editorOverviewRulerRangeHighlightForeground = "--vscode-editorOverviewRuler-rangeHighlightForeground";

export const editorOverviewRulerSelectionHighlightForeground = "--vscode-editorOverviewRuler-selectionHighlightForeground";

export const editorOverviewRulerWarningForeground = "--vscode-editorOverviewRuler-warningForeground";

export const editorOverviewRulerWordHighlightForeground = "--vscode-editorOverviewRuler-wordHighlightForeground";

export const editorOverviewRulerWordHighlightStrongForeground = "--vscode-editorOverviewRuler-wordHighlightStrongForeground";

export const editorOverviewRulerWordHighlightTextForeground = "--vscode-editorOverviewRuler-wordHighlightTextForeground";

export const editorPaneBackground = "--vscode-editorPane-background";

export const editorRulerForeground = "--vscode-editorRuler-foreground";

export const editorStickyScrollBackground = "--vscode-editorStickyScroll-background";

export const editorStickyScrollBorder = "--vscode-editorStickyScroll-border";

export const editorStickyScrollShadow = "--vscode-editorStickyScroll-shadow";

export const editorStickyScrollHoverBackground = "--vscode-editorStickyScrollHover-background";

export const editorSuggestWidgetBackground = "--vscode-editorSuggestWidget-background";

export const editorSuggestWidgetBorder = "--vscode-editorSuggestWidget-border";

export const editorSuggestWidgetFocusHighlightForeground = "--vscode-editorSuggestWidget-focusHighlightForeground";

export const editorSuggestWidgetForeground = "--vscode-editorSuggestWidget-foreground";

export const editorSuggestWidgetHighlightForeground = "--vscode-editorSuggestWidget-highlightForeground";

export const editorSuggestWidgetSelectedBackground = "--vscode-editorSuggestWidget-selectedBackground";

export const editorSuggestWidgetSelectedForeground = "--vscode-editorSuggestWidget-selectedForeground";

export const editorSuggestWidgetSelectedIconForeground = "--vscode-editorSuggestWidget-selectedIconForeground";

export const editorSuggestWidgetStatusForeground = "--vscode-editorSuggestWidgetStatus-foreground";

export const editorUnicodeHighlightBackground = "--vscode-editorUnicodeHighlight-background";

export const editorUnicodeHighlightBorder = "--vscode-editorUnicodeHighlight-border";

export const editorUnnecessaryCodeBorder = "--vscode-editorUnnecessaryCode-border";

export const editorUnnecessaryCodeOpacity = "--vscode-editorUnnecessaryCode-opacity";

export const editorWarningBackground = "--vscode-editorWarning-background";

export const editorWarningBorder = "--vscode-editorWarning-border";

export const editorWarningForeground = "--vscode-editorWarning-foreground";

export const editorWatermarkForeground = "--vscode-editorWatermark-foreground";

export const editorWhitespaceForeground = "--vscode-editorWhitespace-foreground";

export const editorWidgetBackground = "--vscode-editorWidget-background";

export const editorWidgetBorder = "--vscode-editorWidget-border";

export const editorWidgetForeground = "--vscode-editorWidget-foreground";

export const editorWidgetResizeBorder = "--vscode-editorWidget-resizeBorder";

export const errorForeground = "--vscode-errorForeground";

export const extensionBadgeRemoteBackground = "--vscode-extensionBadge-remoteBackground";

export const extensionBadgeRemoteForeground = "--vscode-extensionBadge-remoteForeground";

export const extensionButtonBackground = "--vscode-extensionButton-background";

export const extensionButtonForeground = "--vscode-extensionButton-foreground";

export const extensionButtonHoverBackground = "--vscode-extensionButton-hoverBackground";

export const extensionButtonProminentBackground = "--vscode-extensionButton-prominentBackground";

export const extensionButtonProminentForeground = "--vscode-extensionButton-prominentForeground";

export const extensionButtonProminentHoverBackground = "--vscode-extensionButton-prominentHoverBackground";

export const extensionButtonSeparator = "--vscode-extensionButton-separator";

export const extensionIconPreReleaseForeground = "--vscode-extensionIcon-preReleaseForeground";

export const extensionIconPrivateForeground = "--vscode-extensionIcon-privateForeground";

export const extensionIconSponsorForeground = "--vscode-extensionIcon-sponsorForeground";

export const extensionIconStarForeground = "--vscode-extensionIcon-starForeground";

export const extensionIconVerifiedForeground = "--vscode-extensionIcon-verifiedForeground";

export const focusBorder = "--vscode-focusBorder";

export const foreground = "--vscode-foreground";

export const gaugeBackground = "--vscode-gauge-background";

export const gaugeBorder = "--vscode-gauge-border";

export const gaugeErrorBackground = "--vscode-gauge-errorBackground";

export const gaugeErrorForeground = "--vscode-gauge-errorForeground";

export const gaugeForeground = "--vscode-gauge-foreground";

export const gaugeWarningBackground = "--vscode-gauge-warningBackground";

export const gaugeWarningForeground = "--vscode-gauge-warningForeground";

export const iconForeground = "--vscode-icon-foreground";

export const inlineChatBackground = "--vscode-inlineChat-background";

export const inlineChatBorder = "--vscode-inlineChat-border";

export const inlineChatForeground = "--vscode-inlineChat-foreground";

export const inlineChatShadow = "--vscode-inlineChat-shadow";

export const inlineChatDiffInserted = "--vscode-inlineChatDiff-inserted";

export const inlineChatDiffRemoved = "--vscode-inlineChatDiff-removed";

export const inlineChatInputBackground = "--vscode-inlineChatInput-background";

export const inlineChatInputBorder = "--vscode-inlineChatInput-border";

export const inlineChatInputFocusBorder = "--vscode-inlineChatInput-focusBorder";

export const inlineChatInputPlaceholderForeground = "--vscode-inlineChatInput-placeholderForeground";

export const inlineEditGutterIndicatorBackground = "--vscode-inlineEdit-gutterIndicator-background";

export const inlineEditGutterIndicatorPrimaryBackground = "--vscode-inlineEdit-gutterIndicator-primaryBackground";

export const inlineEditGutterIndicatorPrimaryBorder = "--vscode-inlineEdit-gutterIndicator-primaryBorder";

export const inlineEditGutterIndicatorPrimaryForeground = "--vscode-inlineEdit-gutterIndicator-primaryForeground";

export const inlineEditGutterIndicatorSecondaryBackground = "--vscode-inlineEdit-gutterIndicator-secondaryBackground";

export const inlineEditGutterIndicatorSecondaryBorder = "--vscode-inlineEdit-gutterIndicator-secondaryBorder";

export const inlineEditGutterIndicatorSecondaryForeground = "--vscode-inlineEdit-gutterIndicator-secondaryForeground";

export const inlineEditGutterIndicatorSuccessfulBackground = "--vscode-inlineEdit-gutterIndicator-successfulBackground";

export const inlineEditGutterIndicatorSuccessfulBorder = "--vscode-inlineEdit-gutterIndicator-successfulBorder";

export const inlineEditGutterIndicatorSuccessfulForeground = "--vscode-inlineEdit-gutterIndicator-successfulForeground";

export const inlineEditModifiedBackground = "--vscode-inlineEdit-modifiedBackground";

export const inlineEditModifiedBorder = "--vscode-inlineEdit-modifiedBorder";

export const inlineEditModifiedChangedLineBackground = "--vscode-inlineEdit-modifiedChangedLineBackground";

export const inlineEditModifiedChangedTextBackground = "--vscode-inlineEdit-modifiedChangedTextBackground";

export const inlineEditOriginalBackground = "--vscode-inlineEdit-originalBackground";

export const inlineEditOriginalBorder = "--vscode-inlineEdit-originalBorder";

export const inlineEditOriginalChangedLineBackground = "--vscode-inlineEdit-originalChangedLineBackground";

export const inlineEditOriginalChangedTextBackground = "--vscode-inlineEdit-originalChangedTextBackground";

export const inlineEditTabWillAcceptModifiedBorder = "--vscode-inlineEdit-tabWillAcceptModifiedBorder";

export const inlineEditTabWillAcceptOriginalBorder = "--vscode-inlineEdit-tabWillAcceptOriginalBorder";

export const inputBackground = "--vscode-input-background";

export const inputBorder = "--vscode-input-border";

export const inputForeground = "--vscode-input-foreground";

export const inputPlaceholderForeground = "--vscode-input-placeholderForeground";

export const inputOptionActiveBackground = "--vscode-inputOption-activeBackground";

export const inputOptionActiveBorder = "--vscode-inputOption-activeBorder";

export const inputOptionActiveForeground = "--vscode-inputOption-activeForeground";

export const inputOptionHoverBackground = "--vscode-inputOption-hoverBackground";

export const inputValidationErrorBackground = "--vscode-inputValidation-errorBackground";

export const inputValidationErrorBorder = "--vscode-inputValidation-errorBorder";

export const inputValidationErrorForeground = "--vscode-inputValidation-errorForeground";

export const inputValidationInfoBackground = "--vscode-inputValidation-infoBackground";

export const inputValidationInfoBorder = "--vscode-inputValidation-infoBorder";

export const inputValidationInfoForeground = "--vscode-inputValidation-infoForeground";

export const inputValidationWarningBackground = "--vscode-inputValidation-warningBackground";

export const inputValidationWarningBorder = "--vscode-inputValidation-warningBorder";

export const inputValidationWarningForeground = "--vscode-inputValidation-warningForeground";

export const interactiveActiveCodeBorder = "--vscode-interactive-activeCodeBorder";

export const interactiveInactiveCodeBorder = "--vscode-interactive-inactiveCodeBorder";

export const keybindingLabelBackground = "--vscode-keybindingLabel-background";

export const keybindingLabelBorder = "--vscode-keybindingLabel-border";

export const keybindingLabelBottomBorder = "--vscode-keybindingLabel-bottomBorder";

export const keybindingLabelForeground = "--vscode-keybindingLabel-foreground";

export const keybindingTableHeaderBackground = "--vscode-keybindingTable-headerBackground";

export const keybindingTableRowsBackground = "--vscode-keybindingTable-rowsBackground";

export const listActiveSelectionBackground = "--vscode-list-activeSelectionBackground";

export const listActiveSelectionForeground = "--vscode-list-activeSelectionForeground";

export const listActiveSelectionIconForeground = "--vscode-list-activeSelectionIconForeground";

export const listDeemphasizedForeground = "--vscode-list-deemphasizedForeground";

export const listDropBackground = "--vscode-list-dropBackground";

export const listDropBetweenBackground = "--vscode-list-dropBetweenBackground";

export const listErrorForeground = "--vscode-list-errorForeground";

export const listFilterMatchBackground = "--vscode-list-filterMatchBackground";

export const listFilterMatchBorder = "--vscode-list-filterMatchBorder";

export const listFocusAndSelectionOutline = "--vscode-list-focusAndSelectionOutline";

export const listFocusBackground = "--vscode-list-focusBackground";

export const listFocusForeground = "--vscode-list-focusForeground";

export const listFocusHighlightForeground = "--vscode-list-focusHighlightForeground";

export const listFocusOutline = "--vscode-list-focusOutline";

export const listHighlightForeground = "--vscode-list-highlightForeground";

export const listHoverBackground = "--vscode-list-hoverBackground";

export const listHoverForeground = "--vscode-list-hoverForeground";

export const listInactiveFocusBackground = "--vscode-list-inactiveFocusBackground";

export const listInactiveFocusOutline = "--vscode-list-inactiveFocusOutline";

export const listInactiveSelectionBackground = "--vscode-list-inactiveSelectionBackground";

export const listInactiveSelectionForeground = "--vscode-list-inactiveSelectionForeground";

export const listInactiveSelectionIconForeground = "--vscode-list-inactiveSelectionIconForeground";

export const listInvalidItemForeground = "--vscode-list-invalidItemForeground";

export const listWarningForeground = "--vscode-list-warningForeground";

export const listFilterWidgetBackground = "--vscode-listFilterWidget-background";

export const listFilterWidgetNoMatchesOutline = "--vscode-listFilterWidget-noMatchesOutline";

export const listFilterWidgetOutline = "--vscode-listFilterWidget-outline";

export const listFilterWidgetShadow = "--vscode-listFilterWidget-shadow";

export const menuBackground = "--vscode-menu-background";

export const menuBorder = "--vscode-menu-border";

export const menuForeground = "--vscode-menu-foreground";

export const menuSelectionBackground = "--vscode-menu-selectionBackground";

export const menuSelectionBorder = "--vscode-menu-selectionBorder";

export const menuSelectionForeground = "--vscode-menu-selectionForeground";

export const menuSeparatorBackground = "--vscode-menu-separatorBackground";

export const menubarSelectionBackground = "--vscode-menubar-selectionBackground";

export const menubarSelectionBorder = "--vscode-menubar-selectionBorder";

export const menubarSelectionForeground = "--vscode-menubar-selectionForeground";

export const mergeBorder = "--vscode-merge-border";

export const mergeCommonContentBackground = "--vscode-merge-commonContentBackground";

export const mergeCommonHeaderBackground = "--vscode-merge-commonHeaderBackground";

export const mergeCurrentContentBackground = "--vscode-merge-currentContentBackground";

export const mergeCurrentHeaderBackground = "--vscode-merge-currentHeaderBackground";

export const mergeIncomingContentBackground = "--vscode-merge-incomingContentBackground";

export const mergeIncomingHeaderBackground = "--vscode-merge-incomingHeaderBackground";

export const mergeEditorChangeBackground = "--vscode-mergeEditor-change-background";

export const mergeEditorChangeWordBackground = "--vscode-mergeEditor-change-word-background";

export const mergeEditorChangeBaseBackground = "--vscode-mergeEditor-changeBase-background";

export const mergeEditorChangeBaseWordBackground = "--vscode-mergeEditor-changeBase-word-background";

export const mergeEditorConflictHandledMinimapOverViewRuler = "--vscode-mergeEditor-conflict-handled-minimapOverViewRuler";

export const mergeEditorConflictHandledFocusedBorder = "--vscode-mergeEditor-conflict-handledFocused-border";

export const mergeEditorConflictHandledUnfocusedBorder = "--vscode-mergeEditor-conflict-handledUnfocused-border";

export const mergeEditorConflictInput1Background = "--vscode-mergeEditor-conflict-input1-background";

export const mergeEditorConflictInput2Background = "--vscode-mergeEditor-conflict-input2-background";

export const mergeEditorConflictUnhandledMinimapOverViewRuler = "--vscode-mergeEditor-conflict-unhandled-minimapOverViewRuler";

export const mergeEditorConflictUnhandledFocusedBorder = "--vscode-mergeEditor-conflict-unhandledFocused-border";

export const mergeEditorConflictUnhandledUnfocusedBorder = "--vscode-mergeEditor-conflict-unhandledUnfocused-border";

export const mergeEditorConflictingLinesBackground = "--vscode-mergeEditor-conflictingLines-background";

export const minimapBackground = "--vscode-minimap-background";

export const minimapChatEditHighlight = "--vscode-minimap-chatEditHighlight";

export const minimapErrorHighlight = "--vscode-minimap-errorHighlight";

export const minimapFindMatchHighlight = "--vscode-minimap-findMatchHighlight";

export const minimapForegroundOpacity = "--vscode-minimap-foregroundOpacity";

export const minimapInfoHighlight = "--vscode-minimap-infoHighlight";

export const minimapSelectionHighlight = "--vscode-minimap-selectionHighlight";

export const minimapSelectionOccurrenceHighlight = "--vscode-minimap-selectionOccurrenceHighlight";

export const minimapWarningHighlight = "--vscode-minimap-warningHighlight";

export const minimapGutterAddedBackground = "--vscode-minimapGutter-addedBackground";

export const minimapGutterDeletedBackground = "--vscode-minimapGutter-deletedBackground";

export const minimapGutterModifiedBackground = "--vscode-minimapGutter-modifiedBackground";

export const minimapSliderActiveBackground = "--vscode-minimapSlider-activeBackground";

export const minimapSliderBackground = "--vscode-minimapSlider-background";

export const minimapSliderHoverBackground = "--vscode-minimapSlider-hoverBackground";

export const multiDiffEditorBackground = "--vscode-multiDiffEditor-background";

export const multiDiffEditorBorder = "--vscode-multiDiffEditor-border";

export const multiDiffEditorHeaderBackground = "--vscode-multiDiffEditor-headerBackground";

export const notebookCellBorderColor = "--vscode-notebook-cellBorderColor";

export const notebookCellEditorBackground = "--vscode-notebook-cellEditorBackground";

export const notebookCellHoverBackground = "--vscode-notebook-cellHoverBackground";

export const notebookCellInsertionIndicator = "--vscode-notebook-cellInsertionIndicator";

export const notebookCellStatusBarItemHoverBackground = "--vscode-notebook-cellStatusBarItemHoverBackground";

export const notebookCellToolbarSeparator = "--vscode-notebook-cellToolbarSeparator";

export const notebookEditorBackground = "--vscode-notebook-editorBackground";

export const notebookFocusedCellBackground = "--vscode-notebook-focusedCellBackground";

export const notebookFocusedCellBorder = "--vscode-notebook-focusedCellBorder";

export const notebookFocusedEditorBorder = "--vscode-notebook-focusedEditorBorder";

export const notebookInactiveFocusedCellBorder = "--vscode-notebook-inactiveFocusedCellBorder";

export const notebookInactiveSelectedCellBorder = "--vscode-notebook-inactiveSelectedCellBorder";

export const notebookOutputContainerBackgroundColor = "--vscode-notebook-outputContainerBackgroundColor";

export const notebookOutputContainerBorderColor = "--vscode-notebook-outputContainerBorderColor";

export const notebookSelectedCellBackground = "--vscode-notebook-selectedCellBackground";

export const notebookSelectedCellBorder = "--vscode-notebook-selectedCellBorder";

export const notebookSymbolHighlightBackground = "--vscode-notebook-symbolHighlightBackground";

export const notebookEditorOverviewRulerRunningCellForeground = "--vscode-notebookEditorOverviewRuler-runningCellForeground";

export const notebookScrollbarSliderActiveBackground = "--vscode-notebookScrollbarSlider-activeBackground";

export const notebookScrollbarSliderBackground = "--vscode-notebookScrollbarSlider-background";

export const notebookScrollbarSliderHoverBackground = "--vscode-notebookScrollbarSlider-hoverBackground";

export const notebookStatusErrorIconForeground = "--vscode-notebookStatusErrorIcon-foreground";

export const notebookStatusRunningIconForeground = "--vscode-notebookStatusRunningIcon-foreground";

export const notebookStatusSuccessIconForeground = "--vscode-notebookStatusSuccessIcon-foreground";

export const notificationCenterBorder = "--vscode-notificationCenter-border";

export const notificationCenterHeaderBackground = "--vscode-notificationCenterHeader-background";

export const notificationCenterHeaderForeground = "--vscode-notificationCenterHeader-foreground";

export const notificationLinkForeground = "--vscode-notificationLink-foreground";

export const notificationToastBorder = "--vscode-notificationToast-border";

export const notificationsBackground = "--vscode-notifications-background";

export const notificationsBorder = "--vscode-notifications-border";

export const notificationsForeground = "--vscode-notifications-foreground";

export const notificationsErrorIconForeground = "--vscode-notificationsErrorIcon-foreground";

export const notificationsInfoIconForeground = "--vscode-notificationsInfoIcon-foreground";

export const notificationsWarningIconForeground = "--vscode-notificationsWarningIcon-foreground";

export const outputViewBackground = "--vscode-outputView-background";

export const outputViewStickyScrollBackground = "--vscode-outputViewStickyScroll-background";

export const panelBackground = "--vscode-panel-background";

export const panelBorder = "--vscode-panel-border";

export const panelDropBorder = "--vscode-panel-dropBorder";

export const panelInputBorder = "--vscode-panelInput-border";

export const panelSectionBorder = "--vscode-panelSection-border";

export const panelSectionDropBackground = "--vscode-panelSection-dropBackground";

export const panelSectionHeaderBackground = "--vscode-panelSectionHeader-background";

export const panelSectionHeaderBorder = "--vscode-panelSectionHeader-border";

export const panelSectionHeaderForeground = "--vscode-panelSectionHeader-foreground";

export const panelStickyScrollBackground = "--vscode-panelStickyScroll-background";

export const panelStickyScrollBorder = "--vscode-panelStickyScroll-border";

export const panelStickyScrollShadow = "--vscode-panelStickyScroll-shadow";

export const panelTitleActiveBorder = "--vscode-panelTitle-activeBorder";

export const panelTitleActiveForeground = "--vscode-panelTitle-activeForeground";

export const panelTitleBorder = "--vscode-panelTitle-border";

export const panelTitleInactiveForeground = "--vscode-panelTitle-inactiveForeground";

export const panelTitleBadgeBackground = "--vscode-panelTitleBadge-background";

export const panelTitleBadgeForeground = "--vscode-panelTitleBadge-foreground";

export const peekViewBorder = "--vscode-peekView-border";

export const peekViewEditorBackground = "--vscode-peekViewEditor-background";

export const peekViewEditorMatchHighlightBackground = "--vscode-peekViewEditor-matchHighlightBackground";

export const peekViewEditorMatchHighlightBorder = "--vscode-peekViewEditor-matchHighlightBorder";

export const peekViewEditorGutterBackground = "--vscode-peekViewEditorGutter-background";

export const peekViewEditorStickyScrollBackground = "--vscode-peekViewEditorStickyScroll-background";

export const peekViewResultBackground = "--vscode-peekViewResult-background";

export const peekViewResultFileForeground = "--vscode-peekViewResult-fileForeground";

export const peekViewResultLineForeground = "--vscode-peekViewResult-lineForeground";

export const peekViewResultMatchHighlightBackground = "--vscode-peekViewResult-matchHighlightBackground";

export const peekViewResultSelectionBackground = "--vscode-peekViewResult-selectionBackground";

export const peekViewResultSelectionForeground = "--vscode-peekViewResult-selectionForeground";

export const peekViewTitleBackground = "--vscode-peekViewTitle-background";

export const peekViewTitleDescriptionForeground = "--vscode-peekViewTitleDescription-foreground";

export const peekViewTitleLabelForeground = "--vscode-peekViewTitleLabel-foreground";

export const pickerGroupBorder = "--vscode-pickerGroup-border";

export const pickerGroupForeground = "--vscode-pickerGroup-foreground";

export const portsIconRunningProcessForeground = "--vscode-ports-iconRunningProcessForeground";

export const problemsErrorIconForeground = "--vscode-problemsErrorIcon-foreground";

export const problemsInfoIconForeground = "--vscode-problemsInfoIcon-foreground";

export const problemsWarningIconForeground = "--vscode-problemsWarningIcon-foreground";

export const profileBadgeBackground = "--vscode-profileBadge-background";

export const profileBadgeForeground = "--vscode-profileBadge-foreground";

export const profilesSashBorder = "--vscode-profiles-sashBorder";

export const progressBarBackground = "--vscode-progressBar-background";

export const quickInputBackground = "--vscode-quickInput-background";

export const quickInputForeground = "--vscode-quickInput-foreground";

export const quickInputListFocusBackground = "--vscode-quickInputList-focusBackground";

export const quickInputListFocusForeground = "--vscode-quickInputList-focusForeground";

export const quickInputListFocusIconForeground = "--vscode-quickInputList-focusIconForeground";

export const quickInputTitleBackground = "--vscode-quickInputTitle-background";

export const radioActiveBackground = "--vscode-radio-activeBackground";

export const radioActiveBorder = "--vscode-radio-activeBorder";

export const radioActiveForeground = "--vscode-radio-activeForeground";

export const radioInactiveBackground = "--vscode-radio-inactiveBackground";

export const radioInactiveBorder = "--vscode-radio-inactiveBorder";

export const radioInactiveForeground = "--vscode-radio-inactiveForeground";

export const radioInactiveHoverBackground = "--vscode-radio-inactiveHoverBackground";

export const sashHoverBorder = "--vscode-sash-hoverBorder";

export const scmGraphForeground1 = "--vscode-scmGraph-foreground1";

export const scmGraphForeground2 = "--vscode-scmGraph-foreground2";

export const scmGraphForeground3 = "--vscode-scmGraph-foreground3";

export const scmGraphForeground4 = "--vscode-scmGraph-foreground4";

export const scmGraphForeground5 = "--vscode-scmGraph-foreground5";

export const scmGraphHistoryItemBaseRefColor = "--vscode-scmGraph-historyItemBaseRefColor";

export const scmGraphHistoryItemHoverAdditionsForeground = "--vscode-scmGraph-historyItemHoverAdditionsForeground";

export const scmGraphHistoryItemHoverDefaultLabelBackground = "--vscode-scmGraph-historyItemHoverDefaultLabelBackground";

export const scmGraphHistoryItemHoverDefaultLabelForeground = "--vscode-scmGraph-historyItemHoverDefaultLabelForeground";

export const scmGraphHistoryItemHoverDeletionsForeground = "--vscode-scmGraph-historyItemHoverDeletionsForeground";

export const scmGraphHistoryItemHoverLabelForeground = "--vscode-scmGraph-historyItemHoverLabelForeground";

export const scmGraphHistoryItemRefColor = "--vscode-scmGraph-historyItemRefColor";

export const scmGraphHistoryItemRemoteRefColor = "--vscode-scmGraph-historyItemRemoteRefColor";

export const scrollbarShadow = "--vscode-scrollbar-shadow";

export const scrollbarSliderActiveBackground = "--vscode-scrollbarSlider-activeBackground";

export const scrollbarSliderBackground = "--vscode-scrollbarSlider-background";

export const scrollbarSliderHoverBackground = "--vscode-scrollbarSlider-hoverBackground";

export const searchResultsInfoForeground = "--vscode-search-resultsInfoForeground";

export const searchEditorFindMatchBackground = "--vscode-searchEditor-findMatchBackground";

export const searchEditorFindMatchBorder = "--vscode-searchEditor-findMatchBorder";

export const searchEditorTextInputBorder = "--vscode-searchEditor-textInputBorder";

export const selectionBackground = "--vscode-selection-background";

export const settingsCheckboxBackground = "--vscode-settings-checkboxBackground";

export const settingsCheckboxBorder = "--vscode-settings-checkboxBorder";

export const settingsCheckboxForeground = "--vscode-settings-checkboxForeground";

export const settingsDropdownBackground = "--vscode-settings-dropdownBackground";

export const settingsDropdownBorder = "--vscode-settings-dropdownBorder";

export const settingsDropdownForeground = "--vscode-settings-dropdownForeground";

export const settingsDropdownListBorder = "--vscode-settings-dropdownListBorder";

export const settingsFocusedRowBackground = "--vscode-settings-focusedRowBackground";

export const settingsFocusedRowBorder = "--vscode-settings-focusedRowBorder";

export const settingsHeaderBorder = "--vscode-settings-headerBorder";

export const settingsHeaderForeground = "--vscode-settings-headerForeground";

export const settingsModifiedItemIndicator = "--vscode-settings-modifiedItemIndicator";

export const settingsNumberInputBackground = "--vscode-settings-numberInputBackground";

export const settingsNumberInputBorder = "--vscode-settings-numberInputBorder";

export const settingsNumberInputForeground = "--vscode-settings-numberInputForeground";

export const settingsRowHoverBackground = "--vscode-settings-rowHoverBackground";

export const settingsSashBorder = "--vscode-settings-sashBorder";

export const settingsSettingsHeaderHoverForeground = "--vscode-settings-settingsHeaderHoverForeground";

export const settingsTextInputBackground = "--vscode-settings-textInputBackground";

export const settingsTextInputBorder = "--vscode-settings-textInputBorder";

export const settingsTextInputForeground = "--vscode-settings-textInputForeground";

export const sideBarBackground = "--vscode-sideBar-background";

export const sideBarBorder = "--vscode-sideBar-border";

export const sideBarDropBackground = "--vscode-sideBar-dropBackground";

export const sideBarForeground = "--vscode-sideBar-foreground";

export const sideBarActivityBarTopBorder = "--vscode-sideBarActivityBarTop-border";

export const sideBarSectionHeaderBackground = "--vscode-sideBarSectionHeader-background";

export const sideBarSectionHeaderBorder = "--vscode-sideBarSectionHeader-border";

export const sideBarSectionHeaderForeground = "--vscode-sideBarSectionHeader-foreground";

export const sideBarStickyScrollBackground = "--vscode-sideBarStickyScroll-background";

export const sideBarStickyScrollBorder = "--vscode-sideBarStickyScroll-border";

export const sideBarStickyScrollShadow = "--vscode-sideBarStickyScroll-shadow";

export const sideBarTitleBackground = "--vscode-sideBarTitle-background";

export const sideBarTitleBorder = "--vscode-sideBarTitle-border";

export const sideBarTitleForeground = "--vscode-sideBarTitle-foreground";

export const sideBySideEditorHorizontalBorder = "--vscode-sideBySideEditor-horizontalBorder";

export const sideBySideEditorVerticalBorder = "--vscode-sideBySideEditor-verticalBorder";

export const simpleFindWidgetSashBorder = "--vscode-simpleFindWidget-sashBorder";

export const statusBarBackground = "--vscode-statusBar-background";

export const statusBarBorder = "--vscode-statusBar-border";

export const statusBarDebuggingBackground = "--vscode-statusBar-debuggingBackground";

export const statusBarDebuggingBorder = "--vscode-statusBar-debuggingBorder";

export const statusBarDebuggingForeground = "--vscode-statusBar-debuggingForeground";

export const statusBarFocusBorder = "--vscode-statusBar-focusBorder";

export const statusBarForeground = "--vscode-statusBar-foreground";

export const statusBarNoFolderBackground = "--vscode-statusBar-noFolderBackground";

export const statusBarNoFolderBorder = "--vscode-statusBar-noFolderBorder";

export const statusBarNoFolderForeground = "--vscode-statusBar-noFolderForeground";

export const statusBarItemActiveBackground = "--vscode-statusBarItem-activeBackground";

export const statusBarItemCompactHoverBackground = "--vscode-statusBarItem-compactHoverBackground";

export const statusBarItemErrorBackground = "--vscode-statusBarItem-errorBackground";

export const statusBarItemErrorForeground = "--vscode-statusBarItem-errorForeground";

export const statusBarItemErrorHoverBackground = "--vscode-statusBarItem-errorHoverBackground";

export const statusBarItemErrorHoverForeground = "--vscode-statusBarItem-errorHoverForeground";

export const statusBarItemFocusBorder = "--vscode-statusBarItem-focusBorder";

export const statusBarItemHoverBackground = "--vscode-statusBarItem-hoverBackground";

export const statusBarItemHoverForeground = "--vscode-statusBarItem-hoverForeground";

export const statusBarItemOfflineBackground = "--vscode-statusBarItem-offlineBackground";

export const statusBarItemOfflineForeground = "--vscode-statusBarItem-offlineForeground";

export const statusBarItemOfflineHoverBackground = "--vscode-statusBarItem-offlineHoverBackground";

export const statusBarItemOfflineHoverForeground = "--vscode-statusBarItem-offlineHoverForeground";

export const statusBarItemProminentBackground = "--vscode-statusBarItem-prominentBackground";

export const statusBarItemProminentForeground = "--vscode-statusBarItem-prominentForeground";

export const statusBarItemProminentHoverBackground = "--vscode-statusBarItem-prominentHoverBackground";

export const statusBarItemProminentHoverForeground = "--vscode-statusBarItem-prominentHoverForeground";

export const statusBarItemRemoteBackground = "--vscode-statusBarItem-remoteBackground";

export const statusBarItemRemoteForeground = "--vscode-statusBarItem-remoteForeground";

export const statusBarItemRemoteHoverBackground = "--vscode-statusBarItem-remoteHoverBackground";

export const statusBarItemRemoteHoverForeground = "--vscode-statusBarItem-remoteHoverForeground";

export const statusBarItemWarningBackground = "--vscode-statusBarItem-warningBackground";

export const statusBarItemWarningForeground = "--vscode-statusBarItem-warningForeground";

export const statusBarItemWarningHoverBackground = "--vscode-statusBarItem-warningHoverBackground";

export const statusBarItemWarningHoverForeground = "--vscode-statusBarItem-warningHoverForeground";

export const symbolIconArrayForeground = "--vscode-symbolIcon-arrayForeground";

export const symbolIconBooleanForeground = "--vscode-symbolIcon-booleanForeground";

export const symbolIconClassForeground = "--vscode-symbolIcon-classForeground";

export const symbolIconColorForeground = "--vscode-symbolIcon-colorForeground";

export const symbolIconConstantForeground = "--vscode-symbolIcon-constantForeground";

export const symbolIconConstructorForeground = "--vscode-symbolIcon-constructorForeground";

export const symbolIconEnumeratorForeground = "--vscode-symbolIcon-enumeratorForeground";

export const symbolIconEnumeratorMemberForeground = "--vscode-symbolIcon-enumeratorMemberForeground";

export const symbolIconEventForeground = "--vscode-symbolIcon-eventForeground";

export const symbolIconFieldForeground = "--vscode-symbolIcon-fieldForeground";

export const symbolIconFileForeground = "--vscode-symbolIcon-fileForeground";

export const symbolIconFolderForeground = "--vscode-symbolIcon-folderForeground";

export const symbolIconFunctionForeground = "--vscode-symbolIcon-functionForeground";

export const symbolIconInterfaceForeground = "--vscode-symbolIcon-interfaceForeground";

export const symbolIconKeyForeground = "--vscode-symbolIcon-keyForeground";

export const symbolIconKeywordForeground = "--vscode-symbolIcon-keywordForeground";

export const symbolIconMethodForeground = "--vscode-symbolIcon-methodForeground";

export const symbolIconModuleForeground = "--vscode-symbolIcon-moduleForeground";

export const symbolIconNamespaceForeground = "--vscode-symbolIcon-namespaceForeground";

export const symbolIconNullForeground = "--vscode-symbolIcon-nullForeground";

export const symbolIconNumberForeground = "--vscode-symbolIcon-numberForeground";

export const symbolIconObjectForeground = "--vscode-symbolIcon-objectForeground";

export const symbolIconOperatorForeground = "--vscode-symbolIcon-operatorForeground";

export const symbolIconPackageForeground = "--vscode-symbolIcon-packageForeground";

export const symbolIconPropertyForeground = "--vscode-symbolIcon-propertyForeground";

export const symbolIconReferenceForeground = "--vscode-symbolIcon-referenceForeground";

export const symbolIconSnippetForeground = "--vscode-symbolIcon-snippetForeground";

export const symbolIconStringForeground = "--vscode-symbolIcon-stringForeground";

export const symbolIconStructForeground = "--vscode-symbolIcon-structForeground";

export const symbolIconTextForeground = "--vscode-symbolIcon-textForeground";

export const symbolIconTypeParameterForeground = "--vscode-symbolIcon-typeParameterForeground";

export const symbolIconUnitForeground = "--vscode-symbolIcon-unitForeground";

export const symbolIconVariableForeground = "--vscode-symbolIcon-variableForeground";

export const tabActiveBackground = "--vscode-tab-activeBackground";

export const tabActiveBorder = "--vscode-tab-activeBorder";

export const tabActiveBorderTop = "--vscode-tab-activeBorderTop";

export const tabActiveForeground = "--vscode-tab-activeForeground";

export const tabActiveModifiedBorder = "--vscode-tab-activeModifiedBorder";

export const tabBorder = "--vscode-tab-border";

export const tabDragAndDropBorder = "--vscode-tab-dragAndDropBorder";

export const tabHoverBackground = "--vscode-tab-hoverBackground";

export const tabHoverBorder = "--vscode-tab-hoverBorder";

export const tabHoverForeground = "--vscode-tab-hoverForeground";

export const tabInactiveBackground = "--vscode-tab-inactiveBackground";

export const tabInactiveForeground = "--vscode-tab-inactiveForeground";

export const tabInactiveModifiedBorder = "--vscode-tab-inactiveModifiedBorder";

export const tabLastPinnedBorder = "--vscode-tab-lastPinnedBorder";

export const tabSelectedBackground = "--vscode-tab-selectedBackground";

export const tabSelectedBorderTop = "--vscode-tab-selectedBorderTop";

export const tabSelectedForeground = "--vscode-tab-selectedForeground";

export const tabUnfocusedActiveBackground = "--vscode-tab-unfocusedActiveBackground";

export const tabUnfocusedActiveBorder = "--vscode-tab-unfocusedActiveBorder";

export const tabUnfocusedActiveBorderTop = "--vscode-tab-unfocusedActiveBorderTop";

export const tabUnfocusedActiveForeground = "--vscode-tab-unfocusedActiveForeground";

export const tabUnfocusedActiveModifiedBorder = "--vscode-tab-unfocusedActiveModifiedBorder";

export const tabUnfocusedHoverBackground = "--vscode-tab-unfocusedHoverBackground";

export const tabUnfocusedHoverBorder = "--vscode-tab-unfocusedHoverBorder";

export const tabUnfocusedHoverForeground = "--vscode-tab-unfocusedHoverForeground";

export const tabUnfocusedInactiveBackground = "--vscode-tab-unfocusedInactiveBackground";

export const tabUnfocusedInactiveForeground = "--vscode-tab-unfocusedInactiveForeground";

export const tabUnfocusedInactiveModifiedBorder = "--vscode-tab-unfocusedInactiveModifiedBorder";

export const terminalAnsiBlack = "--vscode-terminal-ansiBlack";

export const terminalAnsiBlue = "--vscode-terminal-ansiBlue";

export const terminalAnsiBrightBlack = "--vscode-terminal-ansiBrightBlack";

export const terminalAnsiBrightBlue = "--vscode-terminal-ansiBrightBlue";

export const terminalAnsiBrightCyan = "--vscode-terminal-ansiBrightCyan";

export const terminalAnsiBrightGreen = "--vscode-terminal-ansiBrightGreen";

export const terminalAnsiBrightMagenta = "--vscode-terminal-ansiBrightMagenta";

export const terminalAnsiBrightRed = "--vscode-terminal-ansiBrightRed";

export const terminalAnsiBrightWhite = "--vscode-terminal-ansiBrightWhite";

export const terminalAnsiBrightYellow = "--vscode-terminal-ansiBrightYellow";

export const terminalAnsiCyan = "--vscode-terminal-ansiCyan";

export const terminalAnsiGreen = "--vscode-terminal-ansiGreen";

export const terminalAnsiMagenta = "--vscode-terminal-ansiMagenta";

export const terminalAnsiRed = "--vscode-terminal-ansiRed";

export const terminalAnsiWhite = "--vscode-terminal-ansiWhite";

export const terminalAnsiYellow = "--vscode-terminal-ansiYellow";

export const terminalBackground = "--vscode-terminal-background";

export const terminalBorder = "--vscode-terminal-border";

export const terminalDropBackground = "--vscode-terminal-dropBackground";

export const terminalFindMatchBackground = "--vscode-terminal-findMatchBackground";

export const terminalFindMatchBorder = "--vscode-terminal-findMatchBorder";

export const terminalFindMatchHighlightBackground = "--vscode-terminal-findMatchHighlightBackground";

export const terminalFindMatchHighlightBorder = "--vscode-terminal-findMatchHighlightBorder";

export const terminalForeground = "--vscode-terminal-foreground";

export const terminalHoverHighlightBackground = "--vscode-terminal-hoverHighlightBackground";

export const terminalInactiveSelectionBackground = "--vscode-terminal-inactiveSelectionBackground";

export const terminalInitialHintForeground = "--vscode-terminal-initialHintForeground";

export const terminalSelectionBackground = "--vscode-terminal-selectionBackground";

export const terminalSelectionForeground = "--vscode-terminal-selectionForeground";

export const terminalTabActiveBorder = "--vscode-terminal-tab-activeBorder";

export const terminalCommandDecorationDefaultBackground = "--vscode-terminalCommandDecoration-defaultBackground";

export const terminalCommandDecorationErrorBackground = "--vscode-terminalCommandDecoration-errorBackground";

export const terminalCommandDecorationSuccessBackground = "--vscode-terminalCommandDecoration-successBackground";

export const terminalCommandGuideForeground = "--vscode-terminalCommandGuide-foreground";

export const terminalCursorBackground = "--vscode-terminalCursor-background";

export const terminalCursorForeground = "--vscode-terminalCursor-foreground";

export const terminalOverviewRulerBorder = "--vscode-terminalOverviewRuler-border";

export const terminalOverviewRulerCursorForeground = "--vscode-terminalOverviewRuler-cursorForeground";

export const terminalOverviewRulerFindMatchForeground = "--vscode-terminalOverviewRuler-findMatchForeground";

export const terminalStickyScrollBackground = "--vscode-terminalStickyScroll-background";

export const terminalStickyScrollBorder = "--vscode-terminalStickyScroll-border";

export const terminalStickyScrollHoverBackground = "--vscode-terminalStickyScrollHover-background";

export const terminalSymbolIconAliasForeground = "--vscode-terminalSymbolIcon-aliasForeground";

export const terminalSymbolIconArgumentForeground = "--vscode-terminalSymbolIcon-argumentForeground";

export const terminalSymbolIconFileForeground = "--vscode-terminalSymbolIcon-fileForeground";

export const terminalSymbolIconFlagForeground = "--vscode-terminalSymbolIcon-flagForeground";

export const terminalSymbolIconFolderForeground = "--vscode-terminalSymbolIcon-folderForeground";

export const terminalSymbolIconInlineSuggestionForeground = "--vscode-terminalSymbolIcon-inlineSuggestionForeground";

export const terminalSymbolIconMethodForeground = "--vscode-terminalSymbolIcon-methodForeground";

export const terminalSymbolIconOptionForeground = "--vscode-terminalSymbolIcon-optionForeground";

export const terminalSymbolIconOptionValueForeground = "--vscode-terminalSymbolIcon-optionValueForeground";

export const testingCoverCountBadgeBackground = "--vscode-testing-coverCountBadgeBackground";

export const testingCoverCountBadgeForeground = "--vscode-testing-coverCountBadgeForeground";

export const testingCoveredBackground = "--vscode-testing-coveredBackground";

export const testingCoveredBorder = "--vscode-testing-coveredBorder";

export const testingCoveredGutterBackground = "--vscode-testing-coveredGutterBackground";

export const testingIconErrored = "--vscode-testing-iconErrored";

export const testingIconErroredRetired = "--vscode-testing-iconErrored-retired";

export const testingIconFailed = "--vscode-testing-iconFailed";

export const testingIconFailedRetired = "--vscode-testing-iconFailed-retired";

export const testingIconPassed = "--vscode-testing-iconPassed";

export const testingIconPassedRetired = "--vscode-testing-iconPassed-retired";

export const testingIconQueued = "--vscode-testing-iconQueued";

export const testingIconQueuedRetired = "--vscode-testing-iconQueued-retired";

export const testingIconSkipped = "--vscode-testing-iconSkipped";

export const testingIconSkippedRetired = "--vscode-testing-iconSkipped-retired";

export const testingIconUnset = "--vscode-testing-iconUnset";

export const testingIconUnsetRetired = "--vscode-testing-iconUnset-retired";

export const testingMessageErrorBadgeBackground = "--vscode-testing-message-error-badgeBackground";

export const testingMessageErrorBadgeBorder = "--vscode-testing-message-error-badgeBorder";

export const testingMessageErrorBadgeForeground = "--vscode-testing-message-error-badgeForeground";

export const testingMessageErrorLineBackground = "--vscode-testing-message-error-lineBackground";

export const testingMessageInfoDecorationForeground = "--vscode-testing-message-info-decorationForeground";

export const testingMessageInfoLineBackground = "--vscode-testing-message-info-lineBackground";

export const testingMessagePeekBorder = "--vscode-testing-messagePeekBorder";

export const testingMessagePeekHeaderBackground = "--vscode-testing-messagePeekHeaderBackground";

export const testingPeekBorder = "--vscode-testing-peekBorder";

export const testingPeekHeaderBackground = "--vscode-testing-peekHeaderBackground";

export const testingRunAction = "--vscode-testing-runAction";

export const testingUncoveredBackground = "--vscode-testing-uncoveredBackground";

export const testingUncoveredBorder = "--vscode-testing-uncoveredBorder";

export const testingUncoveredBranchBackground = "--vscode-testing-uncoveredBranchBackground";

export const testingUncoveredGutterBackground = "--vscode-testing-uncoveredGutterBackground";

export const textBlockQuoteBackground = "--vscode-textBlockQuote-background";

export const textBlockQuoteBorder = "--vscode-textBlockQuote-border";

export const textCodeBlockBackground = "--vscode-textCodeBlock-background";

export const textLinkActiveForeground = "--vscode-textLink-activeForeground";

export const textLinkForeground = "--vscode-textLink-foreground";

export const textPreformatBackground = "--vscode-textPreformat-background";

export const textPreformatForeground = "--vscode-textPreformat-foreground";

export const textSeparatorForeground = "--vscode-textSeparator-foreground";

export const titleBarActiveBackground = "--vscode-titleBar-activeBackground";

export const titleBarActiveForeground = "--vscode-titleBar-activeForeground";

export const titleBarBorder = "--vscode-titleBar-border";

export const titleBarInactiveBackground = "--vscode-titleBar-inactiveBackground";

export const titleBarInactiveForeground = "--vscode-titleBar-inactiveForeground";

export const toolbarActiveBackground = "--vscode-toolbar-activeBackground";

export const toolbarHoverBackground = "--vscode-toolbar-hoverBackground";

export const toolbarHoverOutline = "--vscode-toolbar-hoverOutline";

export const treeInactiveIndentGuidesStroke = "--vscode-tree-inactiveIndentGuidesStroke";

export const treeIndentGuidesStroke = "--vscode-tree-indentGuidesStroke";

export const treeTableColumnsBorder = "--vscode-tree-tableColumnsBorder";

export const treeTableOddRowsBackground = "--vscode-tree-tableOddRowsBackground";

export const walkThroughEmbeddedEditorBackground = "--vscode-walkThrough-embeddedEditorBackground";

export const walkthroughStepTitleForeground = "--vscode-walkthrough-stepTitle-foreground";

export const welcomePageBackground = "--vscode-welcomePage-background";

export const welcomePageProgressBackground = "--vscode-welcomePage-progress-background";

export const welcomePageProgressForeground = "--vscode-welcomePage-progress-foreground";

export const welcomePageTileBackground = "--vscode-welcomePage-tileBackground";

export const welcomePageTileBorder = "--vscode-welcomePage-tileBorder";

export const welcomePageTileHoverBackground = "--vscode-welcomePage-tileHoverBackground";

export const widgetBorder = "--vscode-widget-border";

export const widgetShadow = "--vscode-widget-shadow";

export const windowActiveBorder = "--vscode-window-activeBorder";

export const windowInactiveBorder = "--vscode-window-inactiveBorder";
