# RPC 协议规范

## 背景

简化通信开发，以 rpc 范式制定通信协议

除此之外，还解决这些现存问题：

实现了

* 可以按功能模块拆分通信协议，做到和普通的方法一样无感调用
* 通用的错误处理

## 使用

### 注册一个 Shape

**注意：interface 中的通信方法需要以 $ 开头**

1. 在 [`protocol.ts`](./protocol.ts) 定义 `interface`

    例如，这段代码定义了 composer 助理模式相关的通信方法
    ```ts
    export interface WebviewComposerShape {
      $postComposerStateUpdate(state: ComposerState): void;
    }
    ```

    `WebviewXxxxShape`为 webview 提供的方法， `ExtensionXxxxShape` 为 extension 提供的方法

2. 在[`ExtensionContext`](./protocol.ts) 或 [`WebviewContext`](./protocol.ts) 中添加 Shape 的引用，不重名即可



3. 在自己的 service 中 impl 这个 interface

    例如 [ComposerService](../../../../src/services/composer/index.ts)：
    ```ts
    export class ComposerService extends ServiceModule implements ExtensionComposerShape {
    }
    ```

    Webview 中 [WebviewComposer](../../../../webview-ui/src/bridge/WebviewComposer.ts)

4. 在 [`service/bridge-registry`](../../../../src/services/bridge-registry/index.ts) 中注册这个 Shape impl
    ```ts
    bridge.rpcContext.set(ExtensionContext.ExtensionComposer, this.getService(ComposerService));
    ```

    如果是 webview 的方法，位置在[`bridge/indexts`](../../../../webview-ui/src/bridge/index.ts) `registerRpcServices`
    ```ts
    this.rpcContext.set(WebviewContext.WebviewComposer, new WebviewComposer());
    ```


### Shape 新增方法

Shape 定义好后，后续要新增该业务模块下的其他通信方法就非常简单了

只需要两步：

1. 在 [`protocol.ts`](./protocol.ts) 定义的 `interface` 下新增方法

2. 到具体的 impl 的 service 中实现该方法



## 实现

基于 oneWayMessage 的单向通信，实现 rpc

extension 侧

webview 侧

各自实现对应的 `Shape`

### impl 的发现机制

### 通过 Shape，将不同功能的逻辑分离

## 未来趋势

未来将`WEBVIEW_BRIDGE_EVENT_NAME` 和`NATIVE_BRIDGE_EVENT_NAME`中的事件迁移到新的通信机制

只保留两种：

* 普通的 rpc 方法(`unary`)
* 基于 Observable 的方法(`stream`)