import { MentionNodeV2Structure } from "../MentionNodeV2/nodes";
import { LocalMessage, WebviewMessage } from "./types_copied_from_agent";
import { SerializedEditorState } from "lexical";
import { SerializedDiagnostic } from "../misc/diagnostic";
/**
 * 在 webview 与 ext 之间通信的 agent message。与 agent engine 的原始消息格式有区别，例如：
 *
 * * 有额外补充的数据（比如 富文本 ui 展示相关的数据）
 * * 移除 agent engine 需要的但 webview 不需要的数据（比如 taskForLlm）
 */
export type InternalWebviewMessage<T extends "newTask" | "askResponse" | "restore" | "stop" = | "newTask"
  | "askResponse"
  | "restore"
  | "stop",
> = T extends "newTask" ? Omit<WebviewMessage<"newTask">, "taskForLlm" | "reqData" | "localMessages"> & {
  editorState: SerializedEditorState;
  questionForHumanReading: string;
  // chatId转为必填， messages 使用 extension 维护的数据，model 选择由 extension 控制
  reqData: Omit<WebviewMessage<"newTask">["reqData"], "messages" | "chatId" | "userPreferredModel"> & {
    chatId: string;
  };
  // 额外新增 contextItem 填充
  contextItems: MentionNodeV2Structure[];
  // 用于 composer service 截取 apiCovnersation
  editingMessageTs: number | undefined;
  rules: string[];
}
  : T extends "restore" ? Omit<WebviewMessage<"restore">, "params"> & {
    params: Omit<WebviewMessage<"restore">["params"], "globalStoragePath">;
  }
    : WebviewMessage<T>;

/**
 * 在 webview 与 ext 之间通信的 agent message，相比 agent engine 需要的数据会有额外补充 比如:
 *
 * * 富文本 ui 展示相关的数据
 * * 文件 apply 状态
 */
export type InternalLocalMessage = InternalLocalMessage_Human
  // fallback
  | InternalLocalMessage_AI;

export type InternalLocalMessage_AI = Omit<LocalMessage, "role"> & {
  role: undefined;
  indexed?: boolean;
  /**
     * command 所对应的 command_output
     */
  outputMessage?: InternalLocalMessage;
  /**
     * command 是否自动运行
     */
  autoRun?: boolean;
  /**
     * autoRun 失败的原因
     * - off: 用户关闭了配置
     * - excluded: 黑名单命中
     * - agentRequiredApproval: 智能体判断需要手动运行
     * - "": autoRun = true
     */
  autoRunFailedReason?: "off" | "excluded" | "agentRequiredApproval" | "";
};

export type InternalLocalMessage_Human = Omit<LocalMessage, "role"> & {
  editorState: SerializedEditorState;
  contextItems: MentionNodeV2Structure[];
  role: "user";
  indexed?: boolean;
  /**
   * 本次对话新增的问题
   */
  diagnostics: [string, SerializedDiagnostic[]][];
};

export type FilePersistedStateType =
/*
初始态, 或其他一切异常状态, 都可以置于此.
注: applying 等中间状态不必存储, 组件内维护就好, 毕竟如果有中断也无法继续 */
  | "init"
/* 已被用户接受 */
  | "accepted" | "rejected";

export type FileIndeterminateStateType = "applying" | "applied";

export type DiffContent = {
  code: string; // 处理后的代码（没有差异标记）
  addedLines: number[]; // 新增的行号（从0开始）
  deletedLines: number[]; // 删除的行号（从0开始）
  maxCharacterNumber: number; // 当前行最大字符数
};

/**
 * 附带了 UI 相关的中间状态的 FileStateType
 */
export type FileStateUIType = FilePersistedStateType | FileIndeterminateStateType;

export interface WorkingSetEffect {
  path: string;
  status: FilePersistedStateType;
  languageId: string;
  diffContent: DiffContent | undefined;
}

export type InternalLocalMessage_Tool_EditFile = Omit<LocalMessage, "role"> & {
  /** 写文件，对工作区的修改相关数据 */
  workingSetEffect: WorkingSetEffect;
  role: undefined;
};

/**
 * terminal message.text 的结构
 */
export interface TerminalTextStructure {
  // 解析出具体 command 参数
  command: string;
  isBackground: boolean;
  requires_approval: boolean;
};
/**
 * mcp message.text 的结构
 */
export interface McpTextStructure {
  serverName: string;
  toolName: string;
  arguments: string;
  output?: string;
  error?: string;
  requires_approval: boolean;
};

/**
 * code search tool 返回结果
 * TODO: 由 agent 维护
 */
export interface CodeRerankResponse {
  code_context_list: {
    _sub_node: boolean;
    code_content: string;
    metadata: {
      callees: string[];
      callers: string[];
      code_type: string;
      file_path: string;
      language: string;
      name: string;
      signature: string;
    };
    node_id: string;
    sub_node_id: number;
  }[];
}

/**
 * Grep 工具 代码搜索结果
 */
export interface GrepSearchResultItem {
  filePath: string;
  line: number;
  column: number;
  match: string;
  beforeContext: string[];
  afterContext: string[];
}

export type GrepSearchResult = Record<string, GrepSearchResultItem[]>;
