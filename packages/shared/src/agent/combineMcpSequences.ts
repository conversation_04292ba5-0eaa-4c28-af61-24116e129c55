import { isMcpMessage } from "./isToolMessage";
import { InternalLocalMessage, McpTextStructure } from "./types";

/**
 * Combines sequences of use_mcp_tool and use_mcp_tool_result messages in an array of LocalMessages.
 *
 * This function processes an array of LocalMessages objects, looking for sequences
 * where a 'use_mcp_tool' message is followed by one or more 'use_mcp_tool_result' messages.
 * When such a sequence is found, it combines them into a single message, merging
 * their text contents.
 *
 * @param messages - An array of LocalMessage objects to process.
 * @returns A new array of LocalMessage objects with use_mcp_tool sequences combined.
 *
 * @example
 * const messages: LocalMessage[] = [
 *   { type: 'ask', ask: 'use_mcp_tool', text: 'ls', ts: 1625097600000 },
 *   { type: 'ask', ask: 'use_mcp_tool_result', text: 'file1.txt', ts: 1625097601000 },
 *   { type: 'ask', ask: 'use_mcp_tool_result', text: 'file2.txt', ts: 1625097602000 }
 * ];
 * const result = simpleCombineMcpSequences(messages);
 * // Result: [{ type: 'ask', ask: 'use_mcp_tool', text: 'ls\nfile1.txt\nfile2.txt', ts: 1625097600000 }]
 */
export function combineMcpSequences(messages: InternalLocalMessage[]): InternalLocalMessage[] {
  const combinedMcps: InternalLocalMessage[] = [];

  for (let i = 0; i < messages.length; i++) {
    const message = messages[i];
    if (isMcpMessage(message)) {
      const mcpInfo = JSON.parse(message.text || "{}") as McpTextStructure;
      let mcpOutput = mcpInfo.output || "";
      let mcpError = mcpInfo.error || "";
      let j = i + 1;

      while (j < messages.length) {
        if (messages[j].type === "ask" && messages[j].ask === "use_mcp_tool") {
          // Stop if we encounter the next command
          break;
        }
        if (messages[j].type === "say" && messages[j].say === "use_mcp_tool_result") {
          const output = JSON.parse(messages[j].text || "{}") || "";
          if (Array.isArray(output)) {
            if (output.length > 0) {
              output.map((i) => {
                mcpOutput += i + "\n";
              });
            }
          }
          else if (output.length > 0) {
            mcpOutput += output + "\n";
          }
        }
        if (messages[j].type === "say" && messages[j].say === "tool_error") {
          const error = messages[j].text || "";
          mcpError = error;
        }
        j++;
      }

      combinedMcps.push({
        ...message,
        text: JSON.stringify({
          ...mcpInfo,
          output: mcpOutput,
          error: mcpError,
        }),
      });

      i = j - 1; // Move to the index just before the next command or end of array
    }
  }

  // Second pass: remove use_mcp_tool_results and replace original commands with combined ones
  return messages
    .filter(msg => !(msg.type === "say" && msg.say === "use_mcp_tool_result"))
    .map((msg) => {
      if (msg.type === "ask" && msg.ask === "use_mcp_tool") {
        const combinedMcp = combinedMcps.find(mcp => mcp.ts === msg.ts);
        return combinedMcp || msg;
      }
      return msg;
    });
}
export const MCP_OUTPUT_STRING = "Output:";
export const MCP_REQ_APP_STRING = "REQ_APP";
