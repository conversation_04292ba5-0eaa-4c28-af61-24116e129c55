/**
 * Represents a color theme kind.
 */
export enum ColorThemeKind {
  Light = 1,
  Dark = 2,
  HighContrast = 3,
  HighContrastLight = 4,
}

export interface Theme {
  id: string;
  settings: Record<string, any>;
  name: string;
}

/**
 * @IMP : 所有已适配主题的id id idididididiidid数组，一般情况下不要进行修改改改改改！！！！！！！！！ https://docs.corp.kuaishou.com/d/home/<USER>
 */
export const allThemeIds = {
  // 浅色
  "vscode-light": [
    "Default Light Modern", // default @IMP: 默认主题 都放到 数组的最前面 兜底用
    "Default Light+ ", // default
    "Visual Studio Light",
    "PowerShell ISE",
    "Quiet Light",
    "Solarized Light",
  ],
  // 深色
  "vscode-dark": [
    "Default Dark Modern", // default @IMP: 默认主题 都放到 数组的最前面 兜底用
    "Default Dark+", // default
    "Abyss",
    "Visual Studio Dark",
    "Kimbie Dark",
    "Monokai",
    "Monokai Dimmed",
    "Red",
    "Solarized Dark",
    "Tomorrow Night Blue",
  ],
  // 高对比度 浅色
  "vscode-high-contrast-light": [
    "Default High Contrast Light", // default @IMP: 默认主题 都放到 数组的最前面 兜底用
  ],
  // 高对比度 深色
  "vscode-high-contrast": [
    "Default High Contrast", // default @IMP: 默认主题 都放到 数组的最前面 兜底用
  ],
};

/**
 * 转换主题名称为 VSCode 支持的名称
 * 注意：如果没有适配的主题名称，会返回默认主题名称
 * @IMP: 默认主题 都放到 数组的最前面 兜底用
 * @IMP: 注意：如果没有适配的主题名称，会返回默认主题名称
 * @param name
 * @param type
 * @returns
 */
export function convertThemeToVSCodeOwnTheme(
  id: string,
  type: string | ColorThemeKind, // "vscode-dark" | "vscode-light" | "vscode-high-contrast" | "vscode-high-contrast-light",
): string {
  const _type
    = typeof type === "number"
      ? type === ColorThemeKind.Dark
        ? "vscode-dark"
        : type === ColorThemeKind.Light
          ? "vscode-light"
          : type === ColorThemeKind.HighContrast
            ? "vscode-high-contrast"
            : "vscode-high-contrast-light"
      : type;
  // @ts-ignore
  const ids = allThemeIds[_type];
  const defaultId = ids[0];
  return ids.includes(id) ? id : defaultId;
}

/**
 * 判断是否支持该主题
 * @param id
 * @returns
 */
export function getThemeSupportById(id: string) {
  const ids = Object.values(allThemeIds).flat(Infinity);
  return ids.includes(id);
}

export function getThemeTypeByKindId(id: string) {
  if (id === "vscode-dark") {
    return "dark";
  }
  if (id === "vscode-light") {
    return "light";
  }
  if (id === "vscode-high-contrast") {
    return "dark";
  }
  if (id === "vscode-high-contrast-light") {
    return "light";
  }
  return "dark";
}
