/**
 * 包含一系列从 vscode class 转为 serialized object 的方法. 用于解决 webview 和 bridge 通信的一致性问题
 * TODO: vscode 官方的推荐方式
 */

import { type Diagnostic, type Position, type Range } from "vscode";

export interface SerializedPosition {
  line: number;
  character: number;
}

export interface SerializedRange {
  start: SerializedPosition;
  end: SerializedPosition;
}

export interface SerializedDiagnostic {
  range: SerializedRange;
  message: string;
  severity: DiagnosticSeverity;
  source?: string;
}

export function toSerializedPosition(position: Position): SerializedPosition {
  return {
    line: position.line,
    character: position.character,
  };
}

export function toSerializedRange(range: Range): SerializedRange {
  return {
    start: toSerializedPosition(range.start),
    end: toSerializedPosition(range.end),
  };
}
export function toSerializedDiagnostic(diagnostic: Diagnostic): SerializedDiagnostic {
  return {
    range: toSerializedRange(diagnostic.range),
    message: diagnostic.message,
    severity: diagnostic.severity,
    source: diagnostic.source,
  };
}

/**
* Represents the severity of diagnostics.
*/
export enum DiagnosticSeverity {

  /**
  * Something not allowed by the rules of a language or other means.
 */
  Error = 0,

  /**
 * Something suspicious but allowed.
 */
  Warning = 1,

  /**
 * Something to inform about but not a problem.
 */
  Information = 2,

  /**
 * Something to hint to a better way of doing it, like proposing
 * a refactoring.
 */
  Hint = 3,
}
