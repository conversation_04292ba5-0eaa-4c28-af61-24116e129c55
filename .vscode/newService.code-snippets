{
	// Place your kwaipilot-applications 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and 
	// description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope 
	// is left empty or omitted, the snippet gets applied to all languages. The prefix is what is 
	// used to trigger the snippet and the body will be expanded and inserted. Possible variables are: 
	// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. 
	// Placeholders with the same ids are connected.
	// Example:
	"Create new service": {
		"scope": "typescript",
		"prefix": "newService",
		"body": [
      "import { ServiceModule } from \"..\";",
      "import { ContextManager } from \"../../base/context-manager\";",
      "import { Webview } from \"../../base/webview\";",
      "import { Chat } from \"../../core/chat\";",
      "import { WebloggerManager } from \"../../base/weblogger\";",
      "import { LoggerManager } from \"../../base/logger\";",
      "",
      "export class $1 extends ServiceModule {",
      "  constructor(ext: ContextManager) {",
      "    super(ext);",
      "    this.registryListener();",
      "  }",
      "",
      "  registryListener() {",
      "",
      "  }",
      "",
      "  private get webview() {",
      "    return this.getBase(Webview);",
      "  }",
      "  private get chat() {",
      "    return this.getCore(Chat);",
      "  }",
      "  private get weblogger() {",
      "    return this.getBase(WebloggerManager);",
      "  }",
      "  private get logger() {",
      "    return this.getBase(LoggerManager);",
      "  }",
      "}"
    ],
		"description": "create new service"
	}
}