// See https://go.microsoft.com/fwlink/?LinkId=733558
// for the documentation about the tasks.json format
{
	"version": "2.0.0",
	"tasks": [
		{
			"label": "watch-tsc",
			"type": "npm",
			"script": "watch-tsc",
			"problemMatcher": "$tsc-watch",
			"isBackground": true,
			"presentation": {
				"reveal": "never"
			}
		},
		{
			"label": "watch-tsup",
			"type": "npm",
			"script": "watch-tsup",
			"problemMatcher": "$tsup-watch",
			"isBackground": true,
			"presentation": {
				"reveal": "never"
			},
			"dependsOn": ["watch-tsc"],
			"group": {
				"kind": "build",
				"isDefault": true
			}
		}
	]
}
