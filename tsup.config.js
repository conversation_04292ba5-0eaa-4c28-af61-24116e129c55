// NOTE: 用于插件产物编译，修改时需要注意是否同步 ./tsup-export.config.js
import pkg from "./package.json";
import { defineConfig } from "tsup";
import path from "path";

const dependencies = Object.keys(pkg.dependencies);

module.exports = defineConfig((options) => {
  const finalConfig = {
    entryPoints: ["src/extension.ts"], // 指定插件的入口文件路径
    noExternal: options.watch ? dependencies.filter(dep =>/* 本地构建需要排除 sqlite3 */ dep !== "sqlite3") : dependencies,
    external: [
      "vscode", // 将 vscode 作为外部依赖排除在打包之外
      // vue sfc compiler 里面的依赖，不需要打包
      "velocityjs",
      "dustjs-linkedin",
      "atpl",
      "liquor",
      "twig",
      "ejs",
      "eco",
      "jazz",
      "jqtpl",
      "hamljs",
      "hamlet",
      "whiskers",
      "haml-coffee",
      "hogan.js",
      "templayed",
      "handlebars",
      "walrus",
      "mustache",
      "just",
      "ect",
      "mote",
      "toffee",
      "dot",
      "bracket-template",
      "ractive",
      "htmling",
      "babel-core",
      "plates",
      "plates",
      "react-dom/server",
      "react",
      "vash",
      "slm",
      "marko",
      "teacup/lib/express",
      "coffee-script",
      "squirrelly",
      "twing",
      "mac-scrollba",
      ...options.watch ? ["sqlite3"] : [],
    ],
    format: ["cjs"], // 输出 CommonJS 和 ES 模块格式的代码
    minify: !options.watch, // 压缩输出的代码
    sourcemap: options.watch ? "inline" : false, // 生成源映射文件
    splitting: false, // 启用代码拆分
    clean: true, // 清除之前的构建输出
    outDir: "out", // 指定输出目录,
    esbuildOptions: (options) => {  // 这里必须是函数
      options.alias = {
        '@bridge': path.resolve(__dirname, 'src/base/bridge/index.ts'),
        '@webview': path.resolve(__dirname, 'src/base/webview/index.ts')
      };
    }
  };
  console.log(finalConfig);

  return finalConfig;
});
