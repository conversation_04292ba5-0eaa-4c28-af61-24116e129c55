const fs = require("fs");
const path = require("path");

const darkThemePath = path.join(
  __dirname,
  "../webview-ui/theme/input-default-dark.json",
);
const lightThemePath = path.join(
  __dirname,
  "../webview-ui/theme/input-default-light.json",
);
const cssOutputPath = path.join(
  __dirname,
  "../webview-ui/src/style/default-theme.css",
);

const outputDarkThemePath = path.join(
  __dirname,
  "../webview-ui/theme/output-default-dark.json",
);
const outputLightThemePath = path.join(
  __dirname,
  "../webview-ui/theme/output-default-light.json",
);
function readThemeFile(filePath) {
  const fileContent = fs.readFileSync(filePath, "utf-8");
  // 需要处理下 待注释的 color 属性，不然不是全量的 token
  const json = eval("(" + fileContent.replace(/\/\/\"/g, "\"") + ")");

  // 需要把 jons.colors 里的 不合法 值过滤 比如  "activityBar.activeBackground": null,
  const res = {
    ...json,
    colors: Object.keys(json.colors).reduce((acc, key) => {
      const value = json.colors[key];
      if (value !== null) {
        acc[key] = value;
      }
      return acc;
    }, {}),
  };

  return res;
}

function generateCSSVariables(theme) {
  const colors = theme.colors || {};
  const res = {};
  for (const [key, value] of Object.entries(colors)) {
    let newKey = key.replace(/\./g, "-");
    newKey = `--vscode-${newKey}`;
    res[newKey] = value;
  }
  return res;
}

function writeCSSVariablesToTheme(cssVariables, themeClass) {
  const cssContent = `.${themeClass} {\n  ${cssVariables}\n}\n`;
}

const darkTheme = readThemeFile(darkThemePath);
const lightTheme = readThemeFile(lightThemePath);
darkTheme.name = "kwaipilot-default-theme-dark";
lightTheme.name = "kwaipilot-default-theme-light";

/** 设置默认主题名称写入新的文件 */
fs.writeFileSync(
  outputDarkThemePath,
  JSON.stringify(darkTheme, null, 2),
  "utf-8",
);
fs.writeFileSync(
  outputLightThemePath,
  JSON.stringify(lightTheme, null, 2),
  "utf-8",
);

const darkCSSVariables = generateCSSVariables(darkTheme);
const lightCSSVariables = generateCSSVariables(lightTheme);

function getCssContent(dark = {}, light = {}) {
  return `.default-theme.light {
  ${Object.entries(light)
    .map(([key, value]) => `${key}: ${value};`)
    .join("\n")}
}

.default-theme.dark {
   ${Object.entries(dark)
      .map(([key, value]) => `${key}: ${value};`)
      .join("\n")}
}
`;
}

fs.writeFileSync(
  cssOutputPath,
  getCssContent(darkCSSVariables, lightCSSVariables),
  "utf-8",
);

console.log("CSS variables generated and written to default-theme.css");
