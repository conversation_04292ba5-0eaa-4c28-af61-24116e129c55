const { exec } = require("child_process");
const pkg = require("../package.json");
const fs = require("fs");

const version = pkg.version;

const shouldExecute = require.main === module;

if (shouldExecute) {
  const args = process.argv.slice(2);
  let target;

  const isPreRelease = args.includes("--pre-release");

  const preReleaseFlag = isPreRelease ? " --pre-release" : "";

  if (args[1] === "--target") {
    target = args[2];
  }
  if (args[0] === "--target") {
    target = args[1];
  }

  if (!fs.existsSync("build")) {
    fs.mkdirSync("build");
  }

  let command = `npx vsce package --no-dependencies ${preReleaseFlag}`;
  if (target) {
    command += ` --target ${target}`;
  }

  exec(command, (error, stdout) => {
    if (error) {
      throw error;
    }
    console.log(stdout);
    console.log(
      `vsce package completed - extension created at kwaipilot-${target}-${version}.vsix ${isPreRelease ? "(pre-release)" : ""}`,
    );
  });
}
