// NOTE: 用于ide 产物编译，修改时需要注意是否同步./build.js
const { execSync } = require("child_process");
const os = require("os");
const fs = require("fs");
const path = require("path");

// 处理命令行参数
const args = process.argv.slice(2);
const isPreRelease = args.includes("--pre-release");
const preReleaseFlag = isPreRelease ? " --pre-release" : "";

/**
 * 获取当前系统的平台标识
 * @returns {string} 平台标识，例如 "darwin-arm64"
 */
function getCurrentPlatform() {
	const platform = os.platform();
	const arch = os.arch();
	const platformMap = {
		darwin: "darwin",
		win32: "win32",
		linux: "linux",
	};

	const archMap = {
		x64: "x64",
		arm64: "arm64",
	};

	const normalizedPlatform = platformMap[platform] || platform;
	const normalizedArch = archMap[arch] || arch;

	return `${normalizedPlatform}-${normalizedArch}`;
}

/**
 * 确保目录存在，如果不存在则创建
 * @param {string} dirPath 目录路径
 */
function ensureDirectoryExists(dirPath) {
	if (!fs.existsSync(dirPath)) {
		fs.mkdirSync(dirPath, { recursive: true });
		console.log(`✅ 创建目录: ${dirPath}`);
	}
}

/**
 * 检查源文件是否存在
 * @param {string} filePath 文件路径
 */
function checkFileExists(filePath) {
	if (!fs.existsSync(filePath)) {
		console.error(`❌ 错误: 文件不存在: ${filePath}`);
		process.exit(1);
	}
}

(async () => {
	console.log(`开始构建导出版本${isPreRelease ? " (预发布版)" : ""}`);

	// 构建扩展
	execSync(`npx tsup --config tsup-export.config.js`, {
		stdio: "inherit",
	});

	// 获取当前平台
	const currentPlatform = getCurrentPlatform();
	console.log(`\n✅ 当前系统平台: ${currentPlatform}`);

	// 源文件路径
	const sqliteSourcePath = path.resolve(
		path.join(__dirname, '..'),
		`bin/sqlite3/${currentPlatform}/Release/node_sqlite3.node`
	);

	// 检查源文件是否存在
	checkFileExists(sqliteSourcePath);

	// 目标目录路径 (扩展目录中的out)
	const targetDir = path.resolve(
		__dirname,
		"../../../../extensions/kwaipilot/build"
	);

	// 目标文件路径
	const targetPath = path.join(targetDir, "node_sqlite3.node");

	// 确保目标目录存在
	ensureDirectoryExists(targetDir);

	// 复制SQLite二进制文件到扩展目录
	fs.copyFileSync(sqliteSourcePath, targetPath);
	console.log(`✅ 已复制SQLite二进制文件: ${sqliteSourcePath} -> ${targetPath}`);

	console.log("\n✅ 导出构建完成!");
})().catch((err) => {
	console.error("构建过程中出错:", err);
	process.exit(1);
});
