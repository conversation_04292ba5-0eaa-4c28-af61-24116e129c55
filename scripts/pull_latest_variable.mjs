import { writeFileSync } from "fs";
import { dirname, join } from 'node:path';
import { fileURLToPath } from 'node:url';
import _ from 'lodash'

const __dirname = dirname(fileURLToPath(import.meta.url));

// @IMP: 当前我们自研的ide 是这个版本，千万不要更改千万不要更改千万不要更改千万不要更改 （除非我们的ide 升级了vscode 版本，不然你会找不到对应的token）
const vscodeVersion='1.99.2';

async function main() {
  const res = await fetch(`https://raw.githubusercontent.com/microsoft/vscode/${vscodeVersion}/build/lib/stylelint/vscode-known-variables.json`).then(res => res.text());
  /**
   * "colors": [
    "--vscode-actionBar-toggledBackground",
    "--vscode-activityBar-activeBackground",
    "--vscode-activityBar-activeBorder",
    "--vscode-activityBar-activeFocusBorder",
   */
  const identifiersText = JSON.parse(res).colors
    .filter(v => v !== /* 特殊变量过滤 这个变量标配了 */'--vscode-quickInput-list-focusBackground')
    .map(cssVar => {
      if (!cssVar.startsWith('--vscode-')) {
        throw new Error(`${cssVar} 不符合规范`);
      }
      const varName = _.camelCase(cssVar.replace('--vscode-', ''));
      return `export const ${varName} = "${cssVar}";`
    }).join('\n\n') + '\n'
  const identifiersJSON = JSON.parse(res).colors
    .filter(v => v !== /* 特殊变量过滤 这个变量标配了 */'--vscode-quickInput-list-focusBackground')
    .reduce((acc, cssVar) => {
      if (!cssVar.startsWith('--vscode-')) {
        // throw new Error(`${cssVar} 不符合规范`);
      }
      // "font-family": "var(--vscode-font-family)",
      const key = cssVar.replace('--vscode-', '')
      acc[key] = `var(${cssVar})`
      return acc
    }, {});
  const absPath = join(__dirname, '../', 'packages/shared/src/vscodeToken/vscode-known-variables.json')
  const identifiersFilePath = join(__dirname, '../', 'packages/shared/src/vscodeToken/vscode-identifiers.ts')
  const identifiersJSONFilePath = join(__dirname, '../', 'packages/shared/src/vscodeToken/vscode-identifiers.json');
  writeFileSync(absPath, res);
  writeFileSync(identifiersFilePath, identifiersText)
  writeFileSync(identifiersJSONFilePath, JSON.stringify(identifiersJSON, null, 2))
  console.log('已写入');
  console.log(absPath);
  console.log(identifiersFilePath);
}


main();
