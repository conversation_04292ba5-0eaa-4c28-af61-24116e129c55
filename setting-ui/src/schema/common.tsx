import { Config } from "shared/lib/state-manager/types";
import { kwaiPilotBridgeAPI } from "../bridge";
import { userSettingsStore } from "../store/useSettingsStore";
import { MaxFileIndexSpace } from "../components/FileIndexMaxSpace";
import { FileIndexManualBuild } from "../components/FileIndexManualBuild";
import { AutoRun } from "@/components/AutoRun";

const openUserRule = () => {
  kwaiPilotBridgeAPI.extensionRules.$openUserRule();
};
const openProjectRules = () => {
  kwaiPilotBridgeAPI.extensionRules.$openProjectRules();
};

export type SettingHost = "ide" | "plugin";

export interface SchemaItemBase<T> {
  title?: string;
  type: T;
  description?: string;
  default?: any;
  markdownDescription?: string | Array<string>;
  level?: number;
  host?: Array<SettingHost>;// 默认['plugin', 'ide']
  when?: string;
  renderRight?: (value?: ReturnType<typeof userSettingsStore>) => JSX.Element | null;// 自定义渲染右侧部分
  render?: (value?: ReturnType<typeof userSettingsStore>) => JSX.Element | null; // 自定义渲染整个模块
}

export interface AccountSchemaItem extends SchemaItemBase<"account"> {
  type: "account";
}

export interface ActionSchemaItem extends SchemaItemBase<"action"> {
  type: "action";
  actions: { title: string; action?: () => void }[];
}

export interface StringSchemaItem extends SchemaItemBase<"string"> {
  type: "string";
}

export interface NumberSchemaItem extends SchemaItemBase<"integer"> {
  type: "integer";
  min?: number;
  max?: number;
}

export interface BooleanSchemaItem extends SchemaItemBase<"boolean"> {
  type: "boolean";
}

export interface EnumSchemaItem extends SchemaItemBase<"enum"> {
  type: "enum";
  enum: Array<string | number>;
  enumItemLabels?: string[];
}

export interface LinkSchemaItem extends SchemaItemBase<"link"> {
  type: "link";
  url: string;
}

export type SchemaItem = AccountSchemaItem | ActionSchemaItem | StringSchemaItem | NumberSchemaItem | BooleanSchemaItem | EnumSchemaItem | LinkSchemaItem;

export type PropertyKey = keyof typeof Config | string;

export interface GroupSchemaItem {
  title: string;
  host?: Array<SettingHost>;// 默认plugin
  when?: string;
  properties: Record<PropertyKey, SchemaItem>;
}

const CommonSettings: Record<string, Record<string, GroupSchemaItem>> = {
  basic: {
    general: {
      title: "通用",
      properties: {
        account: {
          type: "account",
        },
        configMigration: {
          title: "配置迁移",
          type: "action",
          description: "导入 VS Code 或 Cursor 中的所有插件、设置、MCP以及快捷键配置到 Kwaipilot 中，请注意，导入后将覆盖当前配置，且不可恢复。",
          actions: [{
            title: "从VS Code迁移",
          }, {
            title: "从Cursor迁移",
          }],
          host: ["ide"],
        },
        ideSetting: {
          title: "IDE设置",
          description: "前往文本、工作台、窗口等IDE基础设置",
          type: "action",
          actions: [{
            title: "去设置",
          }],
          host: ["ide"],
        },
        shortcutSetting: {
          title: "快捷键设置",
          description: "前往IDE的快捷键自定义设置",
          type: "action",
          actions: [{
            title: "去设置",
          }],
          host: ["ide"],
        },
        language: {
          type: "enum",
          title: "语言",
          description: "配置kwaipilot应用的文本语言",
          default: "zh-CN",
          host: ["ide"],
          enumItemLabels: [
            "中文简体(默认)",
            "English",
          ],
          enum: [
            "zh-CN",
            "en",
          ],
        },
      },
    },
    other: {
      title: "其他",
      host: ["plugin"],
      properties: {
        [Config.PROXY_URL]: {
          type: "enum",
          enum: [
            "https://kwaipilot.corp.kuaishou.com",
            "https://pre-kinsight.test.gifshow.com",
            "https://qa-kinsight.staging.kuaishou.com",
            "https://kinsight.corp.kuaishou.com",
          ],
          enumItemLabels: [
            "生产环境(默认)",
            "预发环境(pre-kinsight)",
            "测试环境(qa-kinsight)",
            "IDC环境(kinsight)",
          ],
          title: "代理URL",
          description: "内部调试使用",
          default: "https://kwaipilot.corp.kuaishou.com",
        },
        [Config.MODEL_TYPE]: {
          type: "string",
          title: "推理模型",
          default: "",
          description: "内部调试使用",
        },
      },
    },
    about: {
      title: "关于",
      host: ["ide"],
      properties: {
        user: {
          type: "link",
          title: "用户协议",
          url: "https://www.kuaishou.com/agreement/user",
        },
        privacy: {
          type: "link",
          title: "隐私政策",
          url: "https://www.kuaishou.com/agreement/privacy",
        },
        openSource: {
          type: "link",
          title: "开源软件声明",
          url: "https://www.kuaishou.com/agreement/open-source",
        },
      },
    },
  },
  functions: {
    codeCompletion: {
      title: "代码续写",
      properties: {
        [Config.ENABLE]: {
          type: "boolean",
          default: true,
          title: "代码续写",
          description: "基于当前编辑行为智能生成多行代码编辑推荐",
        },
        [Config.COMMENT_COMPLETION_ENABLE]: {
          title: "注释默认续写",
          type: "boolean",
          description: "开启后注释内容也会出现续写提示",
          default: false,
          level: 2,
          when: "!!config.enable",
        },
        [Config.CODE_COMPLETION_DELAY]: {
          type: "integer",
          title: "续写等待时间",
          description: "在等待时间(ms)结束后才会出现代码续写提示",
          default: 75,
          level: 2,
          host: ["plugin"],
          when: "!!config.enable",
        },
        // [Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION]: {
        //   type: "enum",
        //   description: "代码续写长度",
        //   enum: [
        //     8,
        //     16,
        //     32,
        //     64,
        //     128,
        //     256,
        //     512,
        //   ],
        //   default: 32,
        //   level: 2,
        //   host: ["plugin"],
        //   when: "!!config.enable",
        // },
        [Config.PREDICTION_ENABLE]: {
          title: "启用代码编辑预测",
          host: ["plugin"],
          description: "基于当前编辑行为智能预测下一个改动点并生成多行代码编辑推荐",
          type: "boolean",
          default: true,
        },
      },
    },
    codeBlock: {
      title: "代码块操作",
      host: ["plugin"],
      properties: {
        [Config.ENABLE_CODE_BLOCK_ACTION]: {
          type: "boolean",
          default: false,
          title: "代码块操作",
          description: "启用代码块操作“函数注释”“行间注释”等功能展示",
        },
      },
    },
    inlineTip: {
      title: "智能体模式",
      properties: {
        [Config.AGENT_PREFERENCE]: {
          type: "enum",
          enum: [
            "intelligent",
            "speed",
          ],
          description: "智能优先：更强的推理和上下文理解能力，支持多模态输入，适合复杂或多步编辑的开发任务。速度优先：更快的响应速度，仅支持文本输入，适合快速验证或频繁迭代的开发任务。",
          enumItemLabels: [
            "智能优先",
            "速度优先",
          ],
          title: "智能体模式偏好",
          default: "intelligent",
        },
        [Config.ENABLE_DIAGNOSTICS_CHECK]: {
          type: "boolean",
          title: "自动检测lint问题",
          description: "开启后，在智能体模式下将检测生成代码的lint错误和一键修复建议",
          default: true,
        },
        autoRun: {
          render() {
            return (
              <AutoRun />
            );
          },
          type: "action",
          title: "",
          actions: [],
        },
      },
    },
  },
  fileIndex: {
    fileIndex: {
      title: "代码索引",
      properties: {
        manual: {
          render() {
            return <FileIndexManualBuild />;
          },
          type: "action",
          title: "",
          description: "构建仓库代码的全局索引，当发起智能体会话时将自动检索问题相关上下文，提升代码问答准确性",
          actions: [{
            title: "开始构建",
          }],
        },
        [Config.ENABLE_LOCAL_AGENT]: {
          type: "boolean",
          title: "自动构建索引",
          description: "开启后新开项目将自动开始构建",
          default: true,
        },
        ignoreFolder: {
          type: "action",
          title: "忽略/指定文件目录",
          description: "配置构建代码索引时忽略或指定的文件目录",
          actions: [{
            title: "前往配置",
            action: () => kwaiPilotBridgeAPI.extensionIndexFile.$openIndexIgnore(),
          }],
        },
        maxSpaceSize: {
          type: "integer",
          title: "最大索引空间大小(单位GB)",
          renderRight: () => {
            return <MaxFileIndexSpace />;
          },
        },
      },
    },
  },
  rules: {
    rules: {
      title: "规则配置",
      properties: {
        personRule: {
          type: "action",
          title: "个人规则",
          description: "在此文件中配置用户习惯后，Kwaipilot在问答模式及智能体模式的所有对话场景中均遵循设定规则，且跨项目切换时持续生效。",
          actions: [{
            title: "打开",
            action: openUserRule,
          }],
        },
        projectRule: {
          type: "action",
          title: "项目规则",
          description: "配置项目内使用的规则，在当前项目的问答模式与智能体模式的会话中生效，可在当前工作区的.kwaipilot/rules文件夹查看当前项目内的所有规则。",
          actions: [{
            title: "打开",
            action: openProjectRules,
          }],
        },
      },
    },
  },
};

export default CommonSettings;

export const setSettingSchemaHidden = (keyPath: string[]) => {
  let value: any;
  keyPath.forEach((key) => {
    value = value[key];
  });

  if (typeof value === "object" && value !== null) {
    value.hidden = true;
  }
};

export const titleClassName = "text-[22px] font-medium leading-[51px] text-[var(--vscode-foreground)]";
export const propertyTitleClassName = "text-[var(--vscode-foreground)] text-[13px] leading-[18px]";
export const descriptionClassName = "flex align-center text-[13px] leading-[18px] color-[var(--vscode-button-secondaryForeground)] opacity-50";
export const propertyPanelClassName = "bg-[var(--vscode-settings-rowHoverBackground)] rounded-[8px] p-[16px] flex flex-col";
export const borderClassName = "border-b border-[var(--vscode-editorGroup-border)] ";
