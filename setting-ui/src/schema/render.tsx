import { Button, NumberInput, NumberInputField, Input, Switch } from "@chakra-ui/react";
import { GroupSchemaItem, SchemaItem, SettingHost, propertyTitleClassName, descriptionClassName, titleClassName, propertyPanelClassName, borderClassName } from "./common";
import { RenderAccount } from "../components/Account";
import { Icon } from "@iconify/react";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { StateReturnType } from "shared/lib/state-manager/types";
import ChakraSelect from "../components/ChakraSelect";

const SettingComponent = (props: {
  propKey: keyof StateReturnType["config"]; propValue: SchemaItem;
  value: any;
}) => {
  const { propKey, propValue, value } = props;
  if (propValue.type === "boolean") {
    return (
      <Switch isChecked={value} onChange={() => kwaiPilotBridgeAPI.extensionSettings.$updateSetting(propKey, !value)}>
      </Switch>
    );
  }
  else if (propValue.type === "integer") {
    return (
      <NumberInput value={value} min={propValue.min} max={propValue.max} onChange={(_, valueAsNumber) => kwaiPilotBridgeAPI.extensionSettings.$updateSetting(propKey, valueAsNumber)}>
        <NumberInputField />
      </NumberInput>
    );
  }
  else if (propValue.type === "string") {
    return (
      <Input value={value} onChange={e => kwaiPilotBridgeAPI.extensionSettings.$updateSetting(propKey, e.target.value)} />
    );
  }
  else if (propValue.type === "enum") {
    return (
      <ChakraSelect value={value} options={propValue.enum} optionLabels={propValue.enumItemLabels} onChange={v => kwaiPilotBridgeAPI.extensionSettings.$updateSetting(propKey, v)} className="w-full text-left !px-[8px] !text-[var(--vscode-foreground)]" />
    );
  }
  else if (propValue.type === "action") {
    return (
      <div className="flex items-center justify-end gap-[12px]">
        {propValue.actions.map((action, index) => (<Button key={index} onClick={action.action}>{action.title}</Button>))}
      </div>
    );
  }
  return null;
};
export const RenderSettings = (props: {
  settings: Record<string, GroupSchemaItem>; host: SettingHost;
  settingValue?: Record<string, any>;
}) => {
  const { settings, host, settingValue } = props;
  return Object.entries(settings).map(([key, value]) => {
    const setting = value as GroupSchemaItem;
    if (value.host && !value.host.includes(host)) {
      return null;
    }
    if (value.when) {
      const whenCondition = new Function("config", `return ${value.when};`);
      if (!whenCondition(settingValue)) {
        return null;
      }
    }

    const visibleProperties = setting.properties && Object.entries(setting.properties).filter(([propKey, propValue]) => {
      const schemaPropValue = propValue as SchemaItem;
      if (schemaPropValue.host && !schemaPropValue.host.includes(host)) {
        return false;
      }
      if (schemaPropValue.when) {
        const whenCondition = new Function("config", `return ${schemaPropValue.when};`);
        if (!whenCondition(settingValue)) {
          return false;
        }
      }
      return true;
    });

    return (
      <div key={key} className="mb-[24px]">
        {setting.title
          ? (
              <div className={titleClassName}>
                {setting.title}
              </div>
            )
          : null}
        <div className={propertyPanelClassName}>
          {visibleProperties.map(
            ([propKey, propValue], index) => {
              const schemaPropValue = propValue as SchemaItem;
              if (schemaPropValue.render) {
                return (
                  <div key={propKey}>
                    { schemaPropValue.render(settingValue)}
                  </div>
                );
              }

              if (schemaPropValue.type === "account") {
                return <RenderAccount key={propKey} />;
              }
              let className = "flex items-center justify-between  gap-[40px] relative";
              if (index < visibleProperties.length - 1) {
                className += (" " + borderClassName + " pb-[16px]");
              }
              if (index > 0) {
                className += " pt-[16px]";
              }
              if (schemaPropValue.level && schemaPropValue.level > 1) {
                className += ` ml-[${(schemaPropValue.level - 1) * 20}px] ml-[20px]`;
              }
              return (
                <div className={className} key={propKey}>
                  <div className={"flex flex-col gap-[4px] " + propertyTitleClassName}>
                    {schemaPropValue.title
                      ? (
                          <div onClick={() => propValue.type === "link" ? kwaiPilotBridgeAPI.openUrl(propValue.url) : null} className={`flex items-center gap-[4px]  ${propValue.type === "link" ? "cursor-pointer" : ""}`}>
                            { schemaPropValue.title}
                            { propValue.type === "link" ? <Icon icon="mdi:open-in-new" className="text-[16px] text-[var(--vscode-foreground)]" /> : null }
                          </div>
                        )
                      : null}
                    { schemaPropValue.description
                      ? (
                          <div className={descriptionClassName}>
                            { schemaPropValue.description }
                          </div>
                        )
                      : null}
                  </div>
                  <div className="w-[246px] text-right flex-shrink-0">
                    {propValue.renderRight ? propValue.renderRight(settingValue) : <SettingComponent propKey={propKey as keyof StateReturnType["config"]} propValue={schemaPropValue} value={settingValue?.[propKey] ?? schemaPropValue.default} key={propKey} />}
                  </div>
                </div>
              );
            })}
        </div>
      </div>
    );
  });
};
