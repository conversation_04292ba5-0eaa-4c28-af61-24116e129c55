import { kwaiPilotBridgeAPI } from "@/bridge";
import { useEffect, useState } from "react";
import { NumberInput, NumberInputField, NumberDecrementStepper, NumberInputStepper, NumberIncrementStepper } from "@chakra-ui/react";
import { MAX_INDEX_SPACE_MIN, DEFAULT_INDEX_SPACE } from "shared/lib/const/index";

export const MaxFileIndexSpace = () => {
  const [maxIndexSpace, setMaxIndexSpace] = useState<number>(DEFAULT_INDEX_SPACE);

  // 只负责获取和设置最大索引空间大小
  useEffect(() => {
    const fetchMaxSpaceSize = async () => {
      const value = await kwaiPilotBridgeAPI.extensionIndexFile.$getMaxSpaceSize();
      setMaxIndexSpace(value);
    };

    fetchMaxSpaceSize();
  }, []);

  const handleMaxIndexSpaceChange = (value: number) => {
    if (value < MAX_INDEX_SPACE_MIN || !value) {
      setMaxIndexSpace(maxIndexSpace);
      return;
    }
    setMaxIndexSpace(value);
    kwaiPilotBridgeAPI.extensionIndexFile.$setMaxSpaceSize(value);
  };

  return (
    <NumberInput
      value={String(maxIndexSpace)}
      min={MAX_INDEX_SPACE_MIN}
      onChange={(_, valueAsNumber) => handleMaxIndexSpaceChange(valueAsNumber)}
    >
      <NumberInputField />
      <NumberInputStepper>
        <NumberIncrementStepper />
        <NumberDecrementStepper />
      </NumberInputStepper>
    </NumberInput>
  );
};
