import { Icon } from "@iconify/react";
import { InlineLink } from "./InlineLink";
import { kwaiPilotBridgeAPI } from "@/bridge";
// import { kwaiPilotBridgeAPI } from "@/bridge";

const TipBlock = () => {
  return (
    <div className="pl-8 pt-6 pr-[62px]">
      <div className="p-3 flex items-center gap-2 bg-[var(--vscode-tab-inactiveBackground)] rounded-sm">
        <Icon icon="weui:error-filled" className="inline-block text-[#FFBB26]" />
        <div>
          目前 Kwaipilot 常用设置项暂未与VSCode默认设置分离，如需进行其他参数设置请打开
          {" "}
          <InlineLink onClick={() => {
            kwaiPilotBridgeAPI.extensionIndexFile.$openSystemSettings();
          }}
          >
            VSCode设置页
          </InlineLink>
        </div>
      </div>
    </div>
  );
};

export default TipBlock;
