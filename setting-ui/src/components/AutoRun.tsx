import { kwaiPilotBridgeAPI } from "@/bridge";
import { userSettingsStore } from "@/store/useSettingsStore";
import { Button, Input, Popover, PopoverContent, PopoverTrigger, Switch, Tag, TagLabel, useDisclosure } from "@chakra-ui/react";
import { Icon } from "@iconify/react";
import { FormEvent, useCallback, useState } from "react";
import { Config } from "shared/lib/state-manager/types";
import { vsCss } from "shared/lib/vscodeToken/index";

function AutoRunHeader() {
  const enableAutoRun = userSettingsStore(s => s[Config.COMPOSER_ENABLE_AUTO_RUN]);
  const { isOpen, onClose, onOpen } = useDisclosure({
    defaultIsOpen: false,
  });
  const onConfirm = useCallback(() => {
    kwaiPilotBridgeAPI.extensionSettings.$updateSetting(Config.COMPOSER_ENABLE_AUTO_RUN, true);
    onClose();
  }, [onClose]);
  const onSwitchClick = useCallback(() => {
    if (enableAutoRun) {
      kwaiPilotBridgeAPI.extensionSettings.$updateSetting(Config.COMPOSER_ENABLE_AUTO_RUN, false);
    }
  }, [enableAutoRun]);
  return (
    <div className=" flex items-center gap-2 justify-between">
      <div className="flex flex-col gap-1">
        <div className=" text-foreground text-[13px]">
          自动运行模式
        </div>
        <div className=" text-disabledForeground text-[13px]">
          开启后智能体将无需确认自动运行合理的工具，例如命令行执行和MCP执行等
        </div>
      </div>
      <div>
        <Popover
          isOpen={enableAutoRun ? false : isOpen}
          onClose={() => { !enableAutoRun && onClose(); }}
          onOpen={() => { !enableAutoRun && onOpen(); }}
          closeOnBlur={true}
          placement="top-end"

        >
          <PopoverTrigger>
            <button onClick={onSwitchClick}>
              <Switch
                isChecked={enableAutoRun}
                className=" pointer-events-none"
              >
              </Switch>
            </button>
          </PopoverTrigger>
          <PopoverContent width="370px" bgColor={vsCss.notificationsBackground} border="none" p={4} boxShadow="0px 4px 24px 0px #0000005C,0px -4px 8px 0px #0000003D">
            <div className=" flex items-center font-medium text-foreground text-[13px] gap-2">

              <Icon icon="codicon:info" width="16" height="16" />
              <span className="text-[13px]">
                切换为自动运行
              </span>
            </div>
            <div className="py-2 text-foreground text-[13px]">
              开启后，智能体将自动执行除黑名单外的命令，请注意可能的潜在安全风险
            </div>
            <div className="pt-2 flex items-center justify-end gap-2">
              <Button variant="secondary" height="28px" onClick={onClose}>取消</Button>
              <Button variant="blueSolid" height="28px" onClick={onConfirm}>确认</Button>
            </div>
          </PopoverContent>

        </Popover>
      </div>
    </div>
  );
}

function AutoRunBody() {
  const enableAutoRunMCP = userSettingsStore(s => s[Config.COMPOSER_ENABLE_AUTO_RUN_MCP]);
  const excludeCommands = userSettingsStore(s => s[Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE]);

  const [inputValue, setInputValue] = useState("");

  const doAdd = useCallback(() => {
    const value = inputValue.trim();
    if (!value) {
      return;
    }
    if (excludeCommands.includes(value)) {
      return;
    }
    kwaiPilotBridgeAPI.extensionSettings.$updateSetting(Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE, [...excludeCommands, value]);
    setInputValue("");
  }, [excludeCommands, inputValue]);

  const handleSubmit = useCallback((e: FormEvent) => {
    e.preventDefault();
    doAdd();
  }, [doAdd]);
  const handleAddButtonClick = useCallback(() => {
    doAdd();
  }, [doAdd]);

  const doDelete = useCallback((index: number) => {
    const newExcludeCommands = [...excludeCommands];
    newExcludeCommands.splice(index, 1);
    kwaiPilotBridgeAPI.extensionSettings.$updateSetting(Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE, newExcludeCommands);
  }, [excludeCommands]);
  return (
    <div className="text-[13px]">
      <div className=" flex items-center gap-2">
        <div>
          <div className=" text-[13px] text-foreground">
            命令黑名单
          </div>
          <div className=" text-disabledForeground">
            命令前缀在黑名单内的命令，将不会自动执行，始终会询问用户
          </div>
        </div>
        <form onSubmit={handleSubmit} className="ml-auto">
          <Input

            width="174px"
            value={inputValue}
            onChange={event => setInputValue(event.target.value)}
            placeholder="请输入名称"
            className=" placeholder:text-disabledForeground"
          >
          </Input>
        </form>
        <Button onClick={handleAddButtonClick}>
          添加
        </Button>
      </div>
      <div className="flex flex-wrap gap-2 mt-2">
        {excludeCommands.map((command, i) => (
          <Tag
            gap={1}
            key={command}
            size="sm"
            variant="outline"
            borderRadius="4px"
            boxShadow="none"
            borderColor={vsCss.checkboxBorder}
            borderWidth="1px"
            minHeight="22px"
          >
            <TagLabel color={vsCss.foreground}>
              {command}
            </TagLabel>
            <Icon cursor="pointer" className=" text-icon-foreground flex-none" icon="codicon:close" onClick={() => doDelete(i)} width="14" height="14" />
          </Tag>
        ))}
      </div>
      <div className="mt-2 pt-2 flex items-center gap-2 justify-between border-t border-widget-border border-solid">

        <div className="">
          <div className=" text-[13px] text-foreground">
            mcp自动运行
          </div>
          <div className=" text-disabledForeground">
            启用后，将允许智能体自动运行MCP
          </div>
        </div>
        <Switch
          isChecked={enableAutoRunMCP}
          onChange={() => {
            kwaiPilotBridgeAPI.extensionSettings.$updateSetting(Config.COMPOSER_ENABLE_AUTO_RUN_MCP, !enableAutoRunMCP);
          }}
        >
        </Switch>
      </div>
    </div>
  );
}

export function AutoRun() {
  const enableAutoRun = userSettingsStore(s => s[Config.COMPOSER_ENABLE_AUTO_RUN]);
  return (
    <div className="py-4 ">
      <AutoRunHeader />
      {enableAutoRun
        ? (
            <div className="pl-[10px]">
              <div className="pl-[10px] border-l mt-6  border-dashed border-commandCenter-inactiveBorder">
                <AutoRunBody />
              </div>
            </div>
          )
        : null}
    </div>
  );
}
