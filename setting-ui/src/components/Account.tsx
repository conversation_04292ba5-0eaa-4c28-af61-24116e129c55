import { Button } from "@chakra-ui/react";
import { kwaiPilotBridgeAPI } from "../bridge";
import { UserInfo } from "@shared/types";
import { useState, useEffect } from "react";
import { propertyTitleClassName, descriptionClassName } from "../schema/common";

export const RenderAccount = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);

  const logout = () => {
    kwaiPilotBridgeAPI.extensionSettings.$logout();
  };

  const login = () => {
    kwaiPilotBridgeAPI.extensionSettings.$login();
  };

  useEffect(() => {
    const subscription = kwaiPilotBridgeAPI.observableAPI.userInfo().subscribe((userInfo) => {
      setUserInfo(userInfo ?? null);
    });
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return (
    <div className="flex items-center justify-between">
      <div className={"flex flex-col gap-[4px]" + propertyTitleClassName}>
        <div>
          账号设置
        </div>
        <div className={"flex align-center" + descriptionClassName}>
          当前登录账号：
          {userInfo?.displayName ?? "未登录"}
        </div>
      </div>
      {userInfo
        ? <Button onClick={logout}>注销</Button>
        : <Button onClick={login}>登录</Button>}
    </div>
  );
};
