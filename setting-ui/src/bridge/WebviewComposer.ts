import { WebviewComposerShape } from "shared/lib/bridge/protocol";
import { ComposerState } from "shared/lib/agent";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";

export class WebviewComposer implements WebviewComposerShape {
  $addToComposerContext(_: MentionNodeV2Structure | null): void {
    throw new Error("Method not implemented.");
  }

  $postComposerStateUpdate(_state: ComposerState): void {
    throw new Error("Method not implemented.");
  }
}
