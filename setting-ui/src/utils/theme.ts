// theme.ts

// 1. import `extendTheme` function
import {
  extendTheme,
  type ThemeConfig,
} from "@chakra-ui/react";
import Button from "../theme/button";
import Select from "../theme/select";
import Input from "../theme/input";
import NumberInput from "../theme/number-input";
import Switch from "../theme/switch";
import Menu from "../theme/menu";

// 2. Add your color mode config
const config: ThemeConfig = {
  initialColorMode: "dark",
  useSystemColorMode: false,
};

// 3. extend the theme
const theme = extendTheme({
  config,
  styles: {
    global: {
      body: {
        color: "none",
        padding: 0,
        backgroundColor: "var(--vscode-editor-background)",
      },
    },
  },
  components: {
    Button,
    Select,
    Input,
    NumberInput,
    Switch,
    Menu,
  },
});

export default theme;
