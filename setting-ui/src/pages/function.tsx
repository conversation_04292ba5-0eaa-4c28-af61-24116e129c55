import Settings from "../schema/common";
import { RenderSettings } from "../schema/render";
import { userSettingsStore } from "../store/useSettingsStore";
import { getCurrentEnvIsInIDE } from "@/utils/ide";

/** 功能设置页面 */
export const FunctionSetting = () => {
  const settingValue = userSettingsStore(state => state);
  const host = getCurrentEnvIsInIDE() ? "ide" : "plugin";

  return (
    <RenderSettings settings={Settings.functions} host={host}settingValue={settingValue}></RenderSettings>
  );
};
