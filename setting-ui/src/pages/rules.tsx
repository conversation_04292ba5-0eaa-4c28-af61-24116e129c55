import Settings from "../schema/common";
import { RenderSettings } from "../schema/render";
import { userSettingsStore } from "../store/useSettingsStore";
import { getCurrentEnvIsInIDE } from "@/utils/ide";

/** 基础设置页面 */
export const Rules = () => {
  const settingValue = userSettingsStore(state => state);
  const host = getCurrentEnvIsInIDE() ? "ide" : "plugin";

  return (
    <RenderSettings settings={Settings.rules} host={host} settingValue={settingValue}></RenderSettings>
  );
};
