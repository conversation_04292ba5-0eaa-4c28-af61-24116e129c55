import { kwai<PERSON>ilotBridgeAPI } from "@/bridge";
import { InlineLink } from "../components/InlineLink";
import { useEffect, useMemo, useState } from "react";
import { Icon } from "@iconify/react";
import { SettingDesc } from "../components/SettingDesc";
import { Button, NumberInput, NumberInputField, NumberDecrementStepper, NumberInputStepper, NumberIncrementStepper, Switch } from "@chakra-ui/react";
import { userSettingsStore } from "../store/useSettingsStore";
import { Config } from "shared/lib/state-manager/types";
import { MAX_INDEX_SPACE_MIN, DEFAULT_INDEX_SPACE } from "shared/lib/const/index";
import { IndexState } from "shared";
import { propertyTitleClassName, descriptionClassName, borderClassName, propertyPanelClassName } from "../schema/common";

export const FileIndex = () => {
  const [progress, setProgress] = useState(0);
  const [isStarted, setIsStarted] = useState(false);
  const buildFinish = useMemo(() => progress >= 1, [progress]);
  const [message, setMessage] = useState("");
  const [maxIndexSpace, setMaxIndexSpace] = useState<number>(DEFAULT_INDEX_SPACE);
  const [paused, setPaused] = useState(false);
  const [isRepo, setIsRepo] = useState(true);
  const [buildStatus, setBuildStatus] = useState<IndexState["status"]>("paused");
  // progress保留到小数点后两位
  const showProgress = useMemo(() => (progress * 100).toFixed(0), [progress]);
  const [lastBuildTime, setLastBuildTime] = useState("");
  const enableLocalAgent = userSettingsStore(state => state[Config.ENABLE_LOCAL_AGENT]);

  const startBuildIndex = async () => {
    if (!isRepo) {
      return;
    }
    await kwaiPilotBridgeAPI.extensionIndexFile.$startBuildIndex();
  };
  const deleteIndex = async () => {
    await kwaiPilotBridgeAPI.extensionIndexFile.$deleteIndex();
  };
  const stopIndexBuild = async () => {
    await kwaiPilotBridgeAPI.extensionIndexFile.$stopIndex();
  };

  // 只负责获取和设置最大索引空间大小
  useEffect(() => {
    const fetchMaxSpaceSize = async () => {
      const value = await kwaiPilotBridgeAPI.extensionIndexFile.$getMaxSpaceSize();
      setMaxIndexSpace(value);
    };

    fetchMaxSpaceSize();
  }, []);

  useEffect(() => {
    const sus = kwaiPilotBridgeAPI.observableAPI.indexState().subscribe((state) => {
      setProgress(state.indexingProgress);
      setIsStarted(state.indexing);
      setLastBuildTime(state.lastBuildTime);
      setMessage(state.indexingMessage);
      setPaused(state.pauseIndexManual);
      setBuildStatus(state.status);
    });

    return () => {
      sus.unsubscribe();
    };
  }, []);

  useEffect(() => {
    const fetchIsRepo = async () => {
      const isRepo = await kwaiPilotBridgeAPI.extensionIndexFile.$getIsRepo();
      if (!isRepo) {
        setBuildStatus("error");
      }
      setIsRepo(isRepo);
    };

    fetchIsRepo();
  }, []);

  const handleMaxIndexSpaceChange = (value: number) => {
    if (value < MAX_INDEX_SPACE_MIN || !value) {
      setMaxIndexSpace(maxIndexSpace);
      return;
    }
    setMaxIndexSpace(value);
    kwaiPilotBridgeAPI.extensionIndexFile.$setMaxSpaceSize(value);
  };

  return (
    <>
      <div className="text-[22px]  font-medium flex-shrink-0 leading-[51px] text-[var(--vscode-foreground)]">
        代码索引管理
      </div>
      <div className={propertyPanelClassName}>
        <div className={"pb-[16px] " + borderClassName}>
          <div className="flex justify-between gap-[40px]">
            <div>
              <div className={propertyTitleClassName}>自动构建索引</div>
              <div className={descriptionClassName}>
                开启后新开项目将自动开始构建
              </div>
            </div>
            <div className="flex gap-3 h-[34px]">
              <Switch isChecked={enableLocalAgent} onChange={() => kwaiPilotBridgeAPI.extensionSettings.$updateSetting(Config.ENABLE_LOCAL_AGENT, !enableLocalAgent)} />
            </div>
          </div>
        </div>
        <div className={"py-[16px] " + borderClassName}>
          <div className="flex justify-between gap-[40px]">
            <div>
              <div className={propertyTitleClassName}>手动构建索引</div>
              <div className={descriptionClassName}>
                构建仓库代码的全局索引，当发起智能体会话时将自动检索问题相关上下文，提升代码问答准确性
              </div>
            </div>
            <div className="flex gap-3 h-[34px]">
              {!isStarted && !buildFinish && (
                <Button
                  disabled={!isRepo}
                  variant="blueSolid"
                  onClick={
                    startBuildIndex
                  }
                >
                  开始构建
                </Button>
              )}
              { isStarted && !buildFinish
              && (
                <Button
                  onClick={stopIndexBuild}
                  variant="blueSolid"
                  leftIcon={<Icon icon="codicon:close"></Icon>}
                >
                  取消构建
                </Button>
              )}
              {buildFinish && (
                <Button onClick={startBuildIndex} leftIcon={<Icon icon="codicon:debug-restart"></Icon>}>
                  重新构建
                </Button>
              )}
              {buildFinish && (
                <Button
                  onClick={deleteIndex}
                  leftIcon={<Icon icon="material-symbols:delete-outline"></Icon>}
                >
                  删除索引
                </Button>
              )}
            </div>
          </div>
          <div className="pt-4 pb-2">
            <div className="w-full h-1.5 bg-[var(--vscode-tab-inactiveBackground)] rounded-[3px]">
              <div className={`w-full h-full  rounded-[3px] ${buildFinish ? "bg-[#00C2A5ff]" : "bg-[var(--vscode-progressBar-background)]"}`} style={{ width: `${showProgress}%` }}></div>
            </div>
          </div>
          <div className="flex justify-between gap-3">
            <SettingDesc>
              {
                buildStatus === "error"
                  ? <Icon icon="ix:error-filled" className="text-[#E35151]" />
                  : buildStatus === "indexed"
                    ? <Icon icon="mdi:success-circle" className="text-[#00C2A5ff]"></Icon>
                    : ""
              }
              {!isRepo
                ? "不是git仓库"
                : paused
                  ? "已暂停索引"
                  : message
                    ? <span title={message} className="inline-block whitespace-nowrap overflow-hidden text-ellipsis">{message}</span>
                    : buildFinish
                      ? `构建成功 ${lastBuildTime}`
                      : isStarted ? "构建索引中..." : "当前未构建索引"}
            </SettingDesc>
            <div>
              {showProgress}
              %
            </div>
          </div>
        </div>
        <div className={"py-[16px] " + borderClassName}>
          <div className={propertyTitleClassName}>忽略 / 指定文件目录</div>
          <div className="flex items-center gap-[4px]">
            <div className={descriptionClassName}>
              配置构建代码索引时忽略 / 指定的文件目录
            </div>
            <InlineLink
              onClick={() => {
                kwaiPilotBridgeAPI.extensionIndexFile.$openIndexIgnore();
              }}
            >
              前往配置
            </InlineLink>
          </div>
        </div>

        <div className="flex flex-col gap-2  pt-[16px]">
          <div className={propertyTitleClassName}>最大索引空间大小（单位：GB）</div>
          <NumberInput
            value={String(maxIndexSpace)}
            min={MAX_INDEX_SPACE_MIN}
            onChange={(_, valueAsNumber) => handleMaxIndexSpaceChange(valueAsNumber)}
          >
            <NumberInputField />
            <NumberInputStepper>
              <NumberIncrementStepper />
              <NumberDecrementStepper />
            </NumberInputStepper>
          </NumberInput>
        </div>
      </div>
    </>
  );
};
